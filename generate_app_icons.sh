#!/bin/bash

# Script to generate all required App Store icons with white backgrounds
# Input: Joking/logo/Joking_logo.png
# Output: All required icon sizes in Joking/Assets.xcassets/AppIcon.appiconset/

SOURCE_LOGO="Joking/logo/Joking_logo.png"
ICON_DIR="Joking/Assets.xcassets/AppIcon.appiconset"

# Check if source logo exists
if [ ! -f "$SOURCE_LOGO" ]; then
    echo "❌ Error: Source logo not found at $SOURCE_LOGO"
    exit 1
fi

echo "🎨 Generating App Store icons with white backgrounds..."
echo "📁 Source: $SOURCE_LOGO"
echo "📁 Output: $ICON_DIR"

# Create output directory if it doesn't exist
mkdir -p "$ICON_DIR"

# Function to generate icon with white background
generate_icon() {
    local size=$1
    local filename=$2
    
    echo "  📱 Generating ${filename} (${size}x${size})"
    
    # Create icon with white background using sips
    sips -z $size $size "$SOURCE_LOGO" --out "/tmp/temp_icon.png" > /dev/null 2>&1
    
    # Create white background and composite
    sips -c $size $size --setProperty format png --setProperty formatOptions normal \
         -s format png \
         --padToHeightWidth $size $size --padColor FFFFFF \
         "/tmp/temp_icon.png" --out "$ICON_DIR/$filename" > /dev/null 2>&1
    
    # Alternative method using ImageMagick if available
    if command -v convert &> /dev/null; then
        convert -size ${size}x${size} xc:white \
                \( "$SOURCE_LOGO" -resize ${size}x${size} \) \
                -gravity center -composite \
                "$ICON_DIR/$filename"
    fi
}

# Generate all required icon sizes for iOS App Store
echo "📱 Generating iPhone icons..."
generate_icon 40 "<EMAIL>"      # 20pt @2x
generate_icon 60 "<EMAIL>"      # 20pt @3x
generate_icon 58 "<EMAIL>"      # 29pt @2x
generate_icon 87 "<EMAIL>"      # 29pt @3x
generate_icon 80 "<EMAIL>"      # 40pt @2x
generate_icon 120 "<EMAIL>"     # 40pt @3x
generate_icon 120 "<EMAIL>"     # 60pt @2x
generate_icon 180 "<EMAIL>"     # 60pt @3x

echo "📱 Generating iPad icons..."
generate_icon 20 "icon-20.png"         # 20pt @1x
generate_icon 40 "<EMAIL>"    # 20pt @2x (iPad)
generate_icon 29 "icon-29.png"         # 29pt @1x
generate_icon 58 "<EMAIL>"    # 29pt @2x (iPad)
generate_icon 40 "icon-40.png"         # 40pt @1x
generate_icon 80 "<EMAIL>"    # 40pt @2x (iPad)
generate_icon 76 "icon-76.png"         # 76pt @1x
generate_icon 152 "<EMAIL>"     # 76pt @2x
generate_icon 167 "<EMAIL>"   # 83.5pt @2x

echo "🏪 Generating App Store icon..."
generate_icon 1024 "icon-1024.png"     # App Store 1024x1024

# Clean up temp file
rm -f "/tmp/temp_icon.png"

echo "✅ All icons generated successfully!"
echo "📋 Generated icons:"
ls -la "$ICON_DIR"/*.png | awk '{print "   " $9 " (" $5 " bytes)"}'

echo ""
echo "🎯 Next steps:"
echo "1. Open Xcode and verify all icons appear correctly"
echo "2. Build and test the app"
echo "3. Submit to App Store Connect"
