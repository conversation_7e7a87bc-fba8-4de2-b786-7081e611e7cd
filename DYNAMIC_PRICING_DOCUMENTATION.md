# Dynamic Localized Pricing Documentation v3.0

## Overview

HeartStarter implements a professional dynamic localized pricing system using Apple's StoreKit 2 framework to provide world-class international user experience. This system automatically displays prices in users' local currencies with proper formatting and regional compliance.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Implementation Details](#implementation-details)
3. [Code Structure](#code-structure)
4. [Regional Pricing Examples](#regional-pricing-examples)
5. [Testing and Development](#testing-and-development)
6. [App Store Connect Setup](#app-store-connect-setup)
7. [Troubleshooting](#troubleshooting)

## Architecture Overview

### **Core Components**

1. **Product Extension** (`SubscriptionManager.swift`)
   - Computed properties for localized pricing
   - Dynamic calculations for savings and equivalents
   - Professional fallback handling

2. **SubscriptionManager Helper Methods**
   - Centralized pricing logic
   - UI-friendly helper functions
   - Cache and loading state management

3. **UI Integration**
   - Dynamic pricing across all screens
   - Real-time updates from StoreKit
   - Graceful loading states

### **Key Benefits**

- ✅ **Automatic Localization**: No manual currency conversion required
- ✅ **Professional Presentation**: Native formatting for each region
- ✅ **Legal Compliance**: Regional tax handling by Apple
- ✅ **Real-time Updates**: Exchange rates managed by Apple
- ✅ **Zero Maintenance**: No currency rate updates needed

## Implementation Details

### **1. Product Extension (Core Foundation)**

```swift
// MARK: - Product Extension for Localized Pricing
extension Product {
    /// Returns the billing period as a user-friendly string
    var displayPeriod: String {
        if id.contains("weekly") {
            return "week"
        } else if id.contains("yearly") {
            return "year"
        }
        return "period"
    }
    
    /// Returns the weekly equivalent price for yearly subscriptions
    var displayWeeklyPrice: String {
        if id.contains("yearly") {
            // Calculate weekly price for yearly subscription
            let yearlyPrice = self.price
            let weeklyPrice = yearlyPrice / Decimal(52)
            
            let formatter = NumberFormatter()
            formatter.numberStyle = .currency
            formatter.locale = self.priceFormatStyle.locale
            
            return formatter.string(from: weeklyPrice as NSDecimalNumber) ?? displayPrice
        }
        return displayPrice
    }
    
    /// Returns savings percentage for yearly vs weekly plans
    func displaySavings(comparedTo weeklyProduct: Product?) -> String? {
        guard id.contains("yearly"),
              let weeklyProduct = weeklyProduct else { return nil }
        
        let yearlyWeeklyPrice = self.price / Decimal(52)
        let weeklyPrice = weeklyProduct.price
        
        guard weeklyPrice > 0 else { return nil }
        
        let savings = (weeklyPrice - yearlyWeeklyPrice) / weeklyPrice * 100
        let roundedSavings = Int(NSDecimalNumber(decimal: savings).doubleValue.rounded())
        
        return "Save \(roundedSavings)%"
    }
    
    /// Returns formatted price with period
    var displayPriceWithPeriod: String {
        return "\(displayPrice) per \(displayPeriod)"
    }
    
    /// Returns starting price text for UI display
    var displayStartingPrice: String {
        return "Starting at \(displayPrice)/\(displayPeriod)"
    }
}
```

### **2. SubscriptionManager Helper Methods**

```swift
// MARK: - Dynamic Pricing Helper Methods

/// Returns the lowest starting price for premium subscription
func getStartingPrice() -> String {
    let weeklyProduct = availableProducts.first { $0.id == ProductID.premiumWeekly.rawValue }
    return weeklyProduct?.displayStartingPrice ?? "Starting at $4.99/week"
}

/// Returns the weekly product
var weeklyProduct: Product? {
    return availableProducts.first { $0.id == ProductID.premiumWeekly.rawValue }
}

/// Returns the yearly product
var yearlyProduct: Product? {
    return availableProducts.first { $0.id == ProductID.premiumYearly.rawValue }
}

/// Returns formatted price with period for a specific product
func formattedPrice(for productID: ProductID) -> String {
    guard let product = availableProducts.first(where: { $0.id == productID.rawValue }) else {
        return "Loading..."
    }
    return product.displayPriceWithPeriod
}

/// Returns weekly equivalent price for yearly subscription
func weeklyEquivalentPrice() -> String {
    guard let yearlyProduct = yearlyProduct else {
        return "Loading..."
    }
    return yearlyProduct.displayWeeklyPrice
}

/// Returns savings text for yearly vs weekly
func savingsText() -> String? {
    guard let yearly = yearlyProduct,
          let weekly = weeklyProduct else { return nil }
    return yearly.displaySavings(comparedTo: weekly)
}

/// Returns free trial text with dynamic pricing
func freeTrialText() -> String {
    guard let weeklyProduct = weeklyProduct else {
        return "7-Day Free Trial • Starting at $4.99/week"
    }
    return "7-Day Free Trial • \(weeklyProduct.displayStartingPrice)"
}
```

### **3. UI Integration Pattern**

```swift
// Example: PremiumUpgradeCard.swift
struct PremiumUpgradeCard: View {
    let onUpgrade: () -> Void
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        Button(action: onUpgrade) {
            HStack(spacing: 16) {
                // Crown icon and content...
                
                Text(subscriptionManager.getStartingPrice())  // ← Dynamic pricing
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}
```

## Code Structure

### **Files Modified for Dynamic Pricing**

1. **SubscriptionManager.swift**
   - ✅ Added Product extension with computed properties
   - ✅ Added helper methods for UI integration
   - ✅ Removed hardcoded pricing constants

2. **PremiumUpgradeCard.swift**
   - ✅ Integrated SubscriptionManager dependency
   - ✅ Updated to use `getStartingPrice()`

3. **PlanDetailsView.swift**
   - ✅ Added SubscriptionManager to FreePlanContent
   - ✅ Updated free trial text to use `freeTrialText()`

4. **PremiumView.swift**
   - ✅ Updated PremiumPlanButton with real Product prices
   - ✅ Dynamic savings calculation with `savingsText()`
   - ✅ Real pricing display for weekly/yearly plans

5. **SubscriptionManagementView.swift**
   - ✅ Added StoreKit import
   - ✅ Updated plan comparison to use real Product prices

## Regional Pricing Examples

### **Production Pricing (Approximate)**

| Region | Weekly | Yearly | Weekly Equivalent | Savings |
|--------|--------|--------|-------------------|---------|
| 🇺🇸 United States | $4.99 | $119.00 | $2.29 | Save 54% |
| 🇳🇿 New Zealand | NZ$7.99 | NZ$189.00 | NZ$3.63 | Save 55% |
| 🇦🇺 Australia | A$7.49 | A$179.00 | A$3.44 | Save 54% |
| 🇬🇧 United Kingdom | £3.99 | £95.00 | £1.83 | Save 54% |
| 🇪🇺 European Union | €4.49 | €107.00 | €2.06 | Save 54% |
| 🇨🇦 Canada | CA$6.99 | CA$159.00 | CA$3.06 | Save 56% |
| 🇯🇵 Japan | ¥750 | ¥17,000 | ¥327 | Save 56% |

*Note: Actual prices set by Apple based on current exchange rates and regional policies*

### **Key Features**

- **Dynamic Savings**: Percentage calculated in real-time based on actual regional pricing
- **Proper Formatting**: Each region uses correct currency symbols and decimal formats
- **Tax Inclusive**: Regional taxes automatically included where required
- **Exchange Rate Updates**: Apple handles all currency conversions automatically

## Testing and Development

### **Development Environment Limitations**

⚠️ **Important**: In development, you may still see USD pricing even in other regions due to:

1. **iOS Simulator**: Always displays USD pricing regardless of region settings
2. **Development Builds**: May use test/USD pricing environment
3. **StoreKit Testing**: Local testing uses predefined test pricing

### **Testing Options**

#### **Option 1: Physical Device Regional Testing**

```bash
# On iPhone/iPad:
1. Settings → General → Language & Region
2. Change Region to target country (e.g., "New Zealand")
3. Restart HeartStarter app
4. Verify pricing updates to local currency
```

#### **Option 2: StoreKit Configuration File (Recommended)**

1. **Create StoreKit Configuration**:
   ```
   Xcode → File → New → File → StoreKit Configuration File
   ```

2. **Add Products with Regional Pricing**:
   ```
   - Product ID: com.newversion.HeartStarter.premium.weekly
   - Reference Name: Premium Weekly
   - Price: Set for multiple regions
   
   - Product ID: com.newversion.HeartStarter.premium.yearly  
   - Reference Name: Premium Yearly
   - Price: Set for multiple regions
   ```

3. **Test with Configuration**:
   ```
   Xcode → Edit Scheme → Run → Options → StoreKit Configuration
   Select your .storekit file
   ```

#### **Option 3: Debug Pricing Information**

Add this debug method to `SubscriptionManager.swift`:

```swift
func debugPricing() {
    print("=== PRICING DEBUG ===")
    print("Available products count: \(availableProducts.count)")
    for product in availableProducts {
        print("Product: \(product.id)")
        print("  Display Price: \(product.displayPrice)")
        print("  Raw Price: \(product.price)")
        print("  Locale: \(product.priceFormatStyle.locale)")
        print("  Currency: \(product.priceFormatStyle.currencyCode ?? "Unknown")")
    }
    print("===================")
}
```

Call in `viewDidAppear`:
```swift
// In ContentView or PremiumView
.onAppear {
    SubscriptionManager.shared.debugPricing()
}
```

### **Development vs Production**

| Environment | Pricing Source | Currency Display | Testing Method |
|-------------|----------------|------------------|----------------|
| **Simulator** | Test/USD | USD only | StoreKit Configuration |
| **Development Device** | Test/Mixed | May show USD | Region switching + restart |
| **TestFlight** | Production | Localized | Real regional testing |
| **App Store** | Production | Fully Localized | Live user testing |

## App Store Connect Setup

### **Required Configuration Steps**

#### **1. Base Pricing Setup**

1. **Navigate to App Store Connect**:
   ```
   App Store Connect → Apps → HeartStarter → Features → In-App Purchases
   ```

2. **Select Subscription Products**:
   ```
   - Premium Weekly (com.newversion.HeartStarter.premium.weekly)
   - Premium Yearly (com.newversion.HeartStarter.premium.yearly)
   ```

3. **Configure Pricing and Availability**:
   ```
   Base Territory: United States
   Base Price: $4.99 (weekly), $119.00 (yearly)
   ```

#### **2. Regional Pricing Configuration**

**Option A: Automatic Pricing (Recommended)**
```
✅ Let Apple calculate regional prices automatically
✅ Based on current exchange rates
✅ Includes regional tax requirements
✅ Updates automatically with exchange rate changes
```

**Option B: Manual Regional Pricing**
```
Set custom prices for specific territories:
- New Zealand: Manually set NZD pricing
- Australia: Manually set AUD pricing  
- United Kingdom: Manually set GBP pricing
- European Union: Manually set EUR pricing
```

#### **3. Territory Selection**

Ensure your app is available in target markets:
```
App Store Connect → Apps → HeartStarter → Pricing and Availability
Select territories where dynamic pricing should apply
```

#### **4. Submission and Review**

```
1. Save pricing configuration
2. Submit In-App Purchases for review
3. Submit app update if needed
4. Wait for Apple approval (24-48 hours typically)
```

### **Verification Checklist**

- ✅ Base USD pricing configured
- ✅ Regional territories selected
- ✅ Automatic or manual regional pricing set
- ✅ In-App Purchases submitted for review
- ✅ App Store listing updated if needed

## Troubleshooting

### **Common Issues and Solutions**

#### **Issue 1: Still Seeing USD Prices in Development**

**Cause**: Development environment limitations
**Solution**: 
```swift
// This is expected behavior in development
// Test using:
1. StoreKit Configuration File
2. TestFlight builds
3. Production App Store release
```

#### **Issue 2: "Loading..." Displayed Instead of Prices**

**Cause**: StoreKit products not loaded
**Solution**:
```swift
// Ensure products are loaded before displaying UI
await subscriptionManager.loadProducts()

// Add loading states to UI
if subscriptionManager.availableProducts.isEmpty {
    ProgressView("Loading pricing...")
} else {
    // Display pricing UI
}
```

#### **Issue 3: Products Not Available**

**Cause**: App Store Connect configuration issues
**Solution**:
```swift
// Check product IDs match exactly
enum ProductID: String, CaseIterable {
    case premiumWeekly = "com.newversion.HeartStarter.premium.weekly"
    case premiumYearly = "com.newversion.HeartStarter.premium.yearly"
}

// Verify in App Store Connect:
1. Product IDs are exactly the same
2. Products are in "Ready to Submit" or "Approved" status
3. Pricing is configured for base territory
```

#### **Issue 4: Wrong Currency/Locale**

**Cause**: Device region settings or StoreKit configuration
**Solution**:
```swift
// Debug locale information
print("Device locale: \(Locale.current)")
print("Product locale: \(product.priceFormatStyle.locale)")

// Verify device settings:
Settings → General → Language & Region → Region
```

#### **Issue 5: Savings Percentage Incorrect**

**Cause**: Calculation based on different regional pricing
**Solution**:
```swift
// Savings are calculated dynamically per region
// This is correct behavior - percentages may vary by region
// due to different base pricing and exchange rates

// Example:
// US: $4.99 weekly, $119 yearly = 54% savings
// EU: €4.49 weekly, €107 yearly = 53% savings (due to exchange rates)
```

### **Debug Commands**

Add these debug methods for troubleshooting:

```swift
// In SubscriptionManager.swift
func debugFullPricingInfo() {
    print("=== FULL PRICING DEBUG ===")
    print("Products loaded: \(availableProducts.count)")
    print("Device locale: \(Locale.current)")
    print("Device region: \(Locale.current.region?.identifier ?? "Unknown")")
    
    for product in availableProducts {
        print("\n--- Product: \(product.id) ---")
        print("Display Price: \(product.displayPrice)")
        print("Raw Price: \(product.price)")
        print("Currency Code: \(product.priceFormatStyle.currencyCode ?? "Unknown")")
        print("Locale: \(product.priceFormatStyle.locale)")
        print("Display Period: \(product.displayPeriod)")
        
        if product.id.contains("yearly") {
            print("Weekly Equivalent: \(product.displayWeeklyPrice)")
            if let weeklyProduct = weeklyProduct {
                print("Savings: \(product.displaySavings(comparedTo: weeklyProduct) ?? "N/A")")
            }
        }
    }
    print("========================")
}
```

### **Production Verification**

Once live on App Store, verify with international users or VPN testing:

1. **User Testing**: Ask users in different regions to confirm pricing
2. **VPN Testing**: Use VPN + fresh App Store account for testing
3. **Analytics**: Monitor conversion rates by region to verify pricing effectiveness

## Conclusion

The dynamic localized pricing system provides a professional, user-friendly experience that automatically adapts to users' local currencies and regions. This implementation follows Apple's best practices and significantly improves international user experience and conversion potential.

**Key Benefits Achieved:**
- ✅ Professional international presentation
- ✅ Reduced purchase friction for global users
- ✅ Automatic compliance with regional requirements
- ✅ Zero maintenance currency management
- ✅ Dynamic, accurate savings calculations
- ✅ Future-proof implementation

For questions or issues, refer to the troubleshooting section above or Apple's StoreKit 2 documentation.