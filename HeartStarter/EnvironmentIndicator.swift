//
//  EnvironmentIndicator.swift
//  HeartStarter
//
//  Created by <PERSON> for environment-aware UI indicators
//

import SwiftUI

struct EnvironmentIndicator: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        let environmentInfo = subscriptionManager.getEnvironmentInfo()
        
        if environmentInfo.showEnvironmentIndicator {
            HStack(spacing: 4) {
                Image(systemName: iconForEnvironment(environmentInfo.displayName))
                    .font(.caption2)
                Text(environmentInfo.displayName)
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(colorForEnvironment(environmentInfo.displayName))
            )
            .opacity(0.9)
        }
    }
    
    private func iconForEnvironment(_ environment: String) -> String {
        switch environment {
        case "Development":
            return "wrench"
        case "TestFlight":
            return "airplane"
        default:
            return "checkmark.circle"
        }
    }
    
    private func colorForEnvironment(_ environment: String) -> Color {
        switch environment {
        case "Development":
            return .orange
        case "TestFlight":
            return .blue
        default:
            return .green
        }
    }
}

struct FloatingEnvironmentIndicator: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        let environmentInfo = subscriptionManager.getEnvironmentInfo()
        
        if environmentInfo.showEnvironmentIndicator {
            VStack {
                HStack {
                    Spacer()
                    EnvironmentIndicator()
                        .padding(.trailing, 16)
                        .padding(.top, 8)
                }
                Spacer()
            }
        }
    }
}

struct CompactEnvironmentBadge: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        let environmentInfo = subscriptionManager.getEnvironmentInfo()
        
        if environmentInfo.showEnvironmentIndicator {
            Text(environmentInfo.displayName.prefix(3).uppercased())
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(colorForEnvironment(environmentInfo.displayName))
                )
                .opacity(0.8)
        }
    }
    
    private func colorForEnvironment(_ environment: String) -> Color {
        switch environment {
        case "Development":
            return .orange
        case "TestFlight":
            return .blue
        default:
            return .green
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        EnvironmentIndicator()
        FloatingEnvironmentIndicator()
        CompactEnvironmentBadge()
    }
    .padding()
}