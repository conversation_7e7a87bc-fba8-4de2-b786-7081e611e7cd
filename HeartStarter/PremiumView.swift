//
//  PremiumView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI
import StoreKit

struct PremiumView: View {
    let onPurchase: () -> Void
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    @State private var selectedPlan: PremiumPlan = .weekly
    @State private var sparkleAnimation: Bool = false
    @State private var showingLoadingState = false
    @State private var isEligibleForFreeTrial = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Premium Header
                    PremiumHeaderView(sparkleAnimation: $sparkleAnimation)
                    
                    // Features List
                    PremiumFeaturesView()
                    
                    // Plan Selection
                    PremiumPlanSelector(selectedPlan: $selectedPlan)
                    
                    // Purchase Buttons
                    VStack(spacing: 10) {
                        // Free Trial Button (if eligible)
                        if isEligibleForFreeTrial {
                            Button(action: {
                                handleStartFreeTrial()
                            }) {
                                HStack {
                                    if subscriptionManager.isLoading || showingLoadingState {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(0.9)
                                        Text("Starting Trial...")
                                            .font(.headline)
                                            .fontWeight(.bold)
                                    } else {
                                        VStack(spacing: 4) {
                                            Text("Start 7-Day Free Trial")
                                                .font(.headline)
                                                .fontWeight(.bold)
                                            
                                            Text("Then \(getPriceText())")
                                                .font(.subheadline)
                                                .opacity(0.9)
                                        }
                                    }
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 14)
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(LinearGradient(
                                            colors: [.green, .blue],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ))
                                )
                            }
                            .disabled(subscriptionManager.isLoading || showingLoadingState)
                            .padding(.horizontal, 20)
                        }
                        
                        // Regular Purchase Button
                        Button(action: {
                            handlePurchase()
                        }) {
                            HStack {
                                if subscriptionManager.isLoading || showingLoadingState {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.9)
                                    Text("Processing...")
                                        .font(.headline)
                                        .fontWeight(.bold)
                                } else {
                                    VStack(spacing: 4) {
                                        if isEligibleForFreeTrial {
                                            Text("Continue Without Trial")
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                            
                                            Text(getPriceText())
                                                .font(.caption)
                                                .opacity(0.8)
                                        } else {
                                            Text("Upgrade to Premium")
                                                .font(.headline)
                                                .fontWeight(.bold)
                                            
                                            Text(getPriceText())
                                                .font(.subheadline)
                                                .opacity(0.9)
                                        }
                                    }
                                }
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, isEligibleForFreeTrial ? 10 : 14)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(LinearGradient(
                                        colors: [.yellow, .orange],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ))
                                    .opacity(isEligibleForFreeTrial ? 0.7 : 1.0)
                            )
                        }
                        .disabled(subscriptionManager.isLoading || showingLoadingState)
                        .padding(.horizontal, 20)
                    }
                    
                    // Restore Purchases Button
                    Button(action: {
                        handleRestorePurchases()
                    }) {
                        Text("Restore Purchases")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }
                    .disabled(subscriptionManager.isLoading || showingLoadingState)
                    
                    // Professional Disclaimer
                    VStack(spacing: 6) {
                        Text("Subscription will be charged to your credit card through your iTunes account. Your subscription will automatically renew unless cancelled at least 24 hours before the end of current period. You can turn off or manage subscription in your iTunes & App Store account settings. Cancellation takes effect at the end of current period.")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                        
                        if isEligibleForFreeTrial {
                            Text("7-day free trial available to new subscribers only.")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .fontWeight(.medium)
                        }
                        
                        HStack(spacing: 16) {
                            Link("Terms of Use", destination: URL(string: "https://sites.google.com/view/heartstarter-terms")!)
                                .font(.caption2)
                                .foregroundColor(.blue)
                            
                            Text("|")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            
                            Link("Privacy Policy", destination: URL(string: "https://sites.google.com/view/heartstarter-privacy")!)
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.bottom, 20)
            }
            .navigationTitle("HeartStarter Premium")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            sparkleAnimation = true
            Task {
                await subscriptionManager.loadProducts()
                await checkTrialEligibility()
            }
        }
        .alert("Purchase Status", isPresented: .constant(subscriptionManager.errorMessage != nil)) {
            Button("OK") {
                subscriptionManager.errorMessage = nil
            }
        } message: {
            Text(subscriptionManager.errorMessage ?? "")
        }
    }
    
    private func handlePurchase() {
        showingLoadingState = true
        
        Task {
            let productId: SubscriptionManager.ProductID = selectedPlan == .weekly ? .premiumWeekly : .premiumYearly
            
            guard let product = subscriptionManager.availableProducts.first(where: { $0.id == productId.rawValue }) else {
                subscriptionManager.errorMessage = "Product not available"
                showingLoadingState = false
                return
            }
            
            let success = await subscriptionManager.purchase(product)
            showingLoadingState = false
            
            if success {
                onPurchase()
                dismiss()
            }
        }
    }
    
    private func handleRestorePurchases() {
        debugLog("🔄 User tapped Restore Purchases button")
        showingLoadingState = true
        
        Task {
            debugLog("🔄 Calling subscriptionManager.restorePurchases()")
            let success = await subscriptionManager.restorePurchases()
            
            await MainActor.run {
                showingLoadingState = false
                
                if success {
                    debugLog("✅ Restore successful - dismissing view (notification will update UI)")
                    // Success: The subscriptionUpdated notification will update the UI
                    // Close the sheet immediately
                    onPurchase()
                    dismiss()
                } else {
                    debugLog("❌ Restore failed - showing error alert")
                    // Error: The error message is already set in subscriptionManager.errorMessage
                    // The alert will show automatically when errorMessage is not nil
                    debugLog("ℹ️ Error message: \(subscriptionManager.errorMessage ?? "No error message")")
                }
            }
        }
    }
    
    private func handleStartFreeTrial() {
        showingLoadingState = true
        
        Task {
            let productId: SubscriptionManager.ProductID = selectedPlan == .weekly ? .premiumWeekly : .premiumYearly
            
            guard let product = subscriptionManager.availableProducts.first(where: { $0.id == productId.rawValue }) else {
                subscriptionManager.errorMessage = "Product not available"
                showingLoadingState = false
                return
            }
            
            let success = await subscriptionManager.startFreeTrial(for: product)
            showingLoadingState = false
            
            if success {
                onPurchase()
                dismiss()
            }
        }
    }
    
    private func checkTrialEligibility() async {
        isEligibleForFreeTrial = await subscriptionManager.isEligibleForFreeTrial()
    }
    
    private func getPriceText() -> String {
        let productId: SubscriptionManager.ProductID = selectedPlan == .weekly ? .premiumWeekly : .premiumYearly
        
        if let product = subscriptionManager.availableProducts.first(where: { $0.id == productId.rawValue }) {
            let period = selectedPlan == .weekly ? "week" : "year"
            if selectedPlan == .yearly, let savings = subscriptionManager.savingsText() {
                return "\(product.displayPrice) per \(period) (\(savings)!)"
            }
            return "\(product.displayPrice) per \(period)"
        }
        
        return selectedPlan.priceText(using: subscriptionManager)
    }
}

struct PremiumHeaderView: View {
    @Binding var sparkleAnimation: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Image(systemName: "crown.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(LinearGradient(
                        colors: [.yellow, .orange],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                
                // Sparkle effects
                ForEach(0..<6) { index in
                    Image(systemName: "sparkle")
                        .font(.system(size: 12))
                        .foregroundColor(.yellow)
                        .offset(
                            x: sparkleOffset(for: index).x,
                            y: sparkleOffset(for: index).y
                        )
                        .scaleEffect(sparkleAnimation ? 1.2 : 0.8)
                        .opacity(sparkleAnimation ? 1.0 : 0.6)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true).delay(Double(index) * 0.1), value: sparkleAnimation)
                }
            }
            
            Text("Unlock Premium Features")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundStyle(LinearGradient(
                    colors: [.yellow, .orange],
                    startPoint: .leading,
                    endPoint: .trailing
                ))
            
            Text("Get the full HeartStarter experience with unlimited flirts and exclusive features")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
        }
        .padding(.top, 10)
    }
    
    private func sparkleOffset(for index: Int) -> CGPoint {
        let angle = Double(index) * (360.0 / 6.0) * .pi / 180.0
        let radius: Double = 45
        return CGPoint(
            x: cos(angle) * radius,
            y: sin(angle) * radius
        )
    }
}

struct PremiumFeaturesView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Premium Features")
                .font(.headline)
                .padding(.horizontal, 20)
            
            LazyVStack(spacing: 12) {
                PremiumFeatureRow(
                    icon: "infinity",
                    title: "Unlimited Daily Messages",
                    description: "Generate unlimited flirt messages every day (Free: 3/day)",
                    gradient: LinearGradient(colors: [.blue, .cyan], startPoint: .leading, endPoint: .trailing)
                )
                
                PremiumFeatureRow(
                    icon: "face.smiling",
                    title: "All Tone Options",
                    description: "Access Witty, Romantic, and Cheesy tones (Free: Random only)",
                    gradient: LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing)
                )
                
                PremiumFeatureRow(
                    icon: "wand.and.stars",
                    title: "Personal Touch",
                    description: "Add personal context for customized flirt messages",
                    gradient: LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing)
                )
                
                PremiumFeatureRow(
                    icon: "heart.fill",
                    title: "Save Favorite Messages",
                    description: "Build your personal collection with cloud sync",
                    gradient: LinearGradient(colors: [.red, .pink], startPoint: .leading, endPoint: .trailing)
                )
                
                PremiumFeatureRow(
                    icon: "icloud.fill",
                    title: "Cross-Device Sync",
                    description: "Access favorites and stats on all your devices",
                    gradient: LinearGradient(colors: [.orange, .yellow], startPoint: .leading, endPoint: .trailing)
                )
                
                PremiumFeatureRow(
                    icon: "sparkles",
                    title: "Priority Generation",
                    description: "Get fresh, unique messages with anti-repetition",
                    gradient: LinearGradient(colors: [.indigo, .blue], startPoint: .leading, endPoint: .trailing)
                )
            }
        }
    }
}

struct PremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let gradient: LinearGradient
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .medium))
                .foregroundStyle(gradient)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

enum PremiumPlan: CaseIterable {
    case weekly
    case yearly
    
    var title: String {
        switch self {
        case .weekly: return "Weekly"
        case .yearly: return "Yearly"
        }
    }
    
    func price(using subscriptionManager: SubscriptionManager) -> String {
        switch self {
        case .weekly: 
            return subscriptionManager.weeklyProduct?.displayPrice ?? "Loading..."
        case .yearly: 
            return subscriptionManager.yearlyProduct?.displayPrice ?? "Loading..."
        }
    }
    
    func priceText(using subscriptionManager: SubscriptionManager) -> String {
        switch self {
        case .weekly: 
            return subscriptionManager.formattedPrice(for: .premiumWeekly)
        case .yearly:
            let yearlyPrice = subscriptionManager.formattedPrice(for: .premiumYearly)
            let savingsText = subscriptionManager.savingsText() ?? ""
            return "\(yearlyPrice) (\(savingsText))"
        }
    }
    
    func effectiveWeeklyPrice(using subscriptionManager: SubscriptionManager) -> String {
        switch self {
        case .weekly: 
            return subscriptionManager.formattedPrice(for: .premiumWeekly)
        case .yearly: 
            return subscriptionManager.weeklyEquivalentPrice()
        }
    }
    
    func savings(using subscriptionManager: SubscriptionManager) -> String? {
        switch self {
        case .weekly: return nil
        case .yearly: return subscriptionManager.savingsText()
        }
    }
    
    func yearlyComparison(using subscriptionManager: SubscriptionManager) -> String {
        switch self {
        case .weekly: 
            // Calculate yearly cost from weekly price
            if let weeklyProduct = subscriptionManager.weeklyProduct {
                let weeklyDecimal = weeklyProduct.price
                let yearlyEquivalent = weeklyDecimal * Decimal(52)
                
                let formatter = NumberFormatter()
                formatter.numberStyle = .currency
                formatter.locale = weeklyProduct.priceFormatStyle.locale
                
                let yearlyString = formatter.string(from: yearlyEquivalent as NSDecimalNumber) ?? "Loading..."
                return "(\(yearlyString)/year)"
            }
            return "(yearly cost unavailable)"
        case .yearly: 
            return "(\(subscriptionManager.formattedPrice(for: .premiumYearly)))"
        }
    }
}

struct PremiumPlanSelector: View {
    @Binding var selectedPlan: PremiumPlan
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Choose Your Plan")
                .font(.headline)
                .padding(.horizontal, 20)
            
            HStack(spacing: 12) {
                ForEach(PremiumPlan.allCases, id: \.self) { plan in
                    PremiumPlanButton(
                        plan: plan,
                        isSelected: selectedPlan == plan,
                        action: {
                            selectedPlan = plan
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

struct PremiumPlanButton: View {
    let plan: PremiumPlan
    let isSelected: Bool
    let action: () -> Void
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                if plan == .yearly, let savings = subscriptionManager.savingsText() {
                    Text(savings)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(.green)
                        )
                } else {
                    Text(" ")
                        .font(.caption)
                        .padding(.vertical, 2)
                }
                
                Text(plan.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                if plan == .yearly {
                    VStack(spacing: 2) {
                        Text(subscriptionManager.weeklyEquivalentPrice())
                            .font(.title3)
                            .fontWeight(.bold)
                        
                        Text("per week")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if let yearlyProduct = subscriptionManager.yearlyProduct {
                            Text("billed \(yearlyProduct.displayPrice) annually")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .opacity(0.8)
                        }
                    }
                } else {
                    VStack(spacing: 2) {
                        if let weeklyProduct = subscriptionManager.weeklyProduct {
                            Text(weeklyProduct.displayPrice)
                                .font(.title3)
                                .fontWeight(.bold)
                        } else {
                            Text(plan.price(using: subscriptionManager))
                                .font(.title3)
                                .fontWeight(.bold)
                        }
                        
                        Text("per week")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : .clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : .gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    PremiumView(onPurchase: {})
}