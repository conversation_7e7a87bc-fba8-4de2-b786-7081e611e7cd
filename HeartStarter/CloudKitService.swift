//
//  CloudKitService.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import CloudKit
import Combine

class CloudKitService: ObservableObject {
    static let shared = CloudKitService()
    
    private let container = CKContainer.default()
    private lazy var privateDatabase = container.privateCloudDatabase
    
    // Record Types
    enum RecordType: String {
        case user = "User"
        case flirtMessage = "FlirtMessage"
        case userStats = "UserStats"
        case subscription = "Subscription"
        case subscriptionHistory = "SubscriptionHistory"
    }
    
    // Error Types
    enum CloudKitError: LocalizedError {
        case accountNotAvailable
        case networkUnavailable
        case quotaExceeded
        case recordNotFound
        case permissionFailure
        case unknownError(Error)
        
        var errorDescription: String? {
            switch self {
            case .accountNotAvailable:
                return "iCloud account not available. Please sign in to iCloud."
            case .networkUnavailable:
                return "Network unavailable. Please check your connection."
            case .quotaExceeded:
                return "iCloud storage is full. Please free up some space."
            case .recordNotFound:
                return "Data not found."
            case .permissionFailure:
                return "Permission denied. Please check iCloud settings."
            case .unknownError(let error):
                return error.localizedDescription
            }
        }
    }
    
    private init() {}
    
    // MARK: - Account Status
    func checkAccountStatus() async throws -> CKAccountStatus {
        return try await container.accountStatus()
    }
    
    // MARK: - User Management
    func setupUser(userId: String, name: String?, email: String?) async {
        do {
            let userRecord = try await fetchOrCreateUserRecord(userId: userId)
            
            // Update user info if provided
            if let name = name {
                userRecord["name"] = name as CKRecordValueProtocol
            }
            if let email = email {
                userRecord["email"] = email as CKRecordValueProtocol
            }
            userRecord["lastLoginDate"] = Date() as CKRecordValueProtocol
            
            try await privateDatabase.save(userRecord)
        } catch {
            // Handle CloudKit errors gracefully
            if let ckError = error as? CKError {
                switch ckError.code {
                case .quotaExceeded:
                    // CloudKit quota exceeded - normal in development, silently continue
                    debugLog("CloudKit quota exceeded during user setup - this is expected in development")
                case .networkUnavailable, .networkFailure:
                    debugLog("CloudKit network unavailable during user setup")
                default:
                    debugLog("CloudKit setup user error: \(error)")
                }
            } else {
                debugLog("Failed to setup user: \(error)")
            }
        }
    }
    
    private func fetchOrCreateUserRecord(userId: String) async throws -> CKRecord {
        let recordID = CKRecord.ID(recordName: userId)
        
        do {
            return try await privateDatabase.record(for: recordID)
        } catch let error as CKError where error.code == .unknownItem {
            // User doesn't exist, create new record
            let newRecord = CKRecord(recordType: RecordType.user.rawValue, recordID: recordID)
            newRecord["userId"] = userId as CKRecordValueProtocol
            newRecord["createdDate"] = Date() as CKRecordValueProtocol
            newRecord["subscriptionTier"] = SubscriptionTier.free.rawValue as CKRecordValueProtocol
            return newRecord
        }
    }
    
    // MARK: - User Stats Management
    func saveUserStats(_ stats: UserStats, userId: String) async throws {
        let recordID = CKRecord.ID(recordName: "stats_\(userId)")
        
        let record: CKRecord
        do {
            record = try await privateDatabase.record(for: recordID)
        } catch let error as CKError where error.code == .unknownItem {
            record = CKRecord(recordType: RecordType.userStats.rawValue, recordID: recordID)
            record["userId"] = userId as CKRecordValueProtocol
        }
        
        // Update stats
        record["totalFlirtsGenerated"] = NSNumber(value: stats.totalFlirtsGenerated)
        record["premiumPersonalTouchBoostRemaining"] = NSNumber(value: stats.premiumPersonalTouchBoostRemaining)
        record["dailyFlirtCount"] = NSNumber(value: stats.dailyFlirtCount)
        record["lastResetDate"] = stats.lastResetDate as CKRecordValueProtocol
        record["lastUpdated"] = Date() as CKRecordValueProtocol
        
        // Subscription info
        record["subscriptionTier"] = stats.subscriptionInfo.tier.rawValue as CKRecordValueProtocol
        if let startDate = stats.subscriptionInfo.startDate {
            record["subscriptionStartDate"] = startDate as CKRecordValueProtocol
        }
        if let expiryDate = stats.subscriptionInfo.expiryDate {
            record["subscriptionExpiryDate"] = expiryDate as CKRecordValueProtocol
        }
        if let productId = stats.subscriptionInfo.productId {
            record["subscriptionProductId"] = productId as CKRecordValueProtocol
        }
        record["hasUsedFreeTrial"] = NSNumber(value: stats.subscriptionInfo.hasUsedFreeTrial)
        record["autoRenewEnabled"] = NSNumber(value: stats.subscriptionInfo.autoRenewEnabled)
        
        // Trial fields
        record["isInFreeTrial"] = NSNumber(value: stats.subscriptionInfo.isInFreeTrial)
        if let trialStartDate = stats.subscriptionInfo.freeTrialStartDate {
            record["freeTrialStartDate"] = trialStartDate as CKRecordValueProtocol
        }
        if let trialEndDate = stats.subscriptionInfo.freeTrialEndDate {
            record["freeTrialEndDate"] = trialEndDate as CKRecordValueProtocol
        }
        record["trialGracePeriodDays"] = NSNumber(value: stats.subscriptionInfo.trialGracePeriodDays)
        
        // Backward compatibility
        record["isPremium"] = NSNumber(value: stats.isPremium)
        
        // Convert seen message IDs to array for CloudKit
        let seenIdsArray: [String] = Array(stats.seenMessageIds)
        record["seenMessageIds"] = seenIdsArray as CKRecordValueProtocol
        
        try await privateDatabase.save(record)
    }
    
    func fetchUserStats(userId: String) async throws -> UserStats? {
        let recordID = CKRecord.ID(recordName: "stats_\(userId)")
        
        do {
            let record = try await privateDatabase.record(for: recordID)
            
            var stats = UserStats()
            if let n = record["totalFlirtsGenerated"] as? NSNumber { stats.totalFlirtsGenerated = n.intValue } else { stats.totalFlirtsGenerated = 0 }
            if let n = record["premiumPersonalTouchBoostRemaining"] as? NSNumber { stats.premiumPersonalTouchBoostRemaining = n.intValue }
            if let n = record["dailyFlirtCount"] as? NSNumber { 
                let rawCount = n.intValue
                // Reset to 0 for free users if count suggests premium usage
                stats.dailyFlirtCount = (stats.subscriptionTier == .free && rawCount > 3) ? 0 : rawCount
            } else { 
                stats.dailyFlirtCount = 0 
            }
            stats.lastResetDate = record["lastResetDate"] as? Date ?? Date()
            
            // Parse subscription info
            var subscriptionInfo = SubscriptionInfo()
            if let tierString = record["subscriptionTier"] as? String,
               let tier = SubscriptionTier(rawValue: tierString) {
                subscriptionInfo.tier = tier
            }
            subscriptionInfo.startDate = record["subscriptionStartDate"] as? Date
            subscriptionInfo.expiryDate = record["subscriptionExpiryDate"] as? Date
            subscriptionInfo.productId = record["subscriptionProductId"] as? String
            if let b = record["hasUsedFreeTrial"] as? NSNumber { subscriptionInfo.hasUsedFreeTrial = b.boolValue }
            if let b = record["autoRenewEnabled"] as? NSNumber { subscriptionInfo.autoRenewEnabled = b.boolValue }
            
            // Parse trial fields
            if let b = record["isInFreeTrial"] as? NSNumber { subscriptionInfo.isInFreeTrial = b.boolValue }
            subscriptionInfo.freeTrialStartDate = record["freeTrialStartDate"] as? Date
            subscriptionInfo.freeTrialEndDate = record["freeTrialEndDate"] as? Date
            if let days = record["trialGracePeriodDays"] as? NSNumber { subscriptionInfo.trialGracePeriodDays = days.intValue }
            
            stats.subscriptionInfo = subscriptionInfo
            
            // Convert array back to set
            if let seenIdsArray = record["seenMessageIds"] as? [String] {
                stats.seenMessageIds = Set(seenIdsArray)
            }
            
            return stats
        } catch let error as CKError where error.code == .unknownItem {
            return nil // No stats exist yet
        }
    }
    
    // MARK: - Flirt Messages Management
    func saveFavoriteMessage(_ message: FlirtMessage, userId: String) async throws {
        let recordID = CKRecord.ID(recordName: "favorite_\(message.id)")
        
        let record = CKRecord(recordType: RecordType.flirtMessage.rawValue, recordID: recordID)
        record["userId"] = userId as CKRecordValueProtocol
        record["messageId"] = message.id as CKRecordValueProtocol
        record["text"] = message.text as CKRecordValueProtocol
        record["spiceLevel"] = message.spiceLevel.rawValue as CKRecordValueProtocol
        if let toneRaw = message.tone?.rawValue { record["tone"] = toneRaw as CKRecordValueProtocol }
        record["hasPersonalTouch"] = NSNumber(value: message.hasPersonalTouch)
        record["createdAt"] = message.createdAt as CKRecordValueProtocol
        record["isFavorite"] = NSNumber(value: true)
        
        try await privateDatabase.save(record)
    }
    
    func fetchFavoriteMessages(userId: String) async throws -> [FlirtMessage] {
        let predicate = NSPredicate(format: "userId == %@ AND isFavorite == YES", userId)
        let query = CKQuery(recordType: RecordType.flirtMessage.rawValue, predicate: predicate)
        
        let result = try await privateDatabase.records(matching: query)
        
        return result.matchResults.compactMap { (_, recordResult) -> FlirtMessage? in
            guard case .success(let record) = recordResult else { return nil }
            return self.flirtMessageFromRecord(record)
        }
    }
    
    func deleteFavoriteMessage(messageId: String) async throws {
        let recordID = CKRecord.ID(recordName: "favorite_\(messageId)")
        try await privateDatabase.deleteRecord(withID: recordID)
    }
    
    private func flirtMessageFromRecord(_ record: CKRecord) -> FlirtMessage? {
        guard let messageId = record["messageId"] as? String,
              let text = record["text"] as? String,
              let spiceLevelRaw = record["spiceLevel"] as? String,
              let spiceLevel = SpiceLevel(rawValue: spiceLevelRaw),
              let createdAt = record["createdAt"] as? Date else {
            return nil
        }
        
        let tone: FlirtTone? = {
            if let toneRaw = record["tone"] as? String {
                return FlirtTone(rawValue: toneRaw)
            }
            return nil
        }()
        
        let hasPersonalTouch = (record["hasPersonalTouch"] as? NSNumber)?.boolValue ?? false
        
        return FlirtMessage(
            id: messageId,
            text: text,
            spiceLevel: spiceLevel,
            tone: tone,
            hasPersonalTouch: hasPersonalTouch,
            createdAt: createdAt
        )
    }
    
    // MARK: - Enhanced Subscription Management
    func saveSubscription(userId: String, tier: SubscriptionTier, productId: String, transactionId: String, originalTransactionId: String? = nil, expirationDate: Date?, isTrialPeriod: Bool = false) async throws {
        let recordID = CKRecord.ID(recordName: "subscription_\(userId)")
        
        let record: CKRecord
        do {
            record = try await privateDatabase.record(for: recordID)
        } catch let error as CKError where error.code == .unknownItem {
            record = CKRecord(recordType: RecordType.subscription.rawValue, recordID: recordID)
            record["userId"] = userId as CKRecordValueProtocol
        }
        
        // Enhanced subscription fields
        record["subscriptionTier"] = tier.rawValue as CKRecordValueProtocol
        record["productId"] = productId as CKRecordValueProtocol
        record["transactionId"] = transactionId as CKRecordValueProtocol
        if let originalTransactionId = originalTransactionId {
            record["originalTransactionId"] = originalTransactionId as CKRecordValueProtocol
        }
        record["purchaseDate"] = Date() as CKRecordValueProtocol
        if let expirationDate = expirationDate { 
            record["expirationDate"] = expirationDate as CKRecordValueProtocol 
        }
        record["isActive"] = NSNumber(value: true)
        record["autoRenewStatus"] = NSNumber(value: tier == .premium)
        record["isTrialPeriod"] = NSNumber(value: isTrialPeriod)
        
        // Enhanced trial tracking
        if isTrialPeriod {
            record["trialStartDate"] = Date() as CKRecordValueProtocol
            record["trialEndDate"] = expirationDate
            record["hasUsedFreeTrial"] = NSNumber(value: true)
        }
        
        record["lastUpdated"] = Date() as CKRecordValueProtocol
        
        // Backward compatibility
        record["isPremium"] = NSNumber(value: tier == .premium)
        
        try await privateDatabase.save(record)
    }
    
    func saveSubscriptionHistory(userId: String, action: String, fromTier: SubscriptionTier?, toTier: SubscriptionTier, transactionId: String) async throws {
        let recordID = CKRecord.ID(recordName: "history_\(userId)_\(UUID().uuidString)")
        let record = CKRecord(recordType: RecordType.subscriptionHistory.rawValue, recordID: recordID)
        
        record["userId"] = userId as CKRecordValueProtocol
        record["action"] = action as CKRecordValueProtocol
        if let fromTier = fromTier {
            record["fromTier"] = fromTier.rawValue as CKRecordValueProtocol
        }
        record["toTier"] = toTier.rawValue as CKRecordValueProtocol
        record["timestamp"] = Date() as CKRecordValueProtocol
        record["transactionId"] = transactionId as CKRecordValueProtocol
        
        try await privateDatabase.save(record)
    }
    
    func cancelSubscription(userId: String) async throws {
        let recordID = CKRecord.ID(recordName: "subscription_\(userId)")
        
        do {
            let record = try await privateDatabase.record(for: recordID)
            record["autoRenewStatus"] = NSNumber(value: false)
            record["cancellationDate"] = Date() as CKRecordValueProtocol
            record["lastUpdated"] = Date() as CKRecordValueProtocol
            
            try await privateDatabase.save(record)
        } catch let error as CKError where error.code == .unknownItem {
            // Subscription record doesn't exist, nothing to cancel
            return
        }
    }
    
    func validateSubscriptionStatus(userId: String) async throws -> SubscriptionTier {
        let recordID = CKRecord.ID(recordName: "subscription_\(userId)")
        
        do {
            let record = try await privateDatabase.record(for: recordID)
            
            // Check if subscription is still active
            if let expirationDate = record["expirationDate"] as? Date {
                if Date() >= expirationDate {
                    // Subscription expired, update to free
                    record["subscriptionTier"] = SubscriptionTier.free.rawValue as CKRecordValueProtocol
                    record["isActive"] = NSNumber(value: false)
                    record["lastUpdated"] = Date() as CKRecordValueProtocol
                    
                    try await privateDatabase.save(record)
                    return .free
                }
            }
            
            // Get current tier
            if let tierString = record["subscriptionTier"] as? String,
               let tier = SubscriptionTier(rawValue: tierString),
               let isActive = record["isActive"] as? NSNumber,
               isActive.boolValue {
                return tier
            }
            
            return .free
            
        } catch let error as CKError where error.code == .unknownItem {
            return .free // No subscription record exists
        }
    }
    
    func fetchSubscription(userId: String) async throws -> CKRecord? {
        let recordID = CKRecord.ID(recordName: "subscription_\(userId)")
        
        do {
            return try await privateDatabase.record(for: recordID)
        } catch let error as CKError where error.code == .unknownItem {
            return nil
        }
    }
    
    // MARK: - Data Synchronization
    func syncUserData(userId: String, localStats: UserStats, localFavorites: [FlirtMessage]) async throws -> (UserStats, [FlirtMessage]) {
        // Fetch cloud data
        let cloudStats = try await fetchUserStats(userId: userId)
        let cloudFavorites = try await fetchFavoriteMessages(userId: userId)
        
        // Merge stats (cloud takes precedence for most fields)
        var mergedStats = cloudStats ?? localStats
        
        // Merge seen message IDs (union of both sets)
        mergedStats.seenMessageIds = mergedStats.seenMessageIds.union(localStats.seenMessageIds)
        
        // Use local daily count if it's higher (user might have used app offline)
        // But reset to 0 for free users if count suggests premium usage (> 3)
        if localStats.dailyFlirtCount > mergedStats.dailyFlirtCount {
            let maxCount = max(localStats.dailyFlirtCount, mergedStats.dailyFlirtCount)
            if mergedStats.subscriptionTier == .free && maxCount > 3 {
                // Reset to 0 if count suggests it's from premium usage
                mergedStats.dailyFlirtCount = 0
            } else {
                mergedStats.dailyFlirtCount = maxCount
            }
            mergedStats.totalFlirtsGenerated = max(localStats.totalFlirtsGenerated, mergedStats.totalFlirtsGenerated)
        } else if mergedStats.subscriptionTier == .free && mergedStats.dailyFlirtCount > 3 {
            // Reset CloudKit value if user is free and count suggests premium usage
            mergedStats.dailyFlirtCount = 0
        }
        
        // Merge favorites (combine both lists, removing duplicates)
        let allFavorites = (cloudFavorites + localFavorites).uniqueById()
        
        // Save merged data back to cloud
        try await saveUserStats(mergedStats, userId: userId)
        
        for favorite in localFavorites {
            if !cloudFavorites.contains(where: { $0.id == favorite.id }) {
                try await saveFavoriteMessage(favorite, userId: userId)
            }
        }
        
        return (mergedStats, allFavorites)
    }
    
    // MARK: - Data Cleanup
    func clearUserData() async {
        // This would be called when user signs out
        // In a real implementation, you might want to keep some data
        // but mark it as inactive or remove sensitive information
    }
    
    // MARK: - Error Handling
    private func mapCloudKitError(_ error: Error) -> CloudKitError {
        guard let ckError = error as? CKError else {
            return .unknownError(error)
        }
        
        switch ckError.code {
        case .notAuthenticated:
            return .accountNotAvailable
        case .networkUnavailable, .networkFailure:
            return .networkUnavailable
        case .quotaExceeded:
            return .quotaExceeded
        case .unknownItem:
            return .recordNotFound
        case .permissionFailure:
            return .permissionFailure
        default:
            return .unknownError(error)
        }
    }
}

// MARK: - Array Extension for Unique Elements
private extension Array where Element == FlirtMessage {
    func uniqueById() -> [FlirtMessage] {
        var seen = Set<String>()
        return filter { message in
            if seen.contains(message.id) {
                return false
            } else {
                seen.insert(message.id)
                return true
            }
        }
    }
}
