//
//  ContentHeightPreferenceKey.swift
//  HeartStarter
//
//  Created by <PERSON> on 25/09/2025.
//

import SwiftUI

struct ContentHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}

struct ContentSizeReader: View {
    let onSizeChange: (CGSize) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            Color.clear
                .preference(key: ContentHeightPreferenceKey.self, value: geometry.size.height)
                .onAppear {
                    onSizeChange(geometry.size)
                }
                .onChange(of: geometry.size) { _, newSize in
                    onSizeChange(newSize)
                }
        }
    }
}