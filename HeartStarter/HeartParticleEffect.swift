//
//  HeartParticleEffect.swift
//  HeartStarter
//
//  Created by <PERSON> on 22/09/2025.
//

import SwiftUI
import Combine

struct HeartParticle: Identifiable {
    let id = UUID()
    var position: CGPoint
    var velocity: CGVector
    var scale: CGFloat
    var rotation: Double
    var opacity: Double
    var color: Color
    
    init(startPosition: CGPoint) {
        self.position = startPosition
        
        // Random initial velocity - wider spread for floating across screen
        let horizontalSpeed = Double.random(in: -120...120) // Wider horizontal range
        let verticalSpeed = Double.random(in: -200...(-120)) // Stronger upward movement
        self.velocity = CGVector(dx: horizontalSpeed, dy: verticalSpeed)
        
        // Random visual properties - make them larger and more visible
        self.scale = Double.random(in: 1.0...1.8)
        self.rotation = Double.random(in: 0...360)
        self.opacity = 1.0
        
        // Random heart colors (more vibrant)
        let colors: [Color] = [.pink, .red, .purple, .blue, .orange]
        self.color = colors.randomElement() ?? .pink
    }
}

struct HeartParticleSystem: View {
    @State private var particles: [HeartParticle] = []
    @State private var animationTimer: Timer?
    
    let isActive: Bool
    let sourcePosition: CGPoint
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Image(systemName: "heart.fill")
                    .foregroundColor(particle.color)
                    .font(.system(size: 24, weight: .bold))
                    .scaleEffect(particle.scale)
                    .rotationEffect(.degrees(particle.rotation))
                    .opacity(particle.opacity)
                    .position(x: particle.position.x, y: particle.position.y)
                    .allowsHitTesting(false)
                    .shadow(color: particle.color.opacity(0.5), radius: 2)
            }
        }
        .allowsHitTesting(false)
        .onChange(of: isActive) { _, newValue in
            if newValue {
                startHeartBurst()
            }
        }
        .onDisappear {
            stopAnimation()
        }
    }
    
    private func startHeartBurst() {
        debugLog("🚀 Starting heart burst from position: \(sourcePosition)")
        
        // Clear existing particles
        particles.removeAll()
        
        // Create initial burst of hearts from the source position (message card)
        let particleCount = Int.random(in: 10...15) // More hearts for better effect
        
        for _ in 0..<particleCount {
            // Spread hearts around the source position
            let offsetX = Double.random(in: -30...30)
            let offsetY = Double.random(in: -10...10)
            let startPosition = CGPoint(
                x: sourcePosition.x + offsetX,
                y: sourcePosition.y + offsetY
            )
            
            let particle = HeartParticle(startPosition: startPosition)
            particles.append(particle)
        }
        
        debugLog("💖 Created \(particles.count) heart particles")
        
        // Start physics simulation
        startPhysicsAnimation()
    }
    
    private func startPhysicsAnimation() {
        stopAnimation() // Clean up any existing timer
        
        animationTimer = Timer.scheduledTimer(withTimeInterval: 1/30.0, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func updateParticles() {
        let deltaTime: Double = 1/30.0
        let gravity: Double = 150 // Reduced gravity for more floating effect
        let screenBounds = UIScreen.main.bounds
        
        for i in particles.indices {
            // Apply gravity
            particles[i].velocity.dy += gravity * deltaTime
            
            // Add slight wind effect for more natural movement
            let windEffect = sin(Date().timeIntervalSince1970 * 2) * 10
            particles[i].velocity.dx += windEffect * deltaTime * 0.1
            
            // Update position
            particles[i].position.x += particles[i].velocity.dx * deltaTime
            particles[i].position.y += particles[i].velocity.dy * deltaTime
            
            // Update rotation (spinning effect)
            particles[i].rotation += 120 * deltaTime
            
            // Fade out gradually over time and distance
            let distanceFromSource = sqrt(
                pow(particles[i].position.x - sourcePosition.x, 2) +
                pow(particles[i].position.y - sourcePosition.y, 2)
            )
            
            if distanceFromSource > 100 {
                particles[i].opacity = max(0, particles[i].opacity - deltaTime * 0.5)
            }
        }
        
        // Remove particles that are far off-screen or fully faded
        particles.removeAll { particle in
            particle.position.y > screenBounds.height + 200 ||
            particle.position.x < -200 ||
            particle.position.x > screenBounds.width + 200 ||
            particle.opacity <= 0.02
        }
        
        // Stop animation when all particles are gone
        if particles.isEmpty {
            stopAnimation()
        }
    }
    
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
}

// Global hearts animation coordinator
class HeartAnimationCoordinator: ObservableObject {
    @Published var showAnimation = false
    @Published var sourcePosition: CGPoint = .zero
    
    func triggerAnimation(from position: CGPoint) {
        let heartsEnabled = UserDefaults.standard.object(forKey: "HeartStarter_ShowHeartsOnLaunch") as? Bool ?? true
        guard heartsEnabled else {
            debugLog("🚫 Hearts animation disabled — skipping trigger")
            showAnimation = false
            return
        }
        
        debugLog("🎯 Triggering global heart animation from position: \(position)")
        sourcePosition = position
        showAnimation = false
        
        withAnimation(.easeInOut(duration: 0.2)) {
            showAnimation = true
            debugLog("💫 Global animation state set to TRUE")
        }
        
        // Reset after animation duration
        DispatchQueue.main.asyncAfter(deadline: .now() + 6.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                self.showAnimation = false
                debugLog("⏰ Global animation state set to FALSE")
            }
        }
    }
}

struct BouncingHeartsModifier: ViewModifier {
    @EnvironmentObject var heartCoordinator: HeartAnimationCoordinator
    @State private var messageRect: CGRect = .zero
    
    let messageId: String
    
    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            messageRect = geometry.frame(in: .global)
                        }
                        .onChange(of: geometry.frame(in: .global)) { _, newRect in
                            messageRect = newRect
                        }
                }
            )
            .onAppear {
                triggerHeartAnimation()
            }
            .onChange(of: messageId) { _, _ in
                triggerHeartAnimation()
            }
    }
    
    private func triggerHeartAnimation() {
        // Calculate the bottom center of the message card
        let sourcePosition = CGPoint(
            x: messageRect.midX,
            y: messageRect.maxY - 20
        )
        
        // Delay slightly to ensure geometry is calculated
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            heartCoordinator.triggerAnimation(from: sourcePosition)
        }
    }
}

// Global overlay for hearts that can float anywhere on screen
struct GlobalHeartsOverlay: View {
    @EnvironmentObject var heartCoordinator: HeartAnimationCoordinator
    
    var body: some View {
        GeometryReader { geometry in
            HeartParticleSystem(
                isActive: heartCoordinator.showAnimation,
                sourcePosition: heartCoordinator.sourcePosition
            )
            .frame(width: geometry.size.width, height: geometry.size.height)
            .allowsHitTesting(false)
        }
        .allowsHitTesting(false)
        .ignoresSafeArea() // Allow hearts to float everywhere, including safe areas
    }
}

extension View {
    func bouncingHearts(for messageId: String) -> some View {
        self.modifier(BouncingHeartsModifier(messageId: messageId))
    }
}
