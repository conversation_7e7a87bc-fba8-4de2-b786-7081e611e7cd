//
//  FloatingButtonContainer.swift
//  HeartStarter
//
//  Created by <PERSON> on 25/09/2025.
//

import SwiftUI

struct FloatingButtonContainer: View {
    let isFloating: Bool
    let isLoading: Bool
    let canGenerate: Bool
    let loadingMessageProvider: LoadingMessageProvider?
    let action: () -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(spacing: 0) {
            if isFloating {
                GenerateButton(
                    isLoading: isLoading,
                    canGenerate: canGenerate,
                    loadingMessageProvider: loadingMessageProvider,
                    action: action
                )
                .padding(.horizontal, 16)
                .padding(.top, 12)
                .padding(.bottom, max(16, UIApplication.shared.connectedScenes
                    .compactMap { $0 as? UIWindowScene }
                    .first?.windows.first?.safeAreaInsets.bottom ?? 0))
                // No background - let the button float naturally over content
                .shadow(color: .pink.opacity(0.2), radius: 15, x: 0, y: -5)
                .shadow(color: .black.opacity(colorScheme == .dark ? 0.3 : 0.1), radius: 8, x: 0, y: -2)
            } else {
                // Inline state - standard layout
                GenerateButton(
                    isLoading: isLoading,
                    canGenerate: canGenerate,
                    loadingMessageProvider: loadingMessageProvider,
                    action: action
                )
                .padding(.horizontal, 20)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isFloating)
    }
}

#Preview {
    VStack {
        Spacer()
        
        FloatingButtonContainer(
            isFloating: false,
            isLoading: false,
            canGenerate: true,
            loadingMessageProvider: nil,
            action: {}
        )
        
        FloatingButtonContainer(
            isFloating: true,
            isLoading: false,
            canGenerate: true,
            loadingMessageProvider: nil,
            action: {}
        )
    }
    .background(Color(.systemGroupedBackground))
}