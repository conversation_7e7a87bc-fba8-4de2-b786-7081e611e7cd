//
//  AppLinks.swift
//  HeartStarter
//
//  Centralises external links so we can validate them before exposing them in UI.
//

import Foundation

enum AppLinks {
    static var shareURL: URL? {
        if let envValue = ProcessInfo.processInfo.environment["HEARTSTARTER_SHARE_URL"],
           let url = URL(string: envValue),
           isValid(appStoreURL: url) {
            return url
        }
        return nil
    }

    private static func isValid(appStoreURL url: URL) -> Bool {
        guard url.scheme == "https",
              let host = url.host,
              host.contains("apple.com"),
              url.absoluteString.contains("/app/") else {
            return false
        }
        // Avoid shipping placeholder IDs
        return !url.absoluteString.contains("1234567890")
    }
}
