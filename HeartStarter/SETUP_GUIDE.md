# HeartStarter Authentication & CloudKit Setup Guide

This guide provides step-by-step instructions to complete the setup for Sign in with Apple and CloudKit integration.

## 1. Xcode Project Configuration

### App Capabilities
1. Open your project in Xcode
2. Select your app target
3. Go to "Signing & Capabilities" tab
4. Add the following capabilities:
   - **Sign in with Apple**
   - **CloudKit**
   - **Push Notifications** (optional, for future sync notifications)

### CloudKit Container Setup
1. In the CloudKit capability, you'll see a container created automatically
2. Note the container identifier (should be something like `iCloud.com.yourteam.HeartStarter`)

## 2. Apple Developer Account Setup

### App Identifier Configuration
1. Go to [Apple Developer Portal](https://developer.apple.com)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Select "Identifiers"
4. Find your app identifier (or create one)
5. Enable the following services:
   - **Sign in with Apple**
   - **CloudKit**

### Sign in with Apple Service Setup
1. In the "Sign in with <PERSON>" section of your App ID
2. Configure the service (usually no additional setup required)
3. Make sure your bundle ID matches exactly

## 3. CloudKit Schema Setup

### Using CloudKit Dashboard
1. Go to [CloudKit Dashboard](https://icloud.developer.apple.com/)
2. Select your container
3. Go to "Schema" section
4. Create the following record types:

#### User Record Type
```
Record Type: User
Fields:
- userId (String, Indexed)
- name (String)
- email (String)
- createdDate (Date/Time)
- lastLoginDate (Date/Time)
- isPremium (Int64, default: 0)
```

#### UserStats Record Type
```
Record Type: UserStats
Fields:
- userId (String, Indexed)
- totalFlirtsGenerated (Int64, default: 0)
- dailyFlirtCount (Int64, default: 0)
- lastResetDate (Date/Time)
- isPremium (Int64, default: 0)
- seenMessageIds (String List)
- lastUpdated (Date/Time)
- autoRenewEnabled (Int64, default: 0)
- hasUsedFreeTrial (Int64, default: 0)
- isInFreeTrial (Int64, default: 0)
- subscriptionTier (String)
- trialGracePeriodDays (Int64, default: 0)
```

#### FlirtMessage Record Type
```
Record Type: FlirtMessage
Fields:
- userId (String, Indexed)
- messageId (String, Indexed)
- text (String)
- spiceLevel (String)
- tone (String)
- createdAt (Date/Time)
- isFavorite (Int64, default: 0)
```

#### Subscription Record Type
```
Record Type: Subscription
Fields:
- userId (String, Indexed)
- productId (String)
- transactionId (String)
- purchaseDate (Date/Time)
- expirationDate (Date/Time)
- isActive (Int64, default: 0)
```

### Security Roles
1. Go to "Security Roles" in CloudKit Dashboard
2. Make sure "Authenticated Users" can:
   - Read and write their own records
   - Create new records

## 4. Info.plist Configuration

Add the following to your `Info.plist`:

```xml
<key>NSCloudKitAllowsUIApplication</key>
<true/>
```

## 5. Testing Authentication

### Simulator Testing
1. Sign in to iCloud on the iOS Simulator
2. Make sure you're using a test Apple ID (not your main account)
3. Test Sign in with Apple flow

### Device Testing
1. Use a development device
2. Install the app through Xcode
3. Test with a real Apple ID
4. Verify CloudKit data in the CloudKit Dashboard

## 6. Production Deployment Checklist

### App Store Connect
1. Upload your app to App Store Connect
2. Enable "Sign in with Apple" in App Store Connect
3. Configure CloudKit production environment
4. Submit for review

### CloudKit Production
1. Deploy schema from development to production
2. Test with production CloudKit container
3. Monitor CloudKit usage and quotas

## 7. Privacy and Terms

### App Store Requirements
1. Add privacy policy explaining data collection
2. Include terms of service
3. Make sure Sign in with Apple is prominently featured

### User Communication
1. Clearly explain what data is stored in iCloud
2. Provide options for data deletion
3. Explain sync behavior across devices

## 8. Error Handling

### Common Issues
1. **CloudKit not available**: Check iCloud account status
2. **Sign in failed**: Verify App ID configuration
3. **Sync issues**: Check network connectivity and CloudKit quotas
4. **Schema mismatches**: Ensure CloudKit schema matches code

### User Support
1. Implement clear error messages
2. Provide troubleshooting guidance
3. Include contact information for support

## 9. Performance Optimization

### CloudKit Best Practices
1. Batch operations when possible
2. Use efficient queries with proper indexing
3. Handle sync conflicts gracefully
4. Implement proper retry logic

### Offline Support
1. Cache data locally
2. Sync when connectivity is restored
3. Handle merge conflicts intelligently

## 10. Monitoring and Analytics

### CloudKit Metrics
1. Monitor CloudKit usage in dashboard
2. Track sync success rates
3. Monitor quota usage

### User Metrics
1. Track sign-in success rates
2. Monitor sync errors
3. Analyze user engagement with cloud features

---

## Important Notes

- **Bundle ID**: Make sure your bundle identifier is consistent across Xcode, App ID, and CloudKit container
- **Team ID**: Ensure you're using the correct development team
- **Provisioning**: Use proper provisioning profiles with Sign in with Apple enabled
- **Testing**: Thoroughly test both online and offline scenarios
- **Backup**: Always have a backup plan for user data

## Support Resources

- [Apple CloudKit Documentation](https://developer.apple.com/documentation/cloudkit)
- [Sign in with Apple Documentation](https://developer.apple.com/documentation/authenticationservices)
- [CloudKit Dashboard](https://icloud.developer.apple.com/)
- [Apple Developer Forums](https://developer.apple.com/forums/)