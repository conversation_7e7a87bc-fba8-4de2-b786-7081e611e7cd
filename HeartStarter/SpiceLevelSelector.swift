//
//  SpiceLevelSelector.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct SpiceLevelSelector: View {
    @Binding var selectedSpiceLevel: SpiceLevel
    @State private var isCollapsed = false  // Start expanded for better UX
    
    var body: some View {
        VStack(spacing: 12) {
            Button {
                withAnimation(.easeInOut(duration: 0.25)) {
                    isCollapsed.toggle()
                }
            } label: {
                HStack {
                    Text("Choose Your Spice Level")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if isCollapsed {
                        // Show current selection when collapsed
                        HStack(spacing: 4) {
                            Text(selectedSpiceLevel.emoji)
                                .font(.caption)
                            Text(selectedSpiceLevel.rawValue)
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(selectedSpiceLevel.color.opacity(0.1))
                        )
                    }
                    
                    Image(systemName: isCollapsed ? "chevron.down" : "chevron.up")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)
            
            if !isCollapsed {
                HStack(spacing: 10) {
                    ForEach(SpiceLevel.allCases, id: \.self) { spiceLevel in
                        SpiceLevelButton(
                            spiceLevel: spiceLevel,
                            isSelected: selectedSpiceLevel == spiceLevel,
                            action: {
                                selectedSpiceLevel = spiceLevel
                                // Don't auto-collapse - let user control it
                            }
                        )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 12)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .move(edge: .top)),
                    removal: .opacity.combined(with: .move(edge: .top))
                ))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.gray.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct SpiceLevelButton: View {
    let spiceLevel: SpiceLevel
    let isSelected: Bool
    let action: () -> Void
    
    // Performance optimization: Cache colors to avoid repeated calculations
    private let colorCache = SpiceLevelColorCache.shared
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                Text(spiceLevel.emoji)
                    .font(.system(size: 24))
                    .scaleEffect(isSelected ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.15), value: isSelected)
                
                Text(spiceLevel.rawValue)
                    .font(.system(size: 13))
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? .primary : .secondary)
                
                if isSelected {
                    Text(spiceLevel.description)
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .padding(.horizontal, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? colorCache.backgroundColorFor(spiceLevel) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? colorCache.borderColorFor(spiceLevel) : .gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(.plain)
    }
}

// Performance optimization: Singleton color cache
class SpiceLevelColorCache {
    static let shared = SpiceLevelColorCache()
    
    private let backgroundColors: [SpiceLevel: Color]
    private let borderColors: [SpiceLevel: Color]
    
    private init() {
        // Pre-compute all colors once
        backgroundColors = [
            .mild: .pink.opacity(0.1),
            .medium: .orange.opacity(0.1),
            .spicy: .red.opacity(0.1)
        ]
        
        borderColors = [
            .mild: .pink,
            .medium: .orange,
            .spicy: .red
        ]
    }
    
    func backgroundColorFor(_ spiceLevel: SpiceLevel) -> Color {
        return backgroundColors[spiceLevel] ?? .clear
    }
    
    func borderColorFor(_ spiceLevel: SpiceLevel) -> Color {
        return borderColors[spiceLevel] ?? .gray
    }
}

#Preview {
    SpiceLevelSelector(selectedSpiceLevel: .constant(.medium))
        .padding()
}