//
//  ErrorView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct ErrorView: View {
    let title: String
    let message: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    init(title: String = "Something went wrong", message: String, actionTitle: String? = nil, action: (() -> Void)? = nil) {
        self.title = title
        self.message = message
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            VStack(spacing: 12) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle) {
                    action()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
            }
        }
        .padding()
    }
}

struct CompactErrorView: View {
    let message: String
    let isWarning: Bool
    let onDismiss: () -> Void
    
    init(message: String, isWarning: Bool = false, onDismiss: @escaping () -> Void) {
        self.message = message
        self.isWarning = isWarning
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        HStack {
            Image(systemName: isWarning ? "clock.fill" : "exclamationmark.triangle.fill")
                .foregroundColor(isWarning ? .blue : .orange)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
            
            if !isWarning { // Don't show dismiss button for warnings (cooldowns)
                Button {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        onDismiss()
                    }
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
        }
        .padding()
        .background((isWarning ? Color.blue : Color.orange).opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke((isWarning ? Color.blue : Color.orange).opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal)
    }
}

#Preview("Error View") {
    ErrorView(
        title: "CloudKit Error",
        message: "Unable to sync your data. Please check your iCloud settings and try again.",
        actionTitle: "Retry"
    ) {
        debugLog("Retry tapped")
    }
}

#Preview("Compact Error View") {
    CompactErrorView(message: "Failed to generate flirt. Please try again.") {
        debugLog("Dismissed")
    }
}