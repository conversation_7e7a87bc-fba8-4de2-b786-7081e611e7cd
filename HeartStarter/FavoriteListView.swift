//  FavoriteListView.swift
//  HeartStarter
//
//  Created by the assistant for premium users to view their favorite flirt messages.

import SwiftUI

struct FavoriteListView: View {
    let favorites: [FlirtMessage]
    var onClose: () -> Void
    let onUpgradeRequested: () -> Void

    @EnvironmentObject var statsManager: UserStatsManager
    @State private var showingShareSheet: Bool = false
    @State private var messageToShare: FlirtMessage? = nil
    @State private var textsToShare: [String] = []
    @State private var showCopiedFeedback: Bool = false
    @State private var showingRemoveConfirmation: Bool = false
    @State private var messageToRemove: FlirtMessage? = nil
    @State private var isSelectionMode: Bool = false
    @State private var selectedMessages: Set<String> = []
    @State private var showingBatchRemoveConfirmation: Bool = false

    var body: some View {
        NavigationView {
            if !statsManager.stats.allowsFavorites {
                // Premium Gate View
                VStack(spacing: 24) {
                    Spacer()
                    
                    VStack(spacing: 16) {
                        Image(systemName: "heart.circle.fill")
                            .font(.system(size: 80))
                            .foregroundStyle(LinearGradient(
                                colors: [.pink, .red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                        
                        VStack(spacing: 8) {
                            Text("Favorites")
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text("Save your favorite flirt messages for easy access anytime")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                        }
                    }
                    
                    VStack(spacing: 12) {
                        PremiumFeatureRow(
                            icon: "heart.fill",
                            title: "Save Unlimited Favorites",
                            description: "Keep your best flirt messages",
                            gradient: LinearGradient(colors: [.pink, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
                        )
                        
                        PremiumFeatureRow(
                            icon: "icloud.fill",
                            title: "Sync Across Devices",
                            description: "Access favorites on all your devices",
                            gradient: LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing)
                        )
                        
                        PremiumFeatureRow(
                            icon: "square.and.arrow.up",
                            title: "Easy Sharing",
                            description: "Copy and share your favorites instantly",
                            gradient: LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing)
                        )
                    }
                    .padding(.horizontal, 24)
                    
                    Button(action: {
                        onClose()
                        onUpgradeRequested()
                    }) {
                        Text("Upgrade to Premium")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                LinearGradient(
                                    colors: [.pink, .red],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(12)
                    }
                    .padding(.horizontal, 24)
                    
                    Spacer()
                }
                .navigationTitle("Favorites")
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Close") {
                            onClose()
                        }
                    }
                }
            } else {
                // Regular Favorites List
                List {
                if favorites.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "heart")
                            .font(.system(size: 48))
                            .foregroundStyle(LinearGradient(
                                colors: [.pink, .red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .opacity(0.6)
                        
                        VStack(spacing: 8) {
                            Text("No Favourites Yet")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Text("Tap the heart icon when you find a flirt you love!")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 20)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 60)
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
                } else {
                    ForEach(favorites) { message in
                        HStack(spacing: 12) {
                            // Selection circle
                            if isSelectionMode {
                                Button {
                                    toggleSelection(for: message.id)
                                } label: {
                                    Image(systemName: selectedMessages.contains(message.id) ? "checkmark.circle.fill" : "circle")
                                        .font(.system(size: 22))
                                        .foregroundColor(selectedMessages.contains(message.id) ? .blue : .secondary)
                                }
                                .buttonStyle(.plain)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text(message.text)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                    .fixedSize(horizontal: false, vertical: true)

                                HStack(spacing: 8) {
                                    // Left-side metadata badges
                                    HStack(spacing: 6) {
                                        // Spice Level Badge
                                        HStack(spacing: 3) {
                                            Image(systemName: "flame.fill")
                                                .font(.system(size: 9))
                                            Text(message.spiceLevel.rawValue)
                                                .font(.caption2)
                                                .fontWeight(.medium)
                                        }
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 3)
                                        .background(
                                            Capsule()
                                                .fill(.secondary.opacity(0.1))
                                        )
                                        .foregroundColor(.secondary)
                                        
                                        // Tone Badge
                                        if let tone = message.tone {
                                            HStack(spacing: 3) {
                                                Image(systemName: "face.smiling")
                                                    .font(.system(size: 9))
                                                Text(tone.rawValue)
                                                    .font(.caption2)
                                                    .fontWeight(.medium)
                                            }
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 3)
                                            .background(
                                                Capsule()
                                                    .fill(.secondary.opacity(0.1))
                                            )
                                            .foregroundColor(.secondary)
                                        }
                                        
                                        // Personal Touch Badge
                                        if message.hasPersonalTouch {
                                            HStack(spacing: 3) {
                                                Image(systemName: "person.fill")
                                                    .font(.system(size: 9))
                                                Text("Personal")
                                                    .font(.caption2)
                                                    .fontWeight(.medium)
                                            }
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 3)
                                            .background(
                                                Capsule()
                                                    .fill(.secondary.opacity(0.1))
                                            )
                                            .foregroundColor(.secondary)
                                        }
                                    }

                                    Spacer()

                                    // Right-side actions: Show only in non-selection mode
                                    if !isSelectionMode {
                                        HStack(spacing: 20) {
                                            Button {
                                                copyToClipboard(message.text)
                                            } label: {
                                                Image(systemName: "doc.on.doc")
                                                    .font(.system(size: 16, weight: .semibold))
                                                    .foregroundColor(.blue)
                                            }
                                            .buttonStyle(.plain)
                                            .accessibilityLabel("Copy")

                                            Button {
                                                messageToShare = message
                                                showingShareSheet = true
                                            } label: {
                                                Image(systemName: "square.and.arrow.up")
                                                    .font(.system(size: 16, weight: .semibold))
                                                    .foregroundColor(.green)
                                            }
                                            .buttonStyle(.plain)
                                            .accessibilityLabel("Share")
                                            
                                            Button {
                                                messageToRemove = message
                                                showingRemoveConfirmation = true
                                            } label: {
                                                Image(systemName: "heart.slash")
                                                    .font(.system(size: 16, weight: .semibold))
                                                    .foregroundColor(.red)
                                            }
                                            .buttonStyle(.plain)
                                            .accessibilityLabel("Remove from favorites")
                                        }
                                    }
                                }

                                // Footer date
                                Text(message.createdAt, style: .date)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.vertical, 8)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            if isSelectionMode {
                                toggleSelection(for: message.id)
                            }
                        }
                    }
                    .onDelete(perform: { offsets in
                        if let index = offsets.first {
                            messageToRemove = favorites[index]
                            showingRemoveConfirmation = true
                        }
                    })
                }
            }
            .listStyle(.plain)
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Favorites")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if !favorites.isEmpty {
                        Button(isSelectionMode ? "Cancel" : "Select") {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isSelectionMode.toggle()
                                if !isSelectionMode {
                                    selectedMessages.removeAll()
                                }
                            }
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        onClose()
                    }
                }
            }
            .safeAreaInset(edge: .top, spacing: 0) {
                if showCopiedFeedback {
                    ToastView(
                        message: "Copied!",
                        icon: "checkmark.circle.fill",
                        color: .green,
                        isShowing: $showCopiedFeedback
                    )
                    .padding(.top, 8)
                    .animation(.easeInOut(duration: 0.2), value: showCopiedFeedback)
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if let message = messageToShare {
                    ShareSheet(items: [message.text])
                } else if !textsToShare.isEmpty {
                    ShareSheet(items: textsToShare)
                }
            }
            .alert("Remove from Favourites?", isPresented: $showingRemoveConfirmation) {
                Button("Remove", role: .destructive) {
                    if let message = messageToRemove {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.2)) {
                            statsManager.removeFavorite(message)
                        }
                        let impact = UIImpactFeedbackGenerator(style: .soft)
                        impact.prepare()
                        impact.impactOccurred()
                    }
                    messageToRemove = nil
                }
                
                Button("Cancel", role: .cancel) {
                    messageToRemove = nil
                }
            } message: {
                Text("This flirt will be permanently removed from your favourites.")
            }
            .alert("Remove Selected Favourites?", isPresented: $showingBatchRemoveConfirmation) {
                Button("Remove", role: .destructive) {
                    batchRemoveFavorites()
                }
                
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("These \(selectedMessages.count) flirts will be permanently removed from your favourites.")
            }
            .safeAreaInset(edge: .bottom, spacing: 0) {
                if isSelectionMode && !selectedMessages.isEmpty {
                    BatchActionBar(
                        selectedCount: selectedMessages.count,
                        onCopy: batchCopyMessages,
                        onShare: batchShareMessages,
                        onRemove: {
                            showingBatchRemoveConfirmation = true
                        }
                    )
                }
            }
            }
        }
    }
    
    private func toggleSelection(for messageId: String) {
        if selectedMessages.contains(messageId) {
            selectedMessages.remove(messageId)
        } else {
            selectedMessages.insert(messageId)
        }
    }
    
    private func batchCopyMessages() {
        let selectedTexts = favorites
            .filter { selectedMessages.contains($0.id) }
            .map { $0.text }
            .joined(separator: "\n\n")
        
        UIPasteboard.general.string = selectedTexts
        
        // Show toast feedback
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopiedFeedback = true
        }
        
        // Hide after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopiedFeedback = false
            }
        }
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
        
        // Haptic feedback
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(.success)
    }
    
    private func batchShareMessages() {
        textsToShare = favorites
            .filter { selectedMessages.contains($0.id) }
            .map { $0.text }
        
        messageToShare = nil // Clear single message
        showingShareSheet = true
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
    }
    
    private func batchRemoveFavorites() {
        let messagesToRemove = favorites.filter { selectedMessages.contains($0.id) }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.2)) {
            for message in messagesToRemove {
                statsManager.removeFavorite(message)
            }
        }
        
        let impact = UIImpactFeedbackGenerator(style: .soft)
        impact.prepare()
        impact.impactOccurred()
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
    }
    
    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
        
        // Show toast feedback
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopiedFeedback = true
        }
        
        // Hide after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopiedFeedback = false
            }
        }
        
        // Haptic feedback
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(.success)
    }
}

struct BatchActionBar: View {
    let selectedCount: Int
    let onCopy: () -> Void
    let onShare: () -> Void
    let onRemove: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(height: 0.5)
            
            HStack {
                Text("\(selectedCount) selected")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 24) {
                    Button(action: onCopy) {
                        VStack(spacing: 4) {
                            Image(systemName: "doc.on.doc")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.blue)
                            Text("Copy")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: onShare) {
                        VStack(spacing: 4) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.green)
                            Text("Share")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: onRemove) {
                        VStack(spacing: 4) {
                            Image(systemName: "trash")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.red)
                            Text("Remove")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        FavoriteListView(favorites: [], onClose: {}, onUpgradeRequested: {})
            .environmentObject(UserStatsManager())
    }
}
