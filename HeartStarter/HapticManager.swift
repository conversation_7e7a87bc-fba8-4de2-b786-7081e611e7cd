//
//  HapticManager.swift
//  HeartStarter
//
//  Created by <PERSON> for optimized haptic feedback performance
//

import UIKit
import Combine

@MainActor
class HapticManager: ObservableObject {
    static let shared = HapticManager()
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let softImpact = UIImpactFeedbackGenerator(style: .soft)
    private let rigidImpact = UIImpactFeedbackGenerator(style: .rigid)
    private let selectionFeedback = UISelectionFeedbackGenerator()
    private let notificationFeedback = UINotificationFeedbackGenerator()
    
    private var isWarmedUp = false
    
    private init() {
        // Start warming up haptics in background immediately
        Task.detached(priority: .background) {
            await self.warmUpHaptics()
        }
    }
    
    private func warmUpHaptics() async {
        // Prepare all haptic generators on main actor
        lightImpact.prepare()
        softImpact.prepare()
        rigidImpact.prepare()
        selectionFeedback.prepare()
        notificationFeedback.prepare()
        
        isWarmedUp = true
    }
    
    // MARK: - Public Interface
    
    func lightTap() {
        guard isWarmedUp else { return }
        lightImpact.impactOccurred()
        // Re-prepare for next use
        Task { @MainActor in
            self.lightImpact.prepare()
        }
    }
    
    func softTap() {
        guard isWarmedUp else { return }
        softImpact.impactOccurred()
        Task { @MainActor in
            self.softImpact.prepare()
        }
    }
    
    func rigidTap() {
        guard isWarmedUp else { return }
        rigidImpact.impactOccurred()
        Task { @MainActor in
            self.rigidImpact.prepare()
        }
    }
    
    func selection() {
        guard isWarmedUp else { return }
        selectionFeedback.selectionChanged()
        Task { @MainActor in
            self.selectionFeedback.prepare()
        }
    }
    
    func success() {
        guard isWarmedUp else { return }
        notificationFeedback.notificationOccurred(.success)
        Task { @MainActor in
            self.notificationFeedback.prepare()
        }
    }
    
    func warning() {
        guard isWarmedUp else { return }
        notificationFeedback.notificationOccurred(.warning)
        Task { @MainActor in
            self.notificationFeedback.prepare()
        }
    }
    
    func error() {
        guard isWarmedUp else { return }
        notificationFeedback.notificationOccurred(.error)
        Task { @MainActor in
            self.notificationFeedback.prepare()
        }
    }
}