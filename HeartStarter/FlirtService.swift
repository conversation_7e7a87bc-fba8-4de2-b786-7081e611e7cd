//
//  FlirtService.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import Combine

// MARK: - FlirtService
@MainActor
class FlirtService: ObservableObject {
    @Published var isLoading: Bool = false
    @Published var loadingSource: LoadingSource?
    @Published var errorMessage: String?
    @Published var cooldownMessage: String?
    @Published var showAbuseWarning: Bool = false
    
    // Loading message provider for dynamic loading states
    let loadingMessageProvider = LoadingMessageProvider()
    
    // MARK: - Advanced Components (Lazy Initialization)
    private lazy var cacheManager = CacheManager()
    private lazy var usageTracker = UsageTracker()
    private lazy var moderationService = ModerationService()
    private let userDefaults = UserDefaults.standard
    
    init() {
        setupBindings()
        // DISABLED cache warming for instant UI responsiveness
        // Cache will be populated naturally when users generate messages
    }
    
    private func setupBindings() {
        // Bind usage tracker cooldown state to UI
        usageTracker.$cooldownTimeRemaining
            .sink { [weak self] timeRemaining in
                if timeRemaining > 0 {
                    self?.cooldownMessage = self?.usageTracker.getFriendlyMessage(userStats: UserStats())
                } else {
                    self?.cooldownMessage = nil
                }
            }
            .store(in: &cancellables)
    }
    
    private var cancellables: Set<AnyCancellable> = []
    
    // MARK: - Main Generate Function with Smart Caching & Abuse Prevention
    func generateFlirt(
        spiceLevel: SpiceLevel, 
        tone: FlirtTone?, 
        personalContext: String?, 
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> FlirtMessage? {
        
        // Start loading state
        isLoading = true
        errorMessage = nil
        showAbuseWarning = false
        
        defer { 
            isLoading = false
            loadingSource = nil
            loadingMessageProvider.stopLoading()
        }

        // Reset counters if needed
        userStats.resetCountersIfNeeded()

        // Log current daily counts for visibility (especially for premium users)
        debugLog("🧮 Pre-generation counts – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), dailyFlirtCount=\(userStats.dailyFlirtCount)")
        
        // Check usage limits and cooldowns first
        if !userStats.canGenerateMessage {
            if userStats.isInCooldown {
                cooldownMessage = usageTracker.getFriendlyMessage(userStats: userStats)
                return nil
            } else {
                errorMessage = "Daily limit reached. Upgrade to Premium for unlimited messages!"
                return nil
            }
        }
        
        // Check for abuse warning
        if usageTracker.shouldShowAbuseWarning(userStats: userStats) {
            showAbuseWarning = true
        }
        
        let userTier = userStats.subscriptionTier
        let isPersonalized = personalContext != nil && !personalContext!.isEmpty
        
        // Track the generation attempt
        let canProceed = usageTracker.trackEvent(
            .messageGenerated(spiceLevel: spiceLevel, tone: tone, isPersonalized: isPersonalized),
            userStats: &userStats
        )
        
        guard canProceed else {
            cooldownMessage = usageTracker.getFriendlyMessage(userStats: userStats)
            return nil
        }
        
        var generatedMessage: FlirtMessage?
        
        if isPersonalized {
            // Personalized messages (Premium): Always fresh API calls
            loadingSource = .api
            loadingMessageProvider.startLoading(source: .api)
            
            generatedMessage = await generatePersonalizedMessage(
                spiceLevel: spiceLevel,
                tone: tone,
                personalContext: personalContext!,
                userTier: userTier,
                userStats: &userStats
            )
        } else {
            // Non-personalized messages: Use smart caching with enhanced loading
            generatedMessage = await generateCachedMessageWithEnhancedLoading(
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier,
                seenIds: seenIds,
                userStats: &userStats
            )
        }
        
        // Record successful generation
        if let generatedMessage = generatedMessage {
            userStats.incrementDailyCount()
            if isPersonalized {
                userStats.recordPersonalizedMessage()
            }
            userStats.ingestGeneratedMessage(generatedMessage)

            // Post-generation log to track running total for the day
            debugLog("📈 After-generation counts – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), dailyFlirtCount=\(userStats.dailyFlirtCount)")
        }
        
        return generatedMessage
    }
    
    // MARK: - Smart Cached Message Generation
    private func generateCachedMessage(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> FlirtMessage? {
        
        // Try to get message from cache first
        if let cachedMessage = cacheManager.getUnseenMessage(
            spiceLevel: spiceLevel,
            tone: tone,
            userTier: userTier,
            seenIds: seenIds
        ) {
            // Track cache hit
            _ = usageTracker.trackEvent(.cacheHit(spiceLevel: spiceLevel, tone: tone), userStats: &userStats)
            debugLog("✅ Cache HIT: Returning cached message")
            return cachedMessage
        }
        
        // Cache miss - generate fresh batch
        _ = usageTracker.trackEvent(.cacheMiss(spiceLevel: spiceLevel, tone: tone), userStats: &userStats)
        debugLog("❌ Cache MISS: Generating fresh batch")
        
        // Generate batch if needed
        if cacheManager.needsFreshMessages(
            spiceLevel: spiceLevel,
            tone: tone,
            userTier: userTier,
            seenIds: seenIds
        ) {
            await generateAndCacheBatch(
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier,
                userStats: &userStats
            )
            
            // Try cache again after generation
            if let newMessage = cacheManager.getUnseenMessage(
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier,
                seenIds: seenIds
            ) {
                return newMessage
            }
        }

        // Fallback to sample messages if API fails
        if let error = errorMessage, error.contains("OpenAI API key") {
            return nil
        }
        return generateSampleMessage(spiceLevel: spiceLevel, tone: tone)
    }
    
    // MARK: - Enhanced Cached Message Generation with Loading States
    private func generateCachedMessageWithEnhancedLoading(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> FlirtMessage? {
        
        let startTime = Date()
        
        // Try to get message from cache first
        if let cachedMessage = cacheManager.getUnseenMessage(
            spiceLevel: spiceLevel,
            tone: tone,
            userTier: userTier,
            seenIds: seenIds
        ) {
            // Cache HIT - Show cache loading for minimum 2 seconds
            loadingSource = .cache
            loadingMessageProvider.startLoading(source: .cache)
            
            // Track cache hit
            _ = usageTracker.trackEvent(.cacheHit(spiceLevel: spiceLevel, tone: tone), userStats: &userStats)
            debugLog("✅ Cache HIT: Returning cached message with 2s minimum loading")
            
            // Ensure minimum 2-second loading time
            let elapsedTime = Date().timeIntervalSince(startTime)
            let remainingTime = max(0, 2.0 - elapsedTime)
            if remainingTime > 0 {
                try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
            }
            
            return cachedMessage
        }
        
        // Cache MISS - Use API loading with dynamic messages
        loadingSource = .api
        loadingMessageProvider.startLoading(source: .api)
        
        // Track cache miss
        _ = usageTracker.trackEvent(.cacheMiss(spiceLevel: spiceLevel, tone: tone), userStats: &userStats)
        debugLog("❌ Cache MISS: Generating fresh batch with dynamic loading messages")
        
        // Generate batch if needed
        if cacheManager.needsFreshMessages(
            spiceLevel: spiceLevel,
            tone: tone,
            userTier: userTier,
            seenIds: seenIds
        ) {
            await generateAndCacheBatch(
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier,
                userStats: &userStats
            )
            
            // Try cache again after generation
            if let newMessage = cacheManager.getUnseenMessage(
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier,
                seenIds: seenIds
            ) {
                return newMessage
            }
        }

        // Fallback to sample messages if API fails
        if let error = errorMessage, error.contains("OpenAI API key") {
            return nil
        }
        return generateSampleMessage(spiceLevel: spiceLevel, tone: tone)
    }
    
    private func generateAndCacheBatch(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        userStats: inout UserStats
    ) async {
        do {
            debugLog("🔄 Generating batch of 10 messages for \(spiceLevel.rawValue) \(tone?.rawValue ?? "Any Tone")")
            
            // Premium model usage policy:
            // Use GPT-5 for premium users until they have generated 50 messages today (cached or fresh).
            // After 50 total messages, switch fresh OpenAI calls to GPT-5 Mini for the rest of the day.
            let shouldUsePremiumModel: Bool = {
                if userTier == .premium {
                    let remainingBeforeSwitch = max(0, 50 - userStats.dailyMessagesGenerated)
                    debugLog("🤖 Model policy check – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), remaining GPT-5 allowance today=\(remainingBeforeSwitch)")
                    return userStats.dailyMessagesGenerated < 50
                }
                return false
            }()

            let batchMessages = try await callOpenAIAPI(
                spiceLevel: spiceLevel,
                tone: tone,
                count: 10, // Generate 10 messages per batch
                userTier: userTier,
                userStats: &userStats,
                preferPremiumModel: shouldUsePremiumModel
            )
            
            // Cache the batch
            cacheManager.cacheBatch(
                messages: batchMessages,
                spiceLevel: spiceLevel,
                tone: tone,
                userTier: userTier
            )
            
            debugLog("✅ Successfully cached \(batchMessages.count) messages")
            
        } catch {
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                errorMessage = "Missing OpenAI API key. Add it to OpenAI-Config.plist so we can fetch fresh flirts."
            } else {
                errorMessage = "Failed to generate new flirts. Please try again."
            }
            debugLog("❌ Batch generation failed: \(error)")
        }
    }
    
    // MARK: - Personalized Message Generation
    private func generatePersonalizedMessage(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        personalContext: String,
        userTier: SubscriptionTier,
        userStats: inout UserStats
    ) async -> FlirtMessage? {
        do {
            debugLog("🎯 Generating personalized message with context: \(personalContext.prefix(30))...")
            
            let usePremiumBoost = userTier == .premium && userStats.shouldUsePremiumPersonalTouchBoost()
            let messages = try await callPersonalizedOpenAIAPI(
                spiceLevel: spiceLevel,
                tone: tone,
                personalContext: personalContext,
                userTier: userTier,
                userStats: &userStats,
                usePremiumBoost: usePremiumBoost
            )
            
            if let message = messages.first {
                debugLog("✅ Generated personalized message: \(message.text.prefix(50))...")
                if usePremiumBoost {
                    userStats.consumePremiumPersonalTouchBoost()
                }
                return message
            } else {
                debugLog("⚠️ No personalized message generated, using fallback")
                return generateSampleMessage(spiceLevel: spiceLevel, tone: tone)
            }
            
        } catch {
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                errorMessage = "Add your OpenAI API key to personalize flirts."
                debugLog("❌ Personalized API Error: missing API key")
                return nil
            } else {
                errorMessage = "Failed to generate personalized flirt. Please try again."
                debugLog("❌ Personalized API Error: \(error)")
                return generateSampleMessage(spiceLevel: spiceLevel, tone: tone)
            }
        }
    }
    
    // MARK: - OpenAI API Integration
    private func callOpenAIAPI(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        count: Int = 10,
        userTier: SubscriptionTier,
        userStats: inout UserStats,
        preferPremiumModel: Bool
    ) async throws -> [FlirtMessage] {
        let apiKey = OpenAIConfig.apiKey
        
        debugLog("FlirtService: Attempting OpenAI API call - SpiceLevel: \(spiceLevel), Tone: \(tone?.rawValue ?? "none"), Count: \(count)")
        
        guard !apiKey.isEmpty else {
            errorMessage = "Add your OpenAI API key in OpenAI-Config.plist (or via secure storage) to generate new flirts."
            debugLog("FlirtService: No OpenAI API key configured")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        guard OpenAIConfig.validateAPIKey(apiKey) else {
            errorMessage = "The OpenAI API key format looks invalid. Please double-check it."
            debugLog("FlirtService: Invalid API key format")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        do {
            let openAIService = OpenAIService(apiKey: apiKey)
            let avoidanceList = userStats.recentExclusionList(maxCount: 30)
            let generationResult = try await openAIService.generateFlirtMessages(
                spiceLevel: spiceLevel,
                tone: tone,
                count: count,
                userTier: userTier,
                useOptimalModel: preferPremiumModel,
                avoidPhrases: avoidanceList
            )
            let messageTexts = generationResult.messages
            
            // Track API usage
            let modelUsed = generationResult.model
            _ = usageTracker.trackEvent(
                .apiCall(
                    model: modelUsed,
                    tokenCount: generationResult.usage?.totalTokens ?? count * 50
                ),
                userStats: &userStats
            )
            userStats.recordModelUsage(model: modelUsed)
            if let usage = generationResult.usage {
                let prompt = usage.promptTokens
                let completion = usage.completionTokens
                let total = usage.totalTokens
                debugLog("FlirtService: API usage – prompt: \(prompt), completion: \(completion), total: \(total)")
            }
            if let estimatedCost = generationResult.estimatedCost {
                debugLog("FlirtService: Estimated cost for batch: $\(String(format: "%.6f", estimatedCost))")
            }
            
            debugLog("FlirtService: OpenAI API call successful, received \(messageTexts.count) messages")
            
            // Validate we got meaningful messages
            guard !messageTexts.isEmpty else {
                debugLog("FlirtService: OpenAI returned empty messages, using sample fallback")
                return generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
            }
            
            let moderatedResults = try await moderationService.moderate(messages: messageTexts)
            let safeTexts = moderatedResults.filter { !$0.isFlagged }.map { $0.text }

            if safeTexts.isEmpty {
                errorMessage = "We couldn't generate a safe flirt right now. Please try again with different settings."
                debugLog("FlirtService: Moderation flagged all generated messages")
                return generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
            }

            if safeTexts.count < messageTexts.count {
                debugLog("FlirtService: Moderation filtered \(messageTexts.count - safeTexts.count) message(s)")
            }

            let (freshTexts, familiarTexts) = segregateFreshAndFamiliarTexts(safeTexts, userStats: userStats)
            var finalTexts = freshTexts
            if finalTexts.isEmpty {
                debugLog("FlirtService: All moderated messages already appeared in history; reusing unique familiar set")
                finalTexts = familiarTexts
            }

            // Convert strings to FlirtMessage objects (non-personalized)
            let flirtMessages = finalTexts.map { text in
                FlirtMessage(text: text, spiceLevel: spiceLevel, tone: tone, hasPersonalTouch: false)
            }
            
            debugLog("FlirtService: Successfully created \(flirtMessages.count) moderated FlirtMessage objects")
            return flirtMessages
            
        } catch {
            debugLog("FlirtService: OpenAI API call failed with error: \(error)")
            
            // Log specific error types for better debugging
            if let openAIError = error as? OpenAIError {
                switch openAIError {
                case .missingAPIKey:
                    debugLog("FlirtService: Missing API key - cannot call OpenAI")
                case .invalidAPIKey:
                    debugLog("FlirtService: API key is invalid - check configuration")
                case .apiErrorWithDetails(let code, let details):
                    debugLog("FlirtService: API error \(code) - \(details)")
                case .apiError(let code):
                    debugLog("FlirtService: API error code \(code)")
                default:
                    debugLog("FlirtService: Other OpenAI error - \(openAIError)")
                }
            } else if let moderationError = error as? ModerationError {
                debugLog("FlirtService: Moderation error - \(moderationError.localizedDescription)")
                errorMessage = "We couldn't verify that message was safe. Please try again."
            }
            
            // Fallback to sample messages on errors other than missing key
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                throw openAIError
            } else {
                debugLog("FlirtService: Using sample messages as fallback")
                return generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
            }
        }
    }
    
    // MARK: - Personalized OpenAI API Integration
    private func callPersonalizedOpenAIAPI(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        personalContext: String,
        userTier: SubscriptionTier,
        userStats: inout UserStats,
        count: Int = 1,
        usePremiumBoost: Bool
    ) async throws -> [FlirtMessage] {
        let apiKey = OpenAIConfig.apiKey
        
        debugLog("FlirtService: Attempting personalized OpenAI API call - SpiceLevel: \(spiceLevel), Tone: \(tone?.rawValue ?? "none"), Context: '\(personalContext)'")
        
        guard !apiKey.isEmpty else {
            errorMessage = "Add your OpenAI API key in OpenAI-Config.plist to personalize flirts."
            debugLog("FlirtService: No OpenAI API key configured for personalized request")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        guard OpenAIConfig.validateAPIKey(apiKey) else {
            errorMessage = "The OpenAI API key format looks invalid. Please double-check it."
            debugLog("FlirtService: Invalid API key format for personalized request")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        do {
            let openAIService = OpenAIService(apiKey: apiKey)
            let avoidanceList = userStats.recentExclusionList(maxCount: 30)
            let generationResult = try await openAIService.generatePersonalizedFlirtMessages(
                spiceLevel: spiceLevel,
                tone: tone,
                personalContext: personalContext,
                count: count,
                userTier: userTier,
                useOptimalModel: usePremiumBoost,
                avoidPhrases: avoidanceList
            )
            let messageTexts = generationResult.messages
            
            // Track API usage with higher token estimate for personalized
            let modelUsed = generationResult.model
            _ = usageTracker.trackEvent(
                .apiCall(
                    model: modelUsed,
                    tokenCount: generationResult.usage?.totalTokens ?? count * 100
                ),
                userStats: &userStats
            )
            userStats.recordModelUsage(model: modelUsed)
            if let usage = generationResult.usage {
                debugLog("FlirtService: Personalized API usage – prompt: \(usage.promptTokens), completion: \(usage.completionTokens), total: \(usage.totalTokens)")
            }
            if let estimatedCost = generationResult.estimatedCost {
                debugLog("FlirtService: Personalized estimated cost: $\(String(format: "%.6f", estimatedCost))")
            }
            
            debugLog("FlirtService: Personalized OpenAI API call successful, received \(messageTexts.count) messages")
            
            // Validate we got meaningful messages
            guard !messageTexts.isEmpty else {
                debugLog("FlirtService: Personalized OpenAI returned empty messages, using sample fallback")
                let sampleMessages = generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
                return sampleMessages
            }
            
            let moderatedResults = try await moderationService.moderate(messages: messageTexts)
            let safeTexts = moderatedResults.filter { !$0.isFlagged }.map { $0.text }

            if safeTexts.isEmpty {
                errorMessage = "That personal touch produced something we can't share. Try different details."
                debugLog("FlirtService: Personalized moderation flagged all messages")
                return generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
            }

            if safeTexts.count < messageTexts.count {
                debugLog("FlirtService: Personalized moderation filtered \(messageTexts.count - safeTexts.count) message(s)")
            }

            let (freshTexts, familiarTexts) = segregateFreshAndFamiliarTexts(safeTexts, userStats: userStats)
            var finalTexts = freshTexts
            if finalTexts.isEmpty {
                debugLog("FlirtService: Personalized history filtered all moderated messages; reusing unique familiar set")
                finalTexts = familiarTexts
            }

            // Convert strings to FlirtMessage objects (personalized)
            let flirtMessages = finalTexts.map { text in
                FlirtMessage(text: text, spiceLevel: spiceLevel, tone: tone, hasPersonalTouch: true)
            }
            
            debugLog("FlirtService: Successfully created \(flirtMessages.count) moderated personalized FlirtMessage objects")
            return flirtMessages
            
        } catch {
            debugLog("FlirtService: Personalized OpenAI API call failed with error: \(error)")
            
            // Log specific error types for better debugging
            if let openAIError = error as? OpenAIError {
                switch openAIError {
                case .missingAPIKey:
                    debugLog("FlirtService: Missing API key for personalized request")
                case .invalidAPIKey:
                    debugLog("FlirtService: API key is invalid for personalized request - check configuration")
                case .apiErrorWithDetails(let code, let details):
                    debugLog("FlirtService: Personalized API error \(code) - \(details)")
                case .apiError(let code):
                    debugLog("FlirtService: Personalized API error code \(code)")
                default:
                    debugLog("FlirtService: Other personalized OpenAI error - \(openAIError)")
                }
            } else if let moderationError = error as? ModerationError {
                debugLog("FlirtService: Personalized moderation error - \(moderationError.localizedDescription)")
                errorMessage = "We couldn't verify that message was safe. Please try again."
            }
            
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                throw openAIError
            } else {
                debugLog("FlirtService: Using sample messages as fallback for personalized request")
                return generateSampleMessages(spiceLevel: spiceLevel, tone: tone, count: count)
            }
        }
    }
    
    // MARK: - Sample Message Generation & Fallback
    private func generateSampleMessage(spiceLevel: SpiceLevel, tone: FlirtTone?) -> FlirtMessage {
        let messages = getSampleMessagesArray(spiceLevel: spiceLevel, tone: tone)
        let text = messages.randomElement() ?? "You're absolutely amazing! ✨"
        return FlirtMessage(text: text, spiceLevel: spiceLevel, tone: tone, hasPersonalTouch: false)
    }
    
    private func generateSampleMessages(spiceLevel: SpiceLevel, tone: FlirtTone?, count: Int) -> [FlirtMessage] {
        let messages = getSampleMessagesArray(spiceLevel: spiceLevel, tone: tone)
        return messages.prefix(count).map { text in
            FlirtMessage(text: text, spiceLevel: spiceLevel, tone: tone, hasPersonalTouch: false)
        }
    }
    
    private func getSampleMessagesArray(spiceLevel: SpiceLevel, tone: FlirtTone?) -> [String] {
            switch (spiceLevel, tone) {
            case (.mild, .witty), (.mild, nil):
                return [
                    "Are you a magician? Because whenever I look at you, everyone else disappears. ✨",
                    "I must be a snowflake, because I've fallen for you. ❄️",
                    "Do you have a map? I keep getting lost in your eyes. 🗺️",
                    "Are you Wi-Fi? Because I'm really feeling a connection. 📶",
                    "If you were a vegetable, you'd be a cute-cumber. 🥒"
                ]
            case (.mild, .romantic):
                return [
                    "Every time I see you, you make my heart skip a beat. 💕",
                    "You're the reason I believe in love at first sight. 🌹",
                    "Your smile is my favorite notification. 😊",
                    "I could spend forever just talking with you. ✨",
                    "You make ordinary moments feel magical. 🌟"
                ]
            case (.mild, .cheesy):
                return [
                    "Are you made of copper and tellurium? Because you're Cu-Te! 🧪",
                    "Do you like Star Wars? Because Yoda one for me! ⭐",
                    "Are you a banana? Because I find you a-peel-ing! 🍌",
                    "Do you work at Starbucks? Because I like you a latte! ☕",
                    "Are you a parking ticket? Because you've got FINE written all over you! 🎫"
                ]
            case (.medium, .witty), (.medium, nil):
                return [
                    "I'm not a photographer, but I can definitely picture us together. 📸",
                    "Are you Google? Because you have everything I've been searching for. 🔍",
                    "I must be a time traveler, because I see you in my future. ⏰",
                    "Do you believe in love at first sight, or should I walk by again? 👀",
                    "Are you a loan from a bank? Because you have my interest. 💰"
                ]
            case (.medium, .romantic):
                return [
                    "I'd choose you in every lifetime, in every universe. 💫",
                    "Your voice is my favorite sound in the world. 🎵",
                    "I want to be the reason behind your smile. 😍",
                    "You're not just beautiful, you're captivating. ✨",
                    "I could get lost in conversation with you for hours. 💭"
                ]
            case (.medium, .cheesy):
                return [
                    "If you were a fruit, you'd be a fineapple! 🍍",
                    "Are you a campfire? Because you're hot and I want s'more! 🔥",
                    "Do you like raisins? How about a date? 📅",
                    "Are you Australian? Because you meet all of my koala-fications! 🐨",
                    "Is your name Google? Because you've got everything I'm searching for! 🔎"
                ]
            case (.spicy, .witty), (.spicy, nil):
                return [
                    "I'm not saying you're the best catch, but you're definitely worth the chase. 😏",
                    "Are you a dictionary? Because you add meaning to my life... and you're pretty hot too. 📚",
                    "I must be lost, because heaven is a long way from here, and you're right in front of me. 😈",
                    "Do you have a sunburn, or are you always this hot? 🌞",
                    "I'm not a genie, but I can make your dreams come true tonight. ✨"
                ]
            case (.spicy, .romantic):
                return [
                    "I want to be the reason you look down at your phone and smile. 📱💕",
                    "You're the missing piece I never knew I needed. 🧩",
                    "I could spend eternity getting lost in your eyes. 👁️‍🗨️",
                    "You don't just make my heart race, you make my soul come alive. ⚡",
                    "I want to know every story behind that beautiful smile. 😘"
                ]
            case (.spicy, .cheesy):
                return [
                    "Are you a microwave? Because you make my heart go beep beep beep! 📱",
                    "Do you work at Dick's? Because you're sporting the goods! 🏈",
                    "Are you a beaver? Because daaaaam! 🦫",
                    "If you were a burger at McDonald's, you'd be the McGorgeous! 🍔",
                    "Are you a volcano? Because I lava you! 🌋"
                ]
            }
    }
    
    // MARK: - Cache Warming & Analytics
    private func warmUpCacheIfNeeded() {
        Task {
            // Warm up cache for free tier initially
            cacheManager.warmUpCache(for: .free) { [weak self] keysToWarmUp in
                Task { @MainActor in
                    await self?.warmUpCacheForKeys(keysToWarmUp)
                }
            }
        }
    }
    
    private func warmUpCacheForKeys(_ keys: [CacheKey]) async {
        debugLog("🔥 Warming up cache for \(keys.count) key combinations")
        
        var dummyStats = UserStats() // Dummy stats for warming
        
        for key in keys.prefix(3) { // Limit to 3 keys to avoid excessive API calls
            await generateAndCacheBatch(
                spiceLevel: key.spiceLevel,
                tone: key.tone,
                userTier: key.userTier,
                userStats: &dummyStats
            )
            
            // Small delay between batch generations
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        }
        
        debugLog("🎯 Cache warm-up completed")
    }
    
    // MARK: - Analytics & Debug
    func getCachePerformanceInfo() -> String {
        return cacheManager.getCacheInfo()
    }
    
    func getUsageAnalytics() -> [String: Any] {
        return usageTracker.getAnalyticsData()
    }
    
    func estimatedCostSavings() -> Double {
        return cacheManager.estimatedCostSavings()
    }
    
    // MARK: - Public Cache Management
    func clearAllCaches() {
        cacheManager.clearCache()
        debugLog("🗑️ All caches cleared")
    }
    
    func preloadCacheForUser(userTier: SubscriptionTier) {
        Task {
            cacheManager.warmUpCache(for: userTier) { [weak self] keysToWarmUp in
                Task { @MainActor in
                    await self?.warmUpCacheForKeys(keysToWarmUp)
                }
            }
        }
    }

    // MARK: - Helper Functions
    private func segregateFreshAndFamiliarTexts(_ texts: [String], userStats: UserStats) -> ([String], [String]) {
        var fresh: [String] = []
        var familiar: [String] = []
        var seenFingerprints: Set<String> = []

        for text in texts {
            let fingerprint = normalizeMessage(text)
            guard !fingerprint.isEmpty else { continue }
            if !seenFingerprints.insert(fingerprint).inserted {
                continue
            }

            if userStats.hasSeenMessageText(text) {
                familiar.append(text)
            } else {
                fresh.append(text)
            }
        }

        return (fresh, familiar)
    }

    private func normalizeMessage(_ text: String) -> String {
        text
            .folding(options: [.diacriticInsensitive, .caseInsensitive], locale: .current)
            .components(separatedBy: CharacterSet.alphanumerics.inverted)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
    }
}
