//
//  CacheManager.swift
//  HeartStarter
//
//  Created by <PERSON> on 20/09/2025.
//

import Foundation
import Combine

// MARK: - Cache Key Structure
struct CacheKey: Hashable, Codable {
    let spiceLevel: SpiceLevel
    let tone: FlirtTone?
    let userTier: SubscriptionTier
    let isPersonalized: Bool
    
    init(spiceLevel: SpiceLevel, tone: FlirtTone?, userTier: SubscriptionTier, isPersonalized: Bool = false) {
        self.spiceLevel = spiceLevel
        self.tone = tone
        self.userTier = userTier
        self.isPersonalized = isPersonalized
    }
    
    var description: String {
        let toneStr = tone?.rawValue ?? "Any"
        let personalizedStr = isPersonalized ? "_Personalized" : ""
        return "\(spiceLevel.rawValue)_\(toneStr)_\(userTier.rawValue)\(personalizedStr)"
    }
}

// MARK: - Cache Entry
struct CacheEntry: Codable {
    let messages: [FlirtMessage]
    let createdAt: Date
    let lastUsed: Date
    let usageCount: Int
    let batchId: String
    
    init(messages: [FlirtMessage]) {
        self.messages = messages
        self.createdAt = Date()
        self.lastUsed = Date()
        self.usageCount = 0
        self.batchId = UUID().uuidString
    }
    
    func updatingUsage() -> CacheEntry {
        return CacheEntry(
            messages: messages,
            createdAt: createdAt,
            lastUsed: Date(),
            usageCount: usageCount + 1,
            batchId: batchId
        )
    }
    
    private init(messages: [FlirtMessage], createdAt: Date, lastUsed: Date, usageCount: Int, batchId: String) {
        self.messages = messages
        self.createdAt = createdAt
        self.lastUsed = lastUsed
        self.usageCount = usageCount
        self.batchId = batchId
    }
}

// MARK: - Cache Statistics
struct CacheStatistics: Codable {
    var totalHits: Int = 0
    var totalMisses: Int = 0
    var totalAPICallsSaved: Int = 0
    var averageBatchSize: Double = 0
    var lastResetDate: Date = Date()
    
    var hitRate: Double {
        let total = totalHits + totalMisses
        return total > 0 ? Double(totalHits) / Double(total) : 0.0
    }
    
    mutating func recordHit() {
        totalHits += 1
        totalAPICallsSaved += 1
    }
    
    mutating func recordMiss() {
        totalMisses += 1
    }
    
    mutating func updateBatchSize(_ size: Int) {
        averageBatchSize = (averageBatchSize + Double(size)) / 2.0
    }
}

// MARK: - Cache Manager
@MainActor
class CacheManager: ObservableObject {
    @Published var statistics = CacheStatistics()
    
    private var cache: [CacheKey: CacheEntry] = [:]
    private let userDefaults = UserDefaults.standard
    private let cacheKey = "HeartStarter_AdvancedCache_v2"
    private let statisticsKey = "HeartStarter_CacheStats_v2"
    private let maxCacheSize = 200 // Maximum cache entries
    private let maxBatchAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    private let batchSize = 10 // Standard batch size
    
    // MARK: - Cache warming settings
    private let warmupBatchSize = 5
    private var isWarmingUp = false
    
    init() {
        loadCache()
        loadStatistics()
        cleanupExpiredEntries()
    }
    
    // MARK: - Public Interface
    
    /// Get an unseen message from cache, or nil if no suitable message exists
    func getUnseenMessage(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        seenIds: Set<String>
    ) -> FlirtMessage? {
        
        let key = CacheKey(spiceLevel: spiceLevel, tone: tone, userTier: userTier)
        
        guard let entry = cache[key] else {
            statistics.recordMiss()
            debugLog("🔍 Cache MISS for key: \(key.description)")
            return nil
        }
        
        // Filter out seen messages
        let unseenMessages = entry.messages.filter { !seenIds.contains($0.id) }
        
        guard !unseenMessages.isEmpty else {
            statistics.recordMiss()
            debugLog("🔍 Cache MISS (all messages seen) for key: \(key.description)")
            return nil
        }
        
        // Update usage statistics
        cache[key] = entry.updatingUsage()
        statistics.recordHit()
        
        let selectedMessage = unseenMessages.randomElement()
        debugLog("🎯 Cache HIT for key: \(key.description), returning message: \(selectedMessage?.text.prefix(30) ?? "nil")...")
        
        saveCache()
        saveStatistics()
        
        return selectedMessage
    }
    
    /// Cache a batch of messages
    func cacheBatch(
        messages: [FlirtMessage],
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier
    ) {
        let key = CacheKey(spiceLevel: spiceLevel, tone: tone, userTier: userTier)
        let entry = CacheEntry(messages: messages)
        
        cache[key] = entry
        statistics.updateBatchSize(messages.count)
        
        debugLog("💾 Cached \(messages.count) messages for key: \(key.description)")
        
        // Enforce cache size limits
        enforceCacheSizeLimits()
        
        saveCache()
        saveStatistics()
    }
    
    /// Check if we need fresh messages for a given configuration
    func needsFreshMessages(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        seenIds: Set<String>
    ) -> Bool {
        
        let key = CacheKey(spiceLevel: spiceLevel, tone: tone, userTier: userTier)
        
        guard let entry = cache[key] else {
            return true // No cache entry exists
        }
        
        // Check if cache is expired
        if Date().timeIntervalSince(entry.createdAt) > maxBatchAge {
            cache.removeValue(forKey: key)
            saveCache()
            return true
        }
        
        // Check if we have enough unseen messages (keep at least 3 in reserve)
        let unseenCount = entry.messages.filter { !seenIds.contains($0.id) }.count
        return unseenCount < 3
    }
    
    /// Get cache information for debugging
    func getCacheInfo() -> String {
        let totalEntries = cache.count
        let totalMessages = cache.values.reduce(0) { $0 + $1.messages.count }
        let hitRate = statistics.hitRate * 100
        
        return """
        📊 Cache Statistics:
        • Entries: \(totalEntries)/\(maxCacheSize)
        • Total Messages: \(totalMessages)
        • Hit Rate: \(String(format: "%.1f", hitRate))%
        • API Calls Saved: \(statistics.totalAPICallsSaved)
        """
    }
    
    /// Warm up cache with essential message types
    func warmUpCache(for userTier: SubscriptionTier, completion: @escaping ([CacheKey]) -> Void) {
        guard !isWarmingUp else { return }
        isWarmingUp = true
        
        var keysToWarmUp: [CacheKey] = []
        
        // Warm up cache for all spice levels
        for spiceLevel in SpiceLevel.allCases {
            if userTier == .free {
                // Free users get random tone only
                let key = CacheKey(spiceLevel: spiceLevel, tone: nil, userTier: userTier)
                if needsFreshMessages(spiceLevel: spiceLevel, tone: nil, userTier: userTier, seenIds: Set()) {
                    keysToWarmUp.append(key)
                }
            } else {
                // Premium users get all tones + random
                let tones: [FlirtTone?] = [nil, .witty, .romantic, .cheesy]
                for tone in tones {
                    let key = CacheKey(spiceLevel: spiceLevel, tone: tone, userTier: userTier)
                    if needsFreshMessages(spiceLevel: spiceLevel, tone: tone, userTier: userTier, seenIds: Set()) {
                        keysToWarmUp.append(key)
                    }
                }
            }
        }
        
        isWarmingUp = false
        completion(keysToWarmUp)
    }
    
    // MARK: - Cache Management
    
    /// Clear all cached messages
    func clearCache() {
        cache.removeAll()
        statistics = CacheStatistics()
        
        userDefaults.removeObject(forKey: cacheKey)
        userDefaults.removeObject(forKey: statisticsKey)
        
        debugLog("🗑️ Cache cleared completely")
    }
    
    /// Remove expired cache entries
    func cleanupExpiredEntries() {
        let now = Date()
        let expiredKeys = cache.compactMap { (key, entry) -> CacheKey? in
            return now.timeIntervalSince(entry.createdAt) > maxBatchAge ? key : nil
        }
        
        for key in expiredKeys {
            cache.removeValue(forKey: key)
        }
        
        if !expiredKeys.isEmpty {
            debugLog("🧹 Cleaned up \(expiredKeys.count) expired cache entries")
            saveCache()
        }
    }
    
    /// Enforce cache size limits using LRU eviction
    private func enforceCacheSizeLimits() {
        guard cache.count > maxCacheSize else { return }
        
        // Sort by last used date (LRU)
        let sortedEntries = cache.sorted { $0.value.lastUsed < $1.value.lastUsed }
        let entriesToRemove = sortedEntries.prefix(cache.count - maxCacheSize)
        
        for (key, _) in entriesToRemove {
            cache.removeValue(forKey: key)
        }
        
        debugLog("♻️ Evicted \(entriesToRemove.count) LRU cache entries")
    }
    
    // MARK: - Persistence
    
    private func saveCache() {
        do {
            let data = try JSONEncoder().encode(cache)
            userDefaults.set(data, forKey: cacheKey)
        } catch {
            debugLog("❌ Failed to save cache: \(error)")
        }
    }
    
    private func loadCache() {
        guard let data = userDefaults.data(forKey: cacheKey) else { return }
        
        do {
            cache = try JSONDecoder().decode([CacheKey: CacheEntry].self, from: data)
            debugLog("✅ Loaded cache with \(cache.count) entries")
        } catch {
            debugLog("❌ Failed to load cache: \(error)")
            cache = [:]
        }
    }
    
    private func saveStatistics() {
        do {
            let data = try JSONEncoder().encode(statistics)
            userDefaults.set(data, forKey: statisticsKey)
        } catch {
            debugLog("❌ Failed to save cache statistics: \(error)")
        }
    }
    
    private func loadStatistics() {
        guard let data = userDefaults.data(forKey: statisticsKey) else { return }
        
        do {
            statistics = try JSONDecoder().decode(CacheStatistics.self, from: data)
            debugLog("✅ Loaded cache statistics - Hit rate: \(String(format: "%.1f", statistics.hitRate * 100))%")
        } catch {
            debugLog("❌ Failed to load cache statistics: \(error)")
            statistics = CacheStatistics()
        }
    }
}

// MARK: - Cache Manager Extensions
extension CacheManager {
    
    /// Get performance metrics for analytics
    func getPerformanceMetrics() -> [String: Any] {
        return [
            "cache_hit_rate": statistics.hitRate,
            "total_hits": statistics.totalHits,
            "total_misses": statistics.totalMisses,
            "api_calls_saved": statistics.totalAPICallsSaved,
            "cache_entries": cache.count,
            "average_batch_size": statistics.averageBatchSize
        ]
    }
    
    /// Estimate cost savings from caching
    func estimatedCostSavings(costPerAPICall: Double = 0.0016) -> Double {
        return Double(statistics.totalAPICallsSaved) * costPerAPICall
    }
    
    /// Get messages remaining in cache for a specific key
    func getRemainingMessagesCount(
        spiceLevel: SpiceLevel,
        tone: FlirtTone?,
        userTier: SubscriptionTier,
        seenIds: Set<String>
    ) -> Int {
        let key = CacheKey(spiceLevel: spiceLevel, tone: tone, userTier: userTier)
        
        guard let entry = cache[key] else { return 0 }
        
        return entry.messages.filter { !seenIds.contains($0.id) }.count
    }
}