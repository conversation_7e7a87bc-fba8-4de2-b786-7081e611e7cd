//
//  ToneSelector.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct ToneSelector: View {
    @Binding var selectedTone: FlirtTone?
    let isLocked: Bool
    let onUpgradeRequested: () -> Void
    @State private var isCollapsed = false  // Start expanded for better UX
    
    var body: some View {
        VStack(spacing: 12) {
            Button {
                withAnimation(.easeInOut(duration: 0.25)) {
                    isCollapsed.toggle()
                }
            } label: {
                HStack {
                    Text("Choose Your Tone")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if isLocked {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 10))
                            .foregroundStyle(LinearGradient(
                                colors: [.yellow, .orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    }
                    
                    Spacer()
                    
                    if isCollapsed {
                        // Show current selection when collapsed
                        HStack(spacing: 4) {
                            if let tone = selectedTone {
                                Text(tone.emoji)
                                    .font(.caption)
                                Text(tone.rawValue)
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("✨")
                                    .font(.caption)
                                Text("Any")
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(.purple.opacity(0.1))
                        )
                    }
                    
                    Image(systemName: isCollapsed ? "chevron.down" : "chevron.up")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)
            
            if !isCollapsed {
                HStack(spacing: 6) {
                // "Any" option
                ToneOptionCard(
                    title: "Any",
                    emoji: "✨",
                    isSelected: selectedTone == nil,
                    isLocked: isLocked,
                    action: {
                        if isLocked {
                            onUpgradeRequested()
                        } else {
                            selectedTone = nil
                            // Don't auto-collapse - let user control it
                        }
                    }
                )
                
                ForEach(FlirtTone.allCases, id: \.self) { tone in
                    ToneOptionCard(
                        title: tone.rawValue,
                        emoji: tone.emoji,
                        isSelected: selectedTone == tone,
                        isLocked: isLocked,
                        action: {
                            if isLocked {
                                onUpgradeRequested()
                            } else {
                                selectedTone = tone
                                // Don't auto-collapse - let user control it
                            }
                        }
                    )
                }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 12)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .move(edge: .top)),
                    removal: .opacity.combined(with: .move(edge: .top))
                ))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isLocked ? Color.orange.opacity(0.05) : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isLocked ? Color.orange.opacity(0.3) : .gray.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct ToneOptionCard: View {
    let title: String
    let emoji: String
    let isSelected: Bool
    let isLocked: Bool
    let action: () -> Void
    
    // Performance optimization: Cache colors to avoid repeated calculations
    private let colorCache = ToneColorCache.shared
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                Text(emoji)
                    .font(.system(size: 24))
                    .scaleEffect(isSelected ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.15), value: isSelected)
                
                Text(title)
                    .font(.system(size: 13))
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isLocked && !isSelected ? .secondary.opacity(0.6) : (isSelected ? .primary : .secondary))
                
                if isSelected {
                    Text(toneDescription)
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .padding(.horizontal, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? colorCache.backgroundColorFor(title) : (isLocked ? Color.gray.opacity(0.05) : Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? colorCache.borderColorFor(title) : (isLocked ? Color.gray.opacity(0.3) : .gray.opacity(0.3)), lineWidth: isSelected ? 2 : 1)
                    )
            )
            .opacity(isLocked && !isSelected ? 0.6 : 1.0)
        }
        .buttonStyle(.plain)
    }
    
    private var toneDescription: String {
        return colorCache.descriptionFor(title)
    }
}

// Performance optimization: Singleton color cache for tones
class ToneColorCache {
    static let shared = ToneColorCache()
    
    private let backgroundColors: [String: Color]
    private let borderColors: [String: Color]
    private let descriptions: [String: String]
    
    private init() {
        // Pre-compute all colors and descriptions once
        backgroundColors = [
            "Any": .purple.opacity(0.1),
            "Witty": .blue.opacity(0.1),
            "Romantic": .pink.opacity(0.1),
            "Cheesy": .orange.opacity(0.1)
        ]
        
        borderColors = [
            "Any": .purple,
            "Witty": .blue,
            "Romantic": .pink,
            "Cheesy": .orange
        ]
        
        descriptions = [
            "Any": "Surprise",
            "Witty": "Clever",
            "Romantic": "Sweet",
            "Cheesy": "Corny"
        ]
    }
    
    func backgroundColorFor(_ tone: String) -> Color {
        return backgroundColors[tone] ?? .blue.opacity(0.1)
    }
    
    func borderColorFor(_ tone: String) -> Color {
        return borderColors[tone] ?? .blue
    }
    
    func descriptionFor(_ tone: String) -> String {
        return descriptions[tone] ?? ""
    }
}

#Preview {
    VStack(spacing: 20) {
        ToneSelector(
            selectedTone: .constant(.witty),
            isLocked: false,
            onUpgradeRequested: {}
        )
        .padding()
        
        ToneSelector(
            selectedTone: .constant(nil),
            isLocked: true,
            onUpgradeRequested: {}
        )
        .padding()
    }
}