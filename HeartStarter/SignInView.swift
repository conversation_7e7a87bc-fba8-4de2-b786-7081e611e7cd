//
//  SignInView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI
import AuthenticationServices
import WebKit

struct SignInView: View {
    @ObservedObject var authService: AuthenticationService
    @State private var showingPrivacyPolicy = false
    @State private var showingTerms = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [
                        Color.pink.opacity(0.1),
                        Color.purple.opacity(0.05),
                        Color.clear
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 32) {
                        Spacer(minLength: geometry.size.height * 0.1)
                        
                        // App Logo and Branding
                        VStack(spacing: 24) {
                            // Heart Icon
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [.pink.opacity(0.2), .red.opacity(0.1)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 120, height: 120)
                                
                                Image(systemName: "heart.text.square.fill")
                                    .font(.system(size: 60, weight: .medium))
                                    .foregroundStyle(
                                        LinearGradient(
                                            colors: [.pink, .red],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            }
                            
                            VStack(spacing: 12) {
                                Text("HeartStarter")
                                    .font(.system(size: 36, weight: .bold, design: .rounded))
                                    .foregroundStyle(
                                        LinearGradient(
                                            colors: [.pink, .red],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                
                                Text("AI-powered flirt messages to charm your crush")
                                    .font(.title3)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, 32)
                                
                                Text("💕")
                                    .font(.largeTitle)
                            }
                        }
                        
                        // Features List
                        VStack(spacing: 20) {
                            FeatureRow(
                                icon: "sparkles",
                                title: "AI-Generated Messages",
                                description: "Get unique, personalized flirt messages"
                            )
                            
                            FeatureRow(
                                icon: "heart.fill",
                                title: "Save Favorites",
                                description: "Keep your best messages synced across devices"
                            )
                            
                            FeatureRow(
                                icon: "icloud.fill",
                                title: "Cloud Sync",
                                description: "Your data stays safe and synced"
                            )
                            
                            FeatureRow(
                                icon: "lock.shield",
                                title: "Privacy First",
                                description: "Secure Sign in with Apple"
                            )
                        }
                        .padding(.horizontal, 24)
                        
                        // Sign In Section
                        VStack(spacing: 20) {
                            // Sign in with Apple Button
                            SignInWithAppleButton(
                                onRequest: { request in
                                    request.requestedScopes = [.fullName, .email]
                                },
                                onCompletion: { _ in
                                    // Handle in AuthenticationService
                                }
                            )
                            .signInWithAppleButtonStyle(.black)
                            .frame(height: 50)
                            .frame(maxWidth: 375)
                            .cornerRadius(25)
                            .onTapGesture {
                                authService.signInWithApple()
                            }
                            .disabled(authService.isLoading)
                            
                            // Loading state
                            if authService.isLoading {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Signing in...")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                .padding()
                            }
                            
                            // Error message
                            if let errorMessage = authService.errorMessage {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                    Text(errorMessage)
                                        .font(.subheadline)
                                        .foregroundColor(.red)
                                }
                                .padding()
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(12)
                            }
                            
                            // Terms and Privacy
                            VStack(spacing: 8) {
                                Text("By signing in, you agree to our")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 4) {
                                    Button("Terms of Service") {
                                        showingTerms = true
                                    }
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    
                                    Text("and")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Button("Privacy Policy") {
                                        showingPrivacyPolicy = true
                                    }
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                }
                            }
                        }
                        .padding(.horizontal, 24)
                        
                        Spacer(minLength: 32)
                    }
                }
            }
        }
        .sheet(isPresented: $showingPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showingTerms) {
            TermsOfServiceView()
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.pink)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
    }
}

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            Group {
                if let url = URL(string: "https://sites.google.com/view/heartstarter-privacy") {
                    WebView(url: url)
                } else {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        Text("Unable to load Privacy Policy")
                            .font(.headline)
                        Text("Please try again later")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Privacy Policy")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct TermsOfServiceView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            Group {
                if let url = URL(string: "https://sites.google.com/view/heartstarter-terms") {
                    WebView(url: url)
                } else {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        Text("Unable to load Terms of Use")
                            .font(.headline)
                        Text("Please try again later")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Terms of Use")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct WebView: UIViewRepresentable {
    let url: URL
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            debugLog("Failed to load web page: \(error.localizedDescription)")
        }
    }
}

#Preview {
    SignInView(authService: AuthenticationService())
}