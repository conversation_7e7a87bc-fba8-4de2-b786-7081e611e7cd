//
//  PremiumFeaturesList.swift
//  HeartStarter
//
//  Created by <PERSON> for reusable premium features display.
//

import SwiftUI

struct PremiumFeaturesList: View {
    let showComparison: Bool
    
    init(showComparison: Bool = false) {
        self.showComparison = showComparison
    }
    
    var body: some View {
        LazyVStack(spacing: 12) {
            PremiumFeatureRow(
                icon: "infinity",
                title: "Unlimited Daily Messages",
                description: showComparison ? "Generate unlimited flirt messages every day (Free: 3/day)" : "Generate unlimited flirt messages every day",
                gradient: LinearGradient(colors: [.blue, .cyan], startPoint: .leading, endPoint: .trailing)
            )
            
            PremiumFeatureRow(
                icon: "face.smiling",
                title: "All Tone Options",
                description: showComparison ? "Access Witty, Romantic, and Cheesy tones (Free: Random only)" : "Access Witty, Romantic, and Cheesy tones",
                gradient: LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing)
            )
            
            PremiumFeatureRow(
                icon: "wand.and.stars",
                title: "Personal Touch",
                description: "Add personal context for customized flirt messages",
                gradient: LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing)
            )
            
            PremiumFeatureRow(
                icon: "heart.fill",
                title: "Save Favorite Messages",
                description: "Build your personal collection with cloud sync",
                gradient: LinearGradient(colors: [.red, .pink], startPoint: .leading, endPoint: .trailing)
            )
            
            PremiumFeatureRow(
                icon: "icloud.fill",
                title: "Cross-Device Sync",
                description: "Access favorites and stats on all your devices",
                gradient: LinearGradient(colors: [.orange, .yellow], startPoint: .leading, endPoint: .trailing)
            )
            
            PremiumFeatureRow(
                icon: "sparkles",
                title: "Priority Generation",
                description: "Get fresh, unique messages with anti-repetition",
                gradient: LinearGradient(colors: [.indigo, .blue], startPoint: .leading, endPoint: .trailing)
            )
        }
    }
}

struct DetailedPremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let gradient: LinearGradient
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(gradient)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 1) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        Text("Premium Features")
            .font(.headline)
        
        PremiumFeaturesList(showComparison: true)
            .padding()
    }
}