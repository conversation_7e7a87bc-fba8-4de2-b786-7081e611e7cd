//
//  Extensions.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

// Compatibility extensions for older iOS versions
extension View {
    @ViewBuilder
    func compatibleForegroundStyle<S>(_ style: S) -> some View where S : ShapeStyle {
        if #available(iOS 15.0, *) {
            self.foregroundStyle(style)
        } else {
            self.foregroundColor(.pink)
        }
    }
    
    @ViewBuilder
    func compatibleOverlay<V: View>(@ViewBuilder content: () -> V) -> some View {
        if #available(iOS 15.0, *) {
            self.overlay(alignment: .center, content: content)
        } else {
            self.overlay(content())
        }
    }
}

extension Color {
    static var compatibleSecondary: Color {
        if #available(iOS 13.0, *) {
            return .secondary
        } else {
            return .gray
        }
    }
}