<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>API_KEY</key>
	<string>********************************************************************************************************************************************************************</string>
	<!-- 
	DEVELOPER CONFIGURATION: This is HeartStarter's OpenAI API key for generating flirt messages.
	
	KEYCHAIN-BASED SECURITY: The app uses iOS Keychain for secure API key storage.
	
	Storage Priority (most secure first):
	1. iOS Keychain (recommended) - Encrypted and secure
	2. This plist file (development only) - Will auto-migrate to Keychain
	3. UserDefaults (legacy support) - Will auto-migrate to Keychain
	
	SETUP INSTRUCTIONS FOR DEVELOPERS:
	1. Get your OpenAI API key from platform.openai.com/api-keys
	2. Replace "YOUR_API_KEY_HERE" with your actual API key
	3. Build and run the app - it will automatically migrate to Keychain
	4. The plist value can be reset to placeholder after migration
	
	PRODUCTION SECURITY:
	- API key is encrypted and stored in iOS Keychain
	- Key persists across app updates and device restarts  
	- Only this app can access the stored key
	- End users never see or manage the API key
	
	IMPORTANT: This is the app developer's API key, not individual user keys.
	Users simply use the app - they don't provide their own OpenAI keys.
	-->
</dict>
</plist>
