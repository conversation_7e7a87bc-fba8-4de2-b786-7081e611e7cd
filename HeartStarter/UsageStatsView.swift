//
//  UsageStatsView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct UsageStatsView: View {
    let stats: UserStats
    let onUpgrade: () -> Void
    
    var body: some View {
        VStack(spacing: 14) {
            
            // Plan status and usage limit indicator
            if !stats.isPremium {
                // Free plan indicator with premium styling
                HStack(spacing: 8) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundStyle(LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                    
                    Text("\(displayCount)/\(stats.dailyLimit) messages used today")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                
                // Premium progress bar
                ProgressView(value: Double(displayCount), total: Double(stats.dailyLimit))
                    .progressViewStyle(LinearProgressViewStyle(tint: usageColor))
                    .scaleEffect(y: 1.2)
                
                // Daily limit reached message or premium upsell
                if displayCount >= stats.dailyLimit {
                    DailyLimitReachedMessage(onUpgrade: onUpgrade)
                } else if displayCount > 0 {
                    PremiumUpsellBanner(onUpgrade: onUpgrade)
                }
            } else {
                HStack(spacing: 8) {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundStyle(LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                    
                    Text("Premium unlimited")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
            }
            
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(.systemGray5), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var displayCount: Int {
        // Fresh-start display for downgraded free users:
        // If the stored count exceeds the free limit, show 0/limit instead of "3/3" from capped premium usage.
        if !stats.isPremium && stats.dailyFlirtCount > stats.dailyLimit {
            return 0
        }
        // Otherwise show the actual count (capped to limit for clarity)
        return min(stats.dailyFlirtCount, stats.dailyLimit)
    }
    
    private var usageColor: Color {
        let percentage = Double(displayCount) / Double(stats.dailyLimit)
        
        if percentage < 0.5 {
            return .green
        } else if percentage < 0.8 {
            return .orange
        } else {
            return .red
        }
    }
}

struct DailyLimitReachedMessage: View {
    let onUpgrade: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            Text("💕 You've charmed your way through all 3 free messages today!")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text("Upgrade to Premium for unlimited flirting power, or swing by tomorrow for more free magic! ✨")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Button(action: onUpgrade) {
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.yellow)
                    
                    Text("Upgrade to Premium")
                        .font(.caption)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(LinearGradient(
                            colors: [.yellow.opacity(0.1), .orange.opacity(0.1)],
                            startPoint: .leading,
                            endPoint: .trailing
                        ))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(.yellow.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct PremiumUpsellBanner: View {
    let onUpgrade: () -> Void
    
    @State private var shimmerAnimation: Bool = false
    
    var body: some View {
        Button(action: onUpgrade) {
            HStack {
                Image(systemName: "crown.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.yellow)
                
                Text("Upgrade to Premium")
                    .font(.caption)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [.yellow.opacity(0.1), .orange.opacity(0.1)],
                        startPoint: .leading,
                        endPoint: .trailing
                    ))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(.yellow.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    VStack(spacing: 20) {
        UsageStatsView(
            stats: UserStats(
                totalFlirtsGenerated: 42,
                dailyFlirtCount: 3,
                lastResetDate: Date(),
                seenMessageIds: []
            ),
            onUpgrade: {}
        )
        
        UsageStatsView(
            stats: UserStats(
                totalFlirtsGenerated: 142,
                dailyFlirtCount: 5,
                lastResetDate: Date(),
                seenMessageIds: []
            ),
            onUpgrade: {}
        )
        
        UsageStatsView(
            stats: {
                var premiumStats = UserStats(
                    totalFlirtsGenerated: 342,
                    dailyFlirtCount: 25,
                    lastResetDate: Date(),
                    seenMessageIds: []
                )
                premiumStats.updateSubscription(tier: .premium)
                return premiumStats
            }(),
            onUpgrade: {}
        )
    }
    .padding()
}
