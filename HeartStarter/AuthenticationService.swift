//
//  AuthenticationService.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import AuthenticationServices
import CloudKit
import Combine
import UIKit

@MainActor
class AuthenticationService: NSObject, ObservableObject {
    @Published var isSignedIn: Bool = false
    @Published var userIdentifier: String?
    @Published var userName: String?
    @Published var userEmail: String?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private let keychain = KeychainHelper.shared
    private let cloudKitService = CloudKitService.shared
    
    // UserDefaults keys
    private let userIdKey = "HeartStarter_UserId"
    private let userNameKey = "HeartStarter_UserName"
    private let userEmailKey = "HeartStarter_UserEmail"
    
    override init() {
        super.init()
        checkSignInStatus()
    }
    
    // MARK: - Sign In with Apple
    func signInWithApple() {
        isLoading = true
        errorMessage = nil
        
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    // MARK: - Sign Out
    func signOut() async {
        isLoading = true
        
        // Clear CloudKit data
        await cloudKitService.clearUserData()
        
        // Clear local data
        keychain.delete(userIdKey)
        UserDefaults.standard.removeObject(forKey: userIdKey)
        UserDefaults.standard.removeObject(forKey: userNameKey)
        UserDefaults.standard.removeObject(forKey: userEmailKey)
        
        // Reset authentication state
        userIdentifier = nil
        userName = nil
        userEmail = nil
        isSignedIn = false
        isLoading = false
    }
    
    // MARK: - Check Sign In Status
    private func checkSignInStatus() {
        guard let savedUserId = UserDefaults.standard.string(forKey: userIdKey) else {
            isSignedIn = false
            return
        }
        
        // Verify the saved user ID with Apple
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        appleIDProvider.getCredentialState(forUserID: savedUserId) { [weak self] (credentialState, error) in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    self?.loadUserData(userId: savedUserId)
                case .revoked, .notFound:
                    Task {
                        await self?.signOut()
                    }
                default:
                    break
                }
            }
        }
    }
    
    // MARK: - User Data Management
    private func loadUserData(userId: String) {
        userIdentifier = userId
        userName = UserDefaults.standard.string(forKey: userNameKey)
        userEmail = UserDefaults.standard.string(forKey: userEmailKey)
        isSignedIn = true
    }
    
    private func saveUserData(userId: String, name: String?, email: String?) {
        // Save to secure keychain
        keychain.save(userId, for: userIdKey)
        
        // Save to UserDefaults for quick access
        UserDefaults.standard.set(userId, forKey: userIdKey)
        
        if let name = name {
            UserDefaults.standard.set(name, forKey: userNameKey)
        }
        
        if let email = email {
            UserDefaults.standard.set(email, forKey: userEmailKey)
        }
    }
    
    // MARK: - CloudKit Integration
    private func setupUserInCloudKit(userId: String, name: String?, email: String?) async {
        await cloudKitService.setupUser(
            userId: userId,
            name: name,
            email: email
        )
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AuthenticationService: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userId = appleIDCredential.user
            let fullName = appleIDCredential.fullName
            let email = appleIDCredential.email
            
            // Construct display name
            let displayName = [fullName?.givenName, fullName?.familyName]
                .compactMap { $0 }
                .joined(separator: " ")
            
            // Save user data
            saveUserData(
                userId: userId,
                name: displayName.isEmpty ? nil : displayName,
                email: email
            )
            
            // Update published properties
            userIdentifier = userId
            userName = displayName.isEmpty ? nil : displayName
            userEmail = email
            isSignedIn = true
            
            // Setup user in CloudKit
            Task {
                await setupUserInCloudKit(userId: userId, name: displayName, email: email)
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        isLoading = false
        
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                // User canceled, no need to show error
                break
            case .failed:
                errorMessage = "Sign in failed. Please try again."
            case .invalidResponse:
                errorMessage = "Invalid response from Apple. Please try again."
            case .notHandled:
                errorMessage = "Sign in not handled. Please try again."
            default:
                errorMessage = "An unknown error occurred. Please try again."
            }
        } else {
            errorMessage = "Sign in failed. Please try again."
        }
        
        debugLog("Sign in with Apple failed: \(error)")
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding
extension AuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow(windowScene: UIApplication.shared.connectedScenes.first as! UIWindowScene)
        }
        return window
    }
}

// MARK: - Keychain Helper
class KeychainHelper {
    static let shared = KeychainHelper()
    
    private init() {}
    
    func save(_ value: String, for key: String) {
        let data = Data(value.utf8)
        
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ] as [String: Any]
        
        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }
    
    func get(for key: String) -> String? {
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ] as [String: Any]
        
        var dataTypeRef: AnyObject?
        let status: OSStatus = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        if status == noErr {
            if let data = dataTypeRef as? Data {
                return String(data: data, encoding: .utf8)
            }
        }
        
        return nil
    }
    
    func delete(_ key: String) {
        let query = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ] as [String: Any]
        
        SecItemDelete(query as CFDictionary)
    }
    
    // MARK: - OpenAI API Key Management
    private static let openAIAPIKeyIdentifier = "com.newversion.HeartStarter.OpenAIAPIKey"
    
    func saveOpenAIAPIKey(_ apiKey: String) {
        save(apiKey, for: Self.openAIAPIKeyIdentifier)
    }
    
    func getOpenAIAPIKey() -> String? {
        return get(for: Self.openAIAPIKeyIdentifier)
    }
    
    func deleteOpenAIAPIKey() {
        delete(Self.openAIAPIKeyIdentifier)
    }
    
    func hasOpenAIAPIKey() -> Bool {
        return getOpenAIAPIKey() != nil && !getOpenAIAPIKey()!.isEmpty
    }
}