//
//  UsageTracker.swift
//  HeartStarter
//
//  Created by <PERSON> on 20/09/2025.
//

import Foundation
import Combine

// MARK: - Usage Event
enum UsageEvent {
    case messageGenerated(spiceLevel: SpiceLevel, tone: FlirtTone?, isPersonalized: Bool)
    case cacheHit(spiceLevel: SpiceLevel, tone: FlirtTone?)
    case cacheMiss(spiceLevel: SpiceLevel, tone: FlirtTone?)
    case apiCall(model: String, tokenCount: Int)
    case abuseWarningShown(messageCount: Int)
    case cooldownTriggered(reason: CooldownReason)
}

// MARK: - Cooldown Reason
enum CooldownReason: String, CaseIterable {
    case dailyLimit = "daily_limit"
    case abuseDetection = "abuse_detection"
    case excessiveUsage = "excessive_usage"
    case rateLimit = "rate_limit"
    
    var friendlyMessage: String {
        switch self {
        case .dailyLimit:
            return "You've reached your daily message limit. Upgrade to Premium for unlimited messages!"
        case .abuseDetection:
            return "Wow! You're on fire today! 🔥 To ensure everyone gets great service, let's pace things a bit. Come back in an hour for more flirty magic!"
        case .excessiveUsage:
            return "You're a flirting champion! 🏆 Let's give the AI a quick break. More messages available in 1 hour."
        case .rateLimit:
            return "Taking a quick breather to maintain quality. Please try again in a few minutes."
        }
    }
    
    var cooldownDuration: TimeInterval {
        switch self {
        case .dailyLimit:
            return 24 * 60 * 60 // 24 hours
        case .abuseDetection, .excessiveUsage:
            return 60 * 60 // 1 hour
        case .rateLimit:
            return 5 * 60 // 5 minutes
        }
    }
}

// MARK: - Usage Metrics
struct UsageMetrics: Codable {
    var totalSessions: Int = 0
    var averageMessagesPerSession: Double = 0
    var peakUsageHour: Int = 0
    var mostPopularSpiceLevel: SpiceLevel = .mild
    var mostPopularTone: FlirtTone = .witty
    var costEfficiency: Double = 0.0 // Savings from caching
    
    mutating func updateSessionMetrics(messagesInSession: Int) {
        totalSessions += 1
        averageMessagesPerSession = (averageMessagesPerSession * Double(totalSessions - 1) + Double(messagesInSession)) / Double(totalSessions)
    }
}

// MARK: - Abuse Detection
struct AbuseDetectionConfig {
    let maxMessagesPerHour: Int = 100
    let maxDailyMessages: Int = 500
    let burstThreshold: Int = 20 // Messages in 5 minutes
    let cooldownAfterWarnings: Int = 2
    
    // Premium user specific limits
    let premiumHourlyWarningThreshold: Int = 100
    let premiumDailyCooldownThreshold: Int = 300
}

// MARK: - Usage Tracker
@MainActor
class UsageTracker: ObservableObject {
    @Published var metrics = UsageMetrics()
    @Published var isInCooldown = false
    @Published var cooldownReason: CooldownReason?
    @Published var cooldownTimeRemaining: TimeInterval = 0
    
    private let config = AbuseDetectionConfig()
    private var sessionStartTime = Date()
    private var messagesThisSession = 0
    private var hourlyMessageCounts: [Int] = Array(repeating: 0, count: 24)
    private var burstDetectionWindow: [Date] = []
    private let maxBurstWindowSize = 20
    
    // Timer for cooldown updates
    private var cooldownTimer: Timer?
    
    init() {
        sessionStartTime = Date()
    }
    
    deinit {
        cooldownTimer?.invalidate()
    }
    
    // MARK: - Public Interface
    
    /// Track a usage event and check for abuse patterns
    func trackEvent(_ event: UsageEvent, userStats: inout UserStats) -> Bool {
        switch event {
        case .messageGenerated(let spiceLevel, let tone, let isPersonalized):
            return trackMessageGeneration(spiceLevel: spiceLevel, tone: tone, isPersonalized: isPersonalized, userStats: &userStats)
            
        case .cacheHit(let spiceLevel, let tone):
            trackCacheHit(spiceLevel: spiceLevel, tone: tone, userStats: &userStats)
            return true
            
        case .cacheMiss(let spiceLevel, let tone):
            trackCacheMiss(spiceLevel: spiceLevel, tone: tone, userStats: &userStats)
            return true
            
        case .apiCall(let model, let tokenCount):
            trackAPICall(model: model, tokenCount: tokenCount, userStats: &userStats)
            return true
            
        case .abuseWarningShown(let messageCount):
            trackAbuseWarning(messageCount: messageCount, userStats: &userStats)
            return true
            
        case .cooldownTriggered(let reason):
            triggerCooldown(reason: reason, userStats: &userStats)
            return false
        }
    }
    
    /// Check if user should be shown abuse warning
    func shouldShowAbuseWarning(userStats: UserStats) -> Bool {
        guard userStats.isPremium else { return false }
        
        // Show warning at multiples of 100 messages (100, 200, 300, etc.)
        if userStats.dailyMessagesGenerated > 0 && userStats.dailyMessagesGenerated % 100 == 0 {
            // Check if we haven't shown warning today
            if let lastWarning = userStats.lastAbuseWarningDate {
                return !Calendar.current.isDate(lastWarning, inSameDayAs: Date())
            }
            return true
        }
        
        return false
    }
    
    /// Check if user should be put in cooldown
    func shouldTriggerCooldown(userStats: UserStats) -> CooldownReason? {
        // Free users: daily limit
        if !userStats.isPremium && userStats.dailyFlirtCount >= userStats.dailyLimit {
            return .dailyLimit
        }
        
        // Premium users: abuse detection
        if userStats.isPremium {
            // Check excessive daily usage
            if userStats.dailyMessagesGenerated >= config.premiumDailyCooldownThreshold {
                return .excessiveUsage
            }
            
            // Check burst detection
            if detectBurstUsage() {
                return .rateLimit
            }
            
            // Check if user hit warning thresholds
            if userStats.dailyMessagesGenerated >= config.premiumHourlyWarningThreshold && 
               userStats.dailyMessagesGenerated % 100 == 0 &&
               userStats.abuseWarningCount >= config.cooldownAfterWarnings {
                return .abuseDetection
            }
        }
        
        return nil
    }
    
    /// Get friendly message for current state
    func getFriendlyMessage(userStats: UserStats) -> String? {
        if isInCooldown, let reason = cooldownReason {
            let timeString = formatTimeRemaining(cooldownTimeRemaining)
            return "\(reason.friendlyMessage)\n\nTime remaining: \(timeString)"
        }
        
        if shouldShowAbuseWarning(userStats: userStats) {
            return """
            🎉 Wow! You're on fire today with \(userStats.dailyMessagesGenerated) messages!
            
            You're clearly loving the app! To ensure the best experience for everyone, we'll add a short cooldown after your next batch of messages.
            
            Keep flirting, but remember - quality over quantity! 💕
            """
        }
        
        return nil
    }
    
    // MARK: - Private Tracking Methods
    
    private func trackMessageGeneration(spiceLevel: SpiceLevel, tone: FlirtTone?, isPersonalized: Bool, userStats: inout UserStats) -> Bool {
        messagesThisSession += 1
        addToBurstWindow()
        updatePopularityMetrics(spiceLevel: spiceLevel, tone: tone)
        
        // Check for cooldown triggers
        if let cooldownReason = shouldTriggerCooldown(userStats: userStats) {
            triggerCooldown(reason: cooldownReason, userStats: &userStats)
            return false
        }
        
        // Check for abuse warnings
        if shouldShowAbuseWarning(userStats: userStats) {
            userStats.showAbuseWarning()
        }
        
        return true
    }
    
    private func trackCacheHit(spiceLevel: SpiceLevel, tone: FlirtTone?, userStats: inout UserStats) {
        userStats.recordCacheHit()
        updateCostEfficiency(saved: true)
        debugLog("💰 Cache hit saved API cost - Total saved: \(userStats.totalAPICallsSaved)")
    }
    
    private func trackCacheMiss(spiceLevel: SpiceLevel, tone: FlirtTone?, userStats: inout UserStats) {
        userStats.recordCacheMiss()
        updateCostEfficiency(saved: false)
    }
    
    private func trackAPICall(model: String, tokenCount: Int, userStats: inout UserStats) {
        if model.contains("gpt-5-mini") {
            userStats.recordGPT5MiniUsage()
        } else if model.contains("gpt-5") {
            userStats.recordGPT5Usage()
        }
        
        debugLog("🤖 API call tracked - Model: \(model), Tokens: \(tokenCount)")
    }
    
    private func trackAbuseWarning(messageCount: Int, userStats: inout UserStats) {
        debugLog("⚠️ Abuse warning shown at \(messageCount) messages")
    }
    
    private func triggerCooldown(reason: CooldownReason, userStats: inout UserStats) {
        isInCooldown = true
        cooldownReason = reason
        cooldownTimeRemaining = reason.cooldownDuration
        
        userStats.startCooldown()
        
        startCooldownTimer()
        
        debugLog("🛑 Cooldown triggered: \(reason.rawValue) for \(formatTimeRemaining(reason.cooldownDuration))")
    }
    
    // MARK: - Abuse Detection Helpers
    
    private func detectBurstUsage() -> Bool {
        let now = Date()
        let fiveMinutesAgo = now.addingTimeInterval(-5 * 60)
        
        // Count messages in last 5 minutes
        let recentMessages = burstDetectionWindow.filter { $0 > fiveMinutesAgo }
        
        return recentMessages.count >= config.burstThreshold
    }
    
    private func addToBurstWindow() {
        burstDetectionWindow.append(Date())
        
        // Keep only recent entries
        if burstDetectionWindow.count > maxBurstWindowSize {
            burstDetectionWindow.removeFirst()
        }
        
        // Clean old entries
        let fiveMinutesAgo = Date().addingTimeInterval(-5 * 60)
        burstDetectionWindow = burstDetectionWindow.filter { $0 > fiveMinutesAgo }
    }
    
    private func updatePopularityMetrics(spiceLevel: SpiceLevel, tone: FlirtTone?) {
        // Update most popular spice level (simplified)
        metrics.mostPopularSpiceLevel = spiceLevel
        
        if let tone = tone {
            metrics.mostPopularTone = tone
        }
        
        // Update peak usage hour
        let currentHour = Calendar.current.component(.hour, from: Date())
        hourlyMessageCounts[currentHour] += 1
        
        if let maxHour = hourlyMessageCounts.enumerated().max(by: { $0.element < $1.element })?.offset {
            metrics.peakUsageHour = maxHour
        }
    }
    
    private func updateCostEfficiency(saved: Bool) {
        if saved {
            metrics.costEfficiency += 0.0016 // Cost per API call saved
        }
    }
    
    // MARK: - Cooldown Management
    
    private func startCooldownTimer() {
        cooldownTimer?.invalidate()
        
        cooldownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            Task {
                await MainActor.run {
                    self.updateCooldownTimer()
                }
            }
        }
    }
    
    private func updateCooldownTimer() {
        guard cooldownTimeRemaining > 0 else {
            endCooldown()
            return
        }
        
        cooldownTimeRemaining -= 1
    }
    
    private func endCooldown() {
        isInCooldown = false
        cooldownReason = nil
        cooldownTimeRemaining = 0
        cooldownTimer?.invalidate()
        cooldownTimer = nil
        
        debugLog("✅ Cooldown ended")
    }
    
    // MARK: - Session Management
    
    func endSession() {
        metrics.updateSessionMetrics(messagesInSession: messagesThisSession)
        messagesThisSession = 0
        sessionStartTime = Date()
        
        debugLog("📊 Session ended - Messages: \(messagesThisSession), Avg: \(String(format: "%.1f", metrics.averageMessagesPerSession))")
    }
    
    // MARK: - Utility
    
    private func formatTimeRemaining(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
    
    // MARK: - Analytics
    
    func getAnalyticsData() -> [String: Any] {
        return [
            "total_sessions": metrics.totalSessions,
            "avg_messages_per_session": metrics.averageMessagesPerSession,
            "peak_usage_hour": metrics.peakUsageHour,
            "most_popular_spice": metrics.mostPopularSpiceLevel.rawValue,
            "most_popular_tone": metrics.mostPopularTone.rawValue,
            "cost_efficiency": metrics.costEfficiency,
            "current_session_messages": messagesThisSession
        ]
    }
}