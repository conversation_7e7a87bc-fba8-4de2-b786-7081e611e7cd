//
//  LoadingMessageProvider.swift
//  HeartStarter
//
//  Created by <PERSON> on 22/09/2025.
//

import Foundation
import SwiftUI
import Combine

enum LoadingSource {
    case cache
    case api
}

@MainActor
class LoadingMessageProvider: ObservableObject {
    @Published var currentMessage: String = "Generating… ✨"
    @Published var currentMessageIndex: Int = 0
    
    private var messageTimer: Timer?
    private var loadingSource: LoadingSource = .api
    
    // Creative loading messages for API calls
    private let apiLoadingMessages = [
        "Generating… ✨",
        "Cooking flirts… 🍳💘",
        "Blushing… 😳💭",
        "Sending love… 💌✨",
        "Charging flirt power… ⚡❤️",
        "Winking ideas… 😉🤖",
        "Whispering to GPT… 🫶🤫",
        "Mixing spice… 🧪🔥",
        "Heart-stealer loading… 🏹💘",
        "<PERSON>lirty smile on… 😏✨",
        "Firing Cupid's arrow… 🎯💘",
        "Flirting hard… 🤯❤️",
        "Summoning charm… 🔮💬",
        "Match on my mind… ☁️💖",
        "Sprinkling charm… ✨🧚",
        "Loading charm… 🧲💖"
    ]
    
    // Simple message for cache loading
    private let cacheLoadingMessage = "Generating… ✨"
    
    func startLoading(source: LoadingSource) {
        stopLoading()
        loadingSource = source
        currentMessageIndex = 0
        
        switch source {
        case .cache:
            // For cache, show static message
            currentMessage = cacheLoadingMessage
            
        case .api:
            // For API calls, start rotating messages
            currentMessage = apiLoadingMessages[0]
            startMessageRotation()
        }
    }
    
    func stopLoading() {
        messageTimer?.invalidate()
        messageTimer = nil
    }
    
    private func startMessageRotation() {
        messageTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            Task { @MainActor in
                self.currentMessageIndex = (self.currentMessageIndex + 1) % self.apiLoadingMessages.count
                self.currentMessage = self.apiLoadingMessages[self.currentMessageIndex]
            }
        }
    }
    
    deinit {
        messageTimer?.invalidate()
        messageTimer = nil
    }
}