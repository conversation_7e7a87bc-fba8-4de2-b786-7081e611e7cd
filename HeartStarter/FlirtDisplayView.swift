//
//  FlirtDisplayView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct ToastView: View {
    let message: String
    let icon: String
    let color: Color
    @Binding var isShowing: Bool
    
    var body: some View {
        if isShowing {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                Text(message)
                    .font(.system(size: 15, weight: .medium))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                Capsule()
                    .fill(Color(.systemBackground))
                    .overlay(
                        Capsule()
                            .stroke(color, lineWidth: 1.5)
                    )
                    .shadow(color: color.opacity(0.3), radius: 8, x: 0, y: 2)
            )
            .foregroundColor(color)
            .transition(.asymmetric(
                insertion: .opacity.combined(with: .move(edge: .top)),
                removal: .opacity.combined(with: .move(edge: .top))
            ))
        }
    }
}

struct FlirtDisplayView: View {
    let message: FlirtMessage
    let onCopy: () -> Void
    let onShare: () -> Void
    let onFavorite: () -> Void
    let onReport: () -> Void
    let onClear: () -> Void
    let onShowToast: (String, String, Color) -> Void
    let messageIsFavorited: Bool
    let canFavorite: Bool
    
    @State private var pulseAnimation: Bool = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Message Display with Bouncing Hearts
            VStack(spacing: 10) {
                ZStack(alignment: .bottomTrailing) {
                    Text(message.text)
                        .font(.body)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.leading)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 18)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(backgroundGradient)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(borderColor, lineWidth: 1.5)
                                )
                        )
                        .scaleEffect(pulseAnimation ? 1.02 : 1.0)
                        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseAnimation)
                        .bouncingHearts(for: message.id)
                    
                    // Clear/Dismiss Button - Bottom Right
                    Button(action: {
                        // Show toast feedback
                        onShowToast("Cleared!", "checkmark.circle.fill", .green)
                        
                        onClear()
                        
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 18))
                            .foregroundColor(.secondary)
                            .background(Circle().fill(.white))
                    }
                    .padding(8)
                    .opacity(0.8)
                }
                
                // Message metadata - compact
                HStack(spacing: 6) {
                    // Spice Level Badge
                    HStack(spacing: 3) {
                        Image(systemName: "flame.fill")
                            .font(.system(size: 8))
                        Text(message.spiceLevel.rawValue)
                            .font(.system(size: 10))
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        Capsule()
                            .fill(.secondary.opacity(0.1))
                    )
                    .foregroundColor(.secondary)
                    
                    // Tone Badge
                    if let tone = message.tone {
                        HStack(spacing: 3) {
                            Image(systemName: "face.smiling")
                                .font(.system(size: 8))
                            Text(tone.rawValue)
                                .font(.system(size: 10))
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(.secondary.opacity(0.1))
                        )
                        .foregroundColor(.secondary)
                    }
                    
                    // Personal Touch Badge
                    if message.hasPersonalTouch {
                        HStack(spacing: 3) {
                            Image(systemName: "person.fill")
                                .font(.system(size: 8))
                            Text("Personal")
                                .font(.system(size: 10))
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(.secondary.opacity(0.1))
                        )
                        .foregroundColor(.secondary)
                    }
                }
            }
            
            // Premium Action Buttons - icon only with better spacing
            HStack(spacing: 24) {
                PremiumActionButton(
                    systemImage: "doc.on.doc",
                    gradientColors: [.blue, .cyan],
                    action: {
                        copyToClipboard()
                        onCopy()
                    }
                )
                
                PremiumActionButton(
                    systemImage: "square.and.arrow.up",
                    gradientColors: [.green, .mint],
                    action: onShare
                )
                
                PremiumActionButton(
                    systemImage: messageIsFavorited ? "heart.fill" : "heart",
                    gradientColors: [.pink, .red],
                    showCrown: !canFavorite,
                    action: {
                        if canFavorite {
                            if messageIsFavorited {
                                onShowToast("Already in Favourites!", "heart.fill", .orange)
                            } else {
                                onShowToast("Added to Favourites!", "heart.fill", .pink)
                            }
                        }
                        onFavorite()
                    }
                )
            }
            
            Button {
                onReport()
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.bubble")
                    Text("Report message")
                }
                .font(.footnote.weight(.semibold))
            }
            .buttonStyle(.plain)
            .foregroundColor(.red)
        }
        .onAppear {
            pulseAnimation = true
        }
    }
    
    private var backgroundGradient: LinearGradient {
        switch message.spiceLevel {
        case .mild:
            return LinearGradient(
                colors: [.pink.opacity(0.1), .pink.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .medium:
            return LinearGradient(
                colors: [.orange.opacity(0.1), .orange.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .spicy:
            return LinearGradient(
                colors: [.red.opacity(0.1), .red.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private var borderColor: Color {
        switch message.spiceLevel {
        case .mild: return .pink
        case .medium: return .orange
        case .spicy: return .red
        }
    }
    
    private func copyToClipboard() {
        UIPasteboard.general.string = message.text
        
        // Show toast feedback
        onShowToast("Copied!", "checkmark.circle.fill", .green)
        
        // Haptic feedback
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.prepare()
        notificationFeedback.notificationOccurred(.success)
    }
}

struct PremiumActionButton: View {
    let systemImage: String
    let gradientColors: [Color]
    let showCrown: Bool
    let action: () -> Void
    
    @State private var isPressed: Bool = false
    @State private var pulseEffect: Bool = false
    
    init(systemImage: String, gradientColors: [Color], showCrown: Bool = false, action: @escaping () -> Void) {
        self.systemImage = systemImage
        self.gradientColors = gradientColors
        self.showCrown = showCrown
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            action()
            
            // Enhanced haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.prepare()
            impactFeedback.impactOccurred()
            
            // Pulse animation on tap
            withAnimation(.easeOut(duration: 0.2)) {
                pulseEffect = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                pulseEffect = false
            }
        }) {
            ZStack {
                // Icon
                Image(systemName: systemImage)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                // Crown overlay for premium features
                if showCrown {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 10))
                        .foregroundStyle(LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .offset(x: 12, y: -8)
                }
            }
            .frame(width: 50, height: 50)
            .background(
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradientColors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .scaleEffect(isPressed ? 0.9 : (pulseEffect ? 1.1 : 1.0))
            .shadow(color: gradientColors[0].opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(.plain)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

#Preview {
    FlirtDisplayView(
        message: FlirtMessage(
            text: "Are you a magician? Because whenever I look at you, everyone else disappears. ✨",
            spiceLevel: .mild,
            tone: .witty,
            hasPersonalTouch: true
        ),
        onCopy: {},
        onShare: {},
        onFavorite: {},
        onReport: {},
        onClear: {},
        onShowToast: { _, _, _ in },
        messageIsFavorited: false,
        canFavorite: true
    )
    .padding()
}
