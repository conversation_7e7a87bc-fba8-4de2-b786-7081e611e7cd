//
//  ModerationService.swift
//  HeartStarter
//
//  Provides lightweight content safety checks for AI-generated flirts.
//

import Foundation

struct ModeratedMessage {
    let text: String
    let isFlagged: Bool
    let flaggedCategories: [String]
}

enum ModerationError: LocalizedError {
    case missingAPIKey
    case invalidResponse

    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "Missing API credentials for moderation."
        case .invalidResponse:
            return "Moderation service returned an unexpected response."
        }
    }
}

struct ModerationService {
    private let moderationURL = URL(string: "https://api.openai.com/v1/moderations")!
    private let model = "omni-moderation-latest"

    func moderate(messages: [String]) async throws -> [ModeratedMessage] {
        guard !messages.isEmpty else { return [] }

        let apiKey = OpenAIConfig.apiKey
        guard OpenAIConfig.validateAPIKey(apiKey) else {
            throw ModerationError.missingAPIKey
        }

        let requestBody = ModerationRequest(model: model, input: messages)
        var request = URLRequest(url: moderationURL)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)
        guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
            debugLog("Moderation failed with response: \(String(data: data, encoding: .utf8) ?? "none")")
            throw ModerationError.invalidResponse
        }

        let decoded = try JSONDecoder().decode(ModerationResponse.self, from: data)
        guard decoded.results.count == messages.count else {
            throw ModerationError.invalidResponse
        }

        return zip(messages, decoded.results).map { message, result in
            let flaggedCategories = result.categories
                .filter { $0.value }
                .map { $0.key }
            return ModeratedMessage(
                text: message,
                isFlagged: result.flagged,
                flaggedCategories: flaggedCategories
            )
        }
    }
}

private struct ModerationRequest: Codable {
    let model: String
    let input: [String]
}

private struct ModerationResponse: Codable {
    struct Result: Codable {
        let flagged: Bool
        let categories: [String: Bool]
    }

    let results: [Result]
}
