//
//  SubscriptionManager.swift
//  HeartStarter
//
//  Created by <PERSON> for comprehensive subscription management
//

import Foundation
import StoreKit
import Combine
import UIKit

enum SubscriptionManagerError: Error {
    case failedVerification
    case unknown
}

enum AppEnvironment {
    case development
    case testFlight
    case production
}

struct EnvironmentInfo {
    let displayName: String
    let showRestorePurchases: Bool
    let showEnvironmentIndicator: Bool
    let allowSandboxTesting: Bool
    let description: String
}

@MainActor
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()
    
    @Published var availableProducts: [Product] = []
    @Published var purchasedSubscriptions: [Product] = []
    @Published var subscriptionStatus: Product.SubscriptionInfo.RenewalState?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Cancellation status tracking
    @Published var isSubscriptionCancelled = false
    @Published var subscriptionExpiryDate: Date?
    
    private var updateListenerTask: Task<Void, Error>?
    private var cloudKitService = CloudKitService.shared
    
    // Product IDs - these should match your App Store Connect configuration
    enum ProductID: String, CaseIterable {
        case premiumWeekly = "com.newversion.HeartStarter.premium.weekly"
        case premiumYearly = "com.newversion.HeartStarter.premium.yearly"
        
        var displayName: String {
            switch self {
            case .premiumWeekly: return "Premium Weekly"
            case .premiumYearly: return "Premium Yearly"
            }
        }
        
        var description: String {
            switch self {
            case .premiumWeekly: return "Unlimited flirts, all tones, personal touch, and favorites"
            case .premiumYearly: return "Unlimited flirts, all tones, personal touch, and favorites - Save 50%!"
            }
        }
        
        var period: String {
            switch self {
            case .premiumWeekly: return "week"
            case .premiumYearly: return "year"
            }
        }
    }
    
    private init() {
        updateListenerTask = listenForTransactions()
        
        Task {
            await loadProducts()
            await updateCustomerProductStatus()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // MARK: - Product Loading
    func loadProducts() async {
        do {
            isLoading = true
            errorMessage = nil
            
            let productIDs = ProductID.allCases.map { $0.rawValue }
            availableProducts = try await Product.products(for: productIDs)
            
        } catch {
            errorMessage = "Failed to load products: \(error.localizedDescription)"
            debugLog("Failed to load products: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - Free Trial Management
    func isEligibleForFreeTrial() async -> Bool {
        debugLog("🔍 Checking trial eligibility - Called from:")
        debugLog("   Stack trace: \(Thread.callStackSymbols.prefix(5).joined(separator: "\n   "))")
        
        // First check StoreKit transaction history (authoritative source)
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this transaction was a free trial
                if transaction.offer?.type == .introductory {
                    debugLog("🚫 User not eligible for trial: Found previous trial transaction (Product: \(transaction.productID))")
                    return false // User has already used a free trial
                }
            } catch {
                debugLog("Failed to verify transaction: \(error)")
            }
        }
        
        // Also check all transactions (not just current entitlements) for comprehensive check
        for await result in Transaction.all {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this transaction was a free trial for our products
                if (transaction.productID == ProductID.premiumWeekly.rawValue || 
                    transaction.productID == ProductID.premiumYearly.rawValue),
                   transaction.offer?.type == .introductory {
                    debugLog("🚫 User not eligible for trial: Found trial in transaction history (Product: \(transaction.productID))")
                    return false
                }
            } catch {
                debugLog("Failed to verify historical transaction: \(error)")
            }
        }
        
        debugLog("✅ User is eligible for free trial")
        return true // User is eligible for free trial
    }
    
    func startFreeTrial(for product: Product) async -> Bool {
        // Check eligibility first
        guard await isEligibleForFreeTrial() else {
            errorMessage = "Free trial not available. You may have already used your free trial."
            return false
        }
        
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }
        
        do {
            // Purchase with introductory offer (free trial)
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                
                // Verify this is indeed a trial
                if transaction.offer?.type == .introductory {
                    // Update local subscription status first (always works)
                    await updateCustomerProductStatus()
                    
                    // Try to sync with CloudKit (mark as trial, but don't fail if this fails)
                    await syncTrialToCloudKit(transaction: transaction)
                    
                    await transaction.finish()
                    return true
                } else {
                    // Regular purchase, handle normally
                    await updateCustomerProductStatus()
                    await syncSubscriptionToCloudKit(transaction: transaction)
                    await transaction.finish()
                    return true
                }
                
            case .userCancelled:
                errorMessage = "Free trial was cancelled"
                
            case .pending:
                errorMessage = "Free trial is pending approval"
                
            @unknown default:
                errorMessage = "Unknown result from free trial"
            }
            
        } catch StoreKitError.notAvailableInStorefront {
            errorMessage = "Free trial not available in your region"
        } catch StoreKitError.networkError {
            errorMessage = "Network error. Please check your connection."
        } catch {
            errorMessage = "Free trial failed: \(error.localizedDescription)"
        }
        
        return false
    }
    
    // MARK: - Purchase Flow
    func purchase(_ product: Product) async -> Bool {
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }

        do {
            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                
                // Update local subscription status first (this always works)
                await updateCustomerProductStatus()
                
                // Try to sync with CloudKit (but don't fail the purchase if this fails)
                await syncSubscriptionToCloudKit(transaction: transaction)
                
                await transaction.finish()
                return true
                
            case .userCancelled:
                errorMessage = "Purchase was cancelled"
                
            case .pending:
                errorMessage = "Purchase is pending approval"
                
            @unknown default:
                errorMessage = "Unknown purchase result"
            }
            
        } catch StoreKitError.notAvailableInStorefront {
            errorMessage = "Product not available in your region"
        } catch StoreKitError.networkError {
            errorMessage = "Network error. Please check your connection."
        } catch {
            errorMessage = "Purchase failed: \(error.localizedDescription)"
        }
        
        return false
    }
    
    // MARK: - Restore Purchases
    func restorePurchases() async -> Bool {
        debugLog("🔄 Starting restore purchases...")
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }

        do {
            // Sync with App Store to get latest transaction data
            try await AppStore.sync()
            debugLog("✅ AppStore.sync() completed")
            
            // Update subscription status from StoreKit
            await updateCustomerProductStatus()
            debugLog("✅ updateCustomerProductStatus() completed")
            
            // Check if we found any active subscriptions
            if !purchasedSubscriptions.isEmpty {
                debugLog("✅ Restore successful: Found \(purchasedSubscriptions.count) subscription(s)")
                errorMessage = nil // Ensure no error message is shown
                
                // Get subscription details for notification
                let productId = purchasedSubscriptions.first?.id ?? "com.newversion.HeartStarter.premium.weekly"
                let expiryDate = subscriptionExpiryDate
                
                // Post notification to update UI components with complete data including cancellation status
                NotificationCenter.default.post(
                    name: .subscriptionUpdated,
                    object: nil,
                    userInfo: [
                        "tier": SubscriptionTier.premium,
                        "productId": productId,
                        "expirationDate": expiryDate as Any,
                        "isRestored": true,
                        "isCancelled": isSubscriptionCancelled,
                        "willAutoRenew": !isSubscriptionCancelled
                    ]
                )
                debugLog("📢 Posted subscriptionUpdated notification for restore with productId: \(productId)")
                
                return true
            } else {
                debugLog("❌ Restore failed: No active subscriptions found")
                errorMessage = "No previous purchases found.\n\nIf you had a subscription, please:\n• Check you're using the same Apple ID\n• Try again in a few moments\n• Contact support if the issue persists"
                return false
            }

        } catch {
            debugLog("❌ Restore error: \(error.localizedDescription)")
            let userFriendlyMessage: String
            
            if error.localizedDescription.contains("network") || error.localizedDescription.contains("internet") {
                userFriendlyMessage = "Please check your internet connection and try again."
            } else if error.localizedDescription.contains("cancelled") {
                userFriendlyMessage = "Restore was cancelled. Please try again if needed."
            } else {
                userFriendlyMessage = "Unable to restore purchases at this time. Please try again later."
            }

            errorMessage = userFriendlyMessage
            return false
        }
    }
    
    // MARK: - Subscription Status
    func updateCustomerProductStatus() async {
        let hadActiveSubscription = isSubscriptionActive()
        let previousSubscriptions = purchasedSubscriptions
        var updatedSubscriptions: [Product] = []
        var isSubscriptionCancelled = false
        var subscriptionExpiryDate: Date?
        var observedStatus: Product.SubscriptionInfo.RenewalState?

        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)

                if let subscription = availableProducts.first(where: { $0.id == transaction.productID }) {
                    updatedSubscriptions.append(subscription)

                    // Track subscription expiry date for current entitlement
                    subscriptionExpiryDate = transaction.expirationDate

                    // Check subscription renewal/cancellation status
                    do {
                        if let statuses = try await subscription.subscription?.status {
                            for status in statuses {
                                observedStatus = observedStatus ?? status.state

                                if let renewalInfo = try? checkVerified(status.renewalInfo) {
                                    if let renewalDate = renewalInfo.renewalDate {
                                        subscriptionExpiryDate = renewalDate
                                    }
                                    isSubscriptionCancelled = !renewalInfo.willAutoRenew
                                    debugLog("📋 Subscription Status:")
                                    debugLog("   - Product: \(transaction.productID)")
                                    debugLog("   - Expiry Date: \(subscriptionExpiryDate?.formatted() ?? "Unknown")")
                                    debugLog("   - Will Auto-Renew: \(renewalInfo.willAutoRenew)")
                                    debugLog("   - Is Cancelled: \(isSubscriptionCancelled)")
                                    break
                                }
                            }
                        }
                    } catch {
                        debugLog("Failed to check subscription renewal status: \(error)")
                    }
                } else {
                    // Fallback when products haven't loaded but entitlement exists
                    do {
                        let fetchedProducts = try await Product.products(for: [transaction.productID])
                        if let fetched = fetchedProducts.first {
                            if !availableProducts.contains(where: { $0.id == fetched.id }) {
                                availableProducts.append(fetched)
                            }
                            updatedSubscriptions.append(fetched)
                        }
                    } catch {
                        debugLog("⚠️ Failed to fetch product for entitlement \(transaction.productID): \(error)")
                    }

                    subscriptionExpiryDate = transaction.expirationDate
                    if observedStatus == nil {
                        observedStatus = .subscribed
                    }
                }

            } catch {
                debugLog("Failed to verify transaction: \(error)")
            }
        }

        let hasActiveSubscription = isActiveState(observedStatus) || !updatedSubscriptions.isEmpty

        subscriptionStatus = observedStatus
        if hasActiveSubscription {
            purchasedSubscriptions = updatedSubscriptions.isEmpty ? previousSubscriptions : updatedSubscriptions
        } else {
            purchasedSubscriptions = []
        }
        self.isSubscriptionCancelled = hasActiveSubscription ? isSubscriptionCancelled : false
        let resolvedExpiryDate = hasActiveSubscription ? subscriptionExpiryDate : nil
        self.subscriptionExpiryDate = resolvedExpiryDate

        if hadActiveSubscription && !hasActiveSubscription {
            debugLog("📢 Subscription ended locally, downgrading to free tier")
            NotificationCenter.default.post(
                name: .subscriptionUpdated,
                object: nil,
                userInfo: [
                    "tier": SubscriptionTier.free,
                    "isCancelled": false,
                    "willAutoRenew": false,
                    "isStatusUpdate": true
                ]
            )
        } else if hasActiveSubscription {
            // Always post notification when subscription is active to ensure UI sync
            let productId = purchasedSubscriptions.first?.id ?? "com.newversion.HeartStarter.premium.weekly"
            debugLog("📢 Active subscription found, updating UI with premium status")
            NotificationCenter.default.post(
                name: .subscriptionUpdated,
                object: nil,
                userInfo: [
                    "tier": SubscriptionTier.premium,
                    "productId": productId,
                    "expirationDate": resolvedExpiryDate as Any,
                    "isCancelled": isSubscriptionCancelled,
                    "willAutoRenew": !isSubscriptionCancelled,
                    "isStatusUpdate": true
                ]
            )
        }
    }
    
    /// Check if current subscription is cancelled but still valid
    func getSubscriptionCancellationStatus() async -> (isCancelled: Bool, willAutoRenew: Bool, expiryDate: Date?) {
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                if let product = availableProducts.first(where: { $0.id == transaction.productID }) {
                    // Check real-time cancellation status
                    if let status = try await product.subscription?.status {
                        for renewal in status {
                            if let renewalInfo = try? checkVerified(renewal.renewalInfo) {
                                let isCancelled = !renewalInfo.willAutoRenew
                                return (
                                    isCancelled: isCancelled,
                                    willAutoRenew: renewalInfo.willAutoRenew,
                                    expiryDate: transaction.expirationDate
                                )
                            }
                        }
                    }
                }
                
                // Fallback to current cached status
                return (
                    isCancelled: isSubscriptionCancelled,
                    willAutoRenew: !isSubscriptionCancelled,
                    expiryDate: transaction.expirationDate
                )
                
            } catch {
                debugLog("Failed to check subscription cancellation status: \(error)")
            }
        }
        
        return (isCancelled: false, willAutoRenew: true, expiryDate: nil)
    }

    // MARK: - Utility Methods
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw SubscriptionManagerError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    func isSubscriptionActive() -> Bool {
        if isActiveState(subscriptionStatus) {
            return true
        }
        return !purchasedSubscriptions.isEmpty
    }

    func getActiveSubscriptionTier() -> SubscriptionTier {
        return isSubscriptionActive() ? .premium : .free
    }
    
    private func isActiveState(_ state: Product.SubscriptionInfo.RenewalState?) -> Bool {
        guard let state else { return false }
        switch state {
        case .subscribed, .inGracePeriod, .inBillingRetryPeriod:
            return true
        default:
            return false
        }
    }
    
    /// Validates subscription status purely from StoreKit (independent of CloudKit)
    func validateSubscriptionOffline() async -> (tier: SubscriptionTier, expiryDate: Date?) {
        // Check current entitlements directly from StoreKit
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this is an active subscription or trial
                if let product = availableProducts.first(where: { $0.id == transaction.productID }),
                   product.type == .autoRenewable {
                    
                    // Check if subscription/trial is still valid
                    if let expirationDate = transaction.expirationDate {
                        if Date() < expirationDate {
                            debugLog("✅ Valid subscription found offline: \(transaction.productID), expires: \(expirationDate)")
                            return (.premium, expirationDate)
                        } else {
                            debugLog("⚠️ Expired subscription found: \(transaction.productID)")
                        }
                    } else {
                        // No expiration date means active subscription (managed by Apple)
                        debugLog("✅ Active subscription found offline: \(transaction.productID), no expiry (Apple managed)")
                        return (.premium, nil)
                    }
                }
            } catch {
                debugLog("Failed to verify transaction during offline validation: \(error)")
            }
        }
        
        debugLog("📱 No active subscription found in StoreKit - user is free tier")
        return (.free, nil)
    }
    
    func getActiveProduct() -> Product? {
        guard isSubscriptionActive() else { return nil }
        return purchasedSubscriptions.first
    }
    
    // MARK: - Trial Status Methods
    func isInActiveTrial() async -> Bool {
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this is an active trial
                if transaction.offer?.type == .introductory {
                    // Check if trial is still active
                    if let expirationDate = transaction.expirationDate,
                       Date() < expirationDate {
                        return true
                    }
                }
            } catch {
                debugLog("Failed to verify transaction: \(error)")
            }
        }
        
        return false
    }
    
    func getTrialEndDate() async -> Date? {
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this is an active trial
                if transaction.offer?.type == .introductory {
                    return transaction.expirationDate
                }
            } catch {
                debugLog("Failed to verify transaction: \(error)")
            }
        }
        
        return nil
    }
    
    func checkTrialExpiration() async {
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if this is an expired trial
                if transaction.offer?.type == .introductory,
                   let expirationDate = transaction.expirationDate,
                   Date() >= expirationDate {
                    
                    // Post notification that trial has expired
                    NotificationCenter.default.post(
                        name: .freeTrialExpired,
                        object: nil,
                        userInfo: [
                            "productId": transaction.productID,
                            "transactionId": String(transaction.id),
                            "expirationDate": expirationDate
                        ]
                    )
                }
            } catch {
                debugLog("Failed to verify transaction: \(error)")
            }
        }
    }
    
    // MARK: - Transaction Listener
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await MainActor.run { try self.checkVerified(result) }
                    
                    await self.updateCustomerProductStatus()
                    await self.syncSubscriptionToCloudKit(transaction: transaction)
                    
                    await transaction.finish()
                } catch {
                    debugLog("Transaction failed verification: \(error)")
                }
            }
        }
    }
    
    // MARK: - CloudKit Integration
    private func syncSubscriptionToCloudKit(transaction: Transaction) async {
        // This will be called from UserStatsManager to maintain separation of concerns
        NotificationCenter.default.post(
            name: .subscriptionUpdated,
            object: nil,
            userInfo: [
                "tier": getActiveSubscriptionTier(),
                "productId": transaction.productID,
                "transactionId": String(transaction.id),
                "expirationDate": transaction.expirationDate as Any,
                "isTrialPeriod": transaction.offer?.type == .introductory,
                "isCancelled": isSubscriptionCancelled,
                "willAutoRenew": !isSubscriptionCancelled
            ]
        )
    }
    
    private func syncTrialToCloudKit(transaction: Transaction) async {
        // Sync free trial specifically
        NotificationCenter.default.post(
            name: .freeTrialStarted,
            object: nil,
            userInfo: [
                "productId": transaction.productID,
                "transactionId": String(transaction.id),
                "trialStartDate": transaction.purchaseDate,
                "trialEndDate": transaction.expirationDate as Any,
                "isTrialPeriod": true
            ]
        )
    }
    
    // MARK: - Subscription Management
    
    /// Force check cancellation status and update UI if needed
    func checkAndUpdateCancellationStatus() async {
        debugLog("🔍 Checking cancellation status...")
        
        let previousCancelledStatus = isSubscriptionCancelled
        await updateCustomerProductStatus()
        
        // Always send notification if we have an active subscription to ensure UI is synchronized
        if !purchasedSubscriptions.isEmpty {
            let productId = purchasedSubscriptions.first?.id ?? "com.newversion.HeartStarter.premium.weekly"
            let expiryDate = subscriptionExpiryDate
            
            if previousCancelledStatus != isSubscriptionCancelled {
                debugLog("🔄 Cancellation status changed: \(previousCancelledStatus) → \(isSubscriptionCancelled)")
            } else {
                debugLog("🔄 Refreshing cancellation status: \(isSubscriptionCancelled)")
            }
            
            NotificationCenter.default.post(
                name: .subscriptionUpdated,
                object: nil,
                userInfo: [
                    "tier": SubscriptionTier.premium,
                    "productId": productId,
                    "expirationDate": expiryDate as Any,
                    "isCancelled": isSubscriptionCancelled,
                    "willAutoRenew": !isSubscriptionCancelled,
                    "isStatusUpdate": true
                ]
            )
            debugLog("📢 Posted cancellation status update notification")
        }
    }
    
    /// Opens the native iOS subscription management sheet (iOS 17+) or redirects to Settings
    @MainActor
    func openSubscriptionManagement() {
        if #available(iOS 17.0, *) {
            // iOS 17+ native subscription management sheet
            if let windowScene = UIApplication.shared.connectedScenes
                .compactMap({ $0 as? UIWindowScene })
                .first {
                
                Task {
                    do {
                        try await AppStore.showManageSubscriptions(in: windowScene)
                    } catch {
                        debugLog("Failed to show manage subscriptions sheet: \(error)")
                        // Fallback to Settings app
                        openSubscriptionSettingsLegacy()
                    }
                }
            } else {
                // Fallback if no window scene
                openSubscriptionSettingsLegacy()
            }
        } else {
            // iOS 16 and below - redirect to Settings app
            openSubscriptionSettingsLegacy()
        }
    }
    
    /// Opens subscription management in iOS Settings app (fallback for older iOS or errors)
    private func openSubscriptionSettingsLegacy() {
        if let settingsUrl = URL(string: "https://apps.apple.com/account/subscriptions") {
            UIApplication.shared.open(settingsUrl)
        } else {
            // Ultimate fallback to iOS Settings
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        }
    }
    
    /// Returns current app environment (Development, TestFlight, Production)
    func getCurrentEnvironment() -> AppEnvironment {
        #if DEBUG
        return .development
        #else
        if Bundle.main.appStoreReceiptURL?.lastPathComponent == "sandboxReceipt" {
            return .testFlight
        } else {
            return .production
        }
        #endif
    }
    
    /// Returns environment-specific behavior for subscription management
    func getEnvironmentInfo() -> EnvironmentInfo {
        let environment = getCurrentEnvironment()
        
        switch environment {
        case .development:
            return EnvironmentInfo(
                displayName: "Development",
                showRestorePurchases: true,
                showEnvironmentIndicator: true,
                allowSandboxTesting: true,
                description: "Using Sandbox environment for testing"
            )
        case .testFlight:
            return EnvironmentInfo(
                displayName: "TestFlight",
                showRestorePurchases: true,
                showEnvironmentIndicator: true,
                allowSandboxTesting: true,
                description: "Beta testing environment"
            )
        case .production:
            return EnvironmentInfo(
                displayName: "Production",
                showRestorePurchases: false,
                showEnvironmentIndicator: false,
                allowSandboxTesting: false,
                description: "Live App Store environment"
            )
        }
    }
    
    // MARK: - Helper Methods for UI
    func priceForProduct(_ productID: ProductID) -> String {
        if let product = availableProducts.first(where: { $0.id == productID.rawValue }) {
            return product.displayPrice
        }
        return "Loading..."
    }
    
    func localizedPriceForProduct(_ productID: ProductID) -> String {
        if let product = availableProducts.first(where: { $0.id == productID.rawValue }) {
            return product.displayPrice
        }
        return "N/A"
    }
    
    // MARK: - Dynamic Pricing Helper Methods
    
    /// Returns the lowest starting price for premium subscription
    func getStartingPrice() -> String {
        let weeklyProduct = availableProducts.first { $0.id == ProductID.premiumWeekly.rawValue }
        return weeklyProduct?.displayStartingPrice ?? "Premium subscription available"
    }
    
    /// Returns the weekly product
    var weeklyProduct: Product? {
        return availableProducts.first { $0.id == ProductID.premiumWeekly.rawValue }
    }
    
    /// Returns the yearly product
    var yearlyProduct: Product? {
        return availableProducts.first { $0.id == ProductID.premiumYearly.rawValue }
    }
    
    /// Returns formatted price with period for a specific product
    func formattedPrice(for productID: ProductID) -> String {
        guard let product = availableProducts.first(where: { $0.id == productID.rawValue }) else {
            return "Loading..."
        }
        return product.displayPriceWithPeriod
    }
    
    /// Returns weekly equivalent price for yearly subscription
    func weeklyEquivalentPrice() -> String {
        guard let yearlyProduct = yearlyProduct else {
            return "Loading..."
        }
        return yearlyProduct.displayWeeklyPrice
    }
    
    /// Returns savings text for yearly vs weekly
    func savingsText() -> String? {
        guard let yearly = yearlyProduct,
              let weekly = weeklyProduct else { return nil }
        return yearly.displaySavings(comparedTo: weekly)
    }
    
    /// Returns free trial text with dynamic pricing
    func freeTrialText() -> String {
        guard let weeklyProduct = weeklyProduct else {
            return "7-Day Free Trial • Starting from dynamic pricing"
        }
        return "7-Day Free Trial • \(weeklyProduct.displayStartingPrice)"
    }
    
    /// Returns the active subscription type (Weekly/Yearly/None)
    func getActiveSubscriptionType() -> (type: String, isWeekly: Bool) {
        guard let activeProduct = purchasedSubscriptions.first else {
            return (type: "None", isWeekly: false)
        }
        
        let isWeekly = activeProduct.id.contains("weekly")
        return (type: isWeekly ? "Weekly" : "Yearly", isWeekly: isWeekly)
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let subscriptionUpdated = Notification.Name("subscriptionUpdated")
    static let freeTrialStarted = Notification.Name("freeTrialStarted")
    static let freeTrialExpired = Notification.Name("freeTrialExpired")
}

// MARK: - Product Extension for Localized Pricing
extension Product {
    /// Returns the billing period as a user-friendly string
    var displayPeriod: String {
        if id.contains("weekly") {
            return "week"
        } else if id.contains("yearly") {
            return "year"
        }
        return "period"
    }
    
    /// Returns the weekly equivalent price for yearly subscriptions
    var displayWeeklyPrice: String {
        if id.contains("yearly") {
            // Calculate weekly price for yearly subscription
            let yearlyPrice = self.price
            let weeklyPrice = yearlyPrice / Decimal(52)
            
            let formatter = NumberFormatter()
            formatter.numberStyle = .currency
            formatter.locale = self.priceFormatStyle.locale
            
            return formatter.string(from: weeklyPrice as NSDecimalNumber) ?? displayPrice
        }
        return displayPrice
    }
    
    /// Returns savings percentage for yearly vs weekly plans
    func displaySavings(comparedTo weeklyProduct: Product?) -> String? {
        guard id.contains("yearly"),
              let weeklyProduct = weeklyProduct else { return nil }
        
        let yearlyWeeklyPrice = self.price / Decimal(52)
        let weeklyPrice = weeklyProduct.price
        
        guard weeklyPrice > 0 else { return nil }
        
        let savings = (weeklyPrice - yearlyWeeklyPrice) / weeklyPrice * 100
        let roundedSavings = Int(NSDecimalNumber(decimal: savings).doubleValue.rounded())
        
        return "Save \(roundedSavings)%"
    }
    
    /// Returns formatted price with period
    var displayPriceWithPeriod: String {
        return "\(displayPrice) per \(displayPeriod)"
    }
    
    /// Returns starting price text for UI display
    var displayStartingPrice: String {
        return "Starting at \(displayPrice)/\(displayPeriod)"
    }
}

// MARK: - SubscriptionManager Error Extension
extension SubscriptionManagerError: LocalizedError {
    public var errorDescription: String? {
        switch self {
        case .failedVerification:
            return "Purchase verification failed"
        case .unknown:
            return "Unknown error occurred"
        }
    }
}
