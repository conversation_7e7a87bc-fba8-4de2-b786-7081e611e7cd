//
//  NetworkMonitor.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import Network
import Combine
import SwiftUI

@MainActor
class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    @Published var isConnected = true
    @Published var connectionType: ConnectionType = .unknown
    
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown
    }
    
    private let networkMonitor = NWPathMonitor()
    private let workerQueue = DispatchQueue(label: "NetworkMonitor")
    
    private init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            
            // Determine connection type locally
            let isConnected = path.status == .satisfied
            let connectionType: ConnectionType
            
            if path.usesInterfaceType(.wifi) {
                connectionType = .wifi
            } else if path.usesInterfaceType(.cellular) {
                connectionType = .cellular
            } else if path.usesInterfaceType(.wiredEthernet) {
                connectionType = .ethernet
            } else {
                connectionType = .unknown
            }
            
            Task {
                await MainActor.run {
                    self.isConnected = isConnected
                    self.connectionType = connectionType
                }
            }
        }
        networkMonitor.start(queue: workerQueue)
    }
    
    private func getConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
    
    deinit {
        networkMonitor.cancel()
    }
}

// MARK: - Network Status View
struct NetworkStatusView: View {
    @ObservedObject var networkMonitor = NetworkMonitor.shared
    @State private var showOfflineMessage = false
    
    var body: some View {
        Group {
            if !networkMonitor.isConnected && showOfflineMessage {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "wifi.slash")
                            .foregroundColor(.red)
                        Text("No Internet Connection")
                            .font(.subheadline)
                            .foregroundColor(.red)
                        Spacer()
                        Button("Dismiss") {
                            withAnimation {
                                showOfflineMessage = false
                            }
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                    
                    Text("Some features may not work without an internet connection.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .onChange(of: networkMonitor.isConnected) { _, isConnected in
            if !isConnected {
                withAnimation(.easeInOut) {
                    showOfflineMessage = true
                }
            } else {
                withAnimation(.easeInOut) {
                    showOfflineMessage = false
                }
            }
        }
        .onAppear {
            if !networkMonitor.isConnected {
                showOfflineMessage = true
            }
        }
    }
}
