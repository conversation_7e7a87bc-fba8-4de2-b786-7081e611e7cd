//
//  GenerateButton.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct GenerateButton: View {
    let isLoading: Bool
    let canGenerate: Bool
    let loadingMessageProvider: LoadingMessageProvider?
    let action: () -> Void
    
    @State private var heartPulse: Bool = false
    @State private var buttonPress: Bool = false
    
    var body: some View {
        Button(action: {
            if canGenerate && !isLoading {
                // Simple haptic feedback
                let impact = UIImpactFeedbackGenerator(style: .medium)
                impact.impactOccurred()
                
                // Execute action immediately
                action()
            }
        }) {
            HStack(spacing: 12) {
                if isLoading {
                    // Beautiful animated loading spinner with better visibility
                    ZStack {
                        Circle()
                            .stroke(Color.white.opacity(0.4), lineWidth: 3)
                            .frame(width: 22, height: 22)
                        
                        Circle()
                            .trim(from: 0, to: 0.7)
                            .stroke(Color.white, lineWidth: 3)
                            .frame(width: 22, height: 22)
                            .rotationEffect(.degrees(buttonPress ? 360 : 0))
                            .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: buttonPress)
                    }
                    .onAppear {
                        buttonPress = true
                    }
                } else {
                    Image(systemName: "heart.fill")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .scaleEffect(heartPulse ? 1.15 : 1.0)
                        .onAppear {
                            startHeartbeat()
                        }
                }
                
                if isLoading {
                    if let messageProvider = loadingMessageProvider {
                        Text(messageProvider.currentMessage)
                            .font(.system(size: 18, weight: .black, design: .rounded))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .lineLimit(1)
                            .minimumScaleFactor(0.75)
                    } else {
                        Text("Generating...")
                            .font(.system(size: 18, weight: .black, design: .rounded))
                            .foregroundColor(.white)
                    }
                } else {
                    Text("Generate Flirt")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(buttonGradient)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .scaleEffect(buttonPress ? 0.98 : 1.0)
        }
        .buttonStyle(.plain)
        .disabled(!canGenerate || isLoading)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                buttonPress = pressing
            }
        }, perform: {})
    }
    
    private func startHeartbeat() {
        // Create heartbeat pattern: beat-beat-pause
        Timer.scheduledTimer(withTimeInterval: 1.2, repeats: true) { _ in
            // First beat
            withAnimation(.easeInOut(duration: 0.1)) {
                heartPulse = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    heartPulse = false
                }
                
                // Second beat
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        heartPulse = true
                    }
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.easeInOut(duration: 0.1)) {
                            heartPulse = false
                        }
                    }
                }
            }
        }
    }
    
    private var buttonGradient: LinearGradient {
        if canGenerate || isLoading {
            return LinearGradient(
                colors: [.pink, .red],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                colors: [.gray, .gray.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        GenerateButton(isLoading: false, canGenerate: true, loadingMessageProvider: nil, action: {})
        GenerateButton(isLoading: true, canGenerate: true, loadingMessageProvider: LoadingMessageProvider(), action: {})
        GenerateButton(isLoading: false, canGenerate: false, loadingMessageProvider: nil, action: {})
    }
    .padding()
}