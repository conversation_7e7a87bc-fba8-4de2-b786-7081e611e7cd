//
//  PlanDetailsView.swift
//  HeartStarter
//
//  Created by <PERSON> for displaying plan details for both free and premium users.
//

import SwiftUI

struct PlanDetailsView: View {
    let userStats: UserStats
    let onUpgrade: () -> Void
    let onManageSubscription: () -> Void
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Plan Header
                PlanHeader(userStats: userStats)
                
                if userStats.isPremium {
                    // Premium Plan Content
                    PremiumPlanContent(userStats: userStats, onManageSubscription: onManageSubscription)
                } else {
                    // Free Plan Content
                    FreePlanContent(userStats: userStats, onUpgrade: onUpgrade)
                }
                
                Spacer(minLength: 20)
            }
            .padding(.horizontal, 20)
        }
        .navigationTitle("Plan Details")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            // Check for subscription cancellation status updates when plan details view appears
            Task {
                await SubscriptionManager.shared.checkAndUpdateCancellationStatus()
            }
        }
    }
}

// MARK: - Plan Header
struct PlanHeader: View {
    let userStats: UserStats
    
    var body: some View {
        VStack(spacing: 16) {
            // Plan Icon and Badge
            HStack(spacing: 16) {
                Image(systemName: userStats.isPremium ? "crown.fill" : "heart.circle.fill")
                    .font(.system(size: 40))
                    .foregroundStyle(userStats.isPremium ? 
                        LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing) :
                        LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(userStats.subscriptionTier.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if userStats.subscriptionInfo.isInActiveFreeTrial {
                        let daysRemaining = userStats.subscriptionInfo.daysRemainingInTrial
                        if daysRemaining > 0 {
                            Text("\(daysRemaining) day\(daysRemaining == 1 ? "" : "s") left in trial")
                                .font(.caption)
                                .foregroundColor(.blue)
                        } else {
                            let hoursRemaining = userStats.subscriptionInfo.hoursRemainingInTrial
                            Text("\(hoursRemaining) hour\(hoursRemaining == 1 ? "" : "s") left in trial")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .gray.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Free Plan Content
struct FreePlanContent: View {
    let userStats: UserStats
    let onUpgrade: () -> Void
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        VStack(spacing: 20) {
            // What's Included Section
            FeatureSection(
                title: "✅ What's Included",
                titleColor: .primary,
                features: [
                    FeatureItem(text: "3 messages per day", isIncluded: true),
                    FeatureItem(text: "All spice levels (Mild, Medium, Spicy)", isIncluded: true),
                    FeatureItem(text: "AI-powered flirt generation", isIncluded: true)
                ]
            )
            
            // Premium Features Not Included Section
            VStack(alignment: .leading, spacing: 12) {
                Text("🚫 Premium Features not included")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    DetailedPremiumFeatureRow(
                        icon: "infinity",
                        title: "Unlimited Daily Messages",
                        description: "Generate unlimited flirt messages every day",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "face.smiling",
                        title: "All Tone Options",
                        description: "Access Witty, Romantic, and Cheesy tones",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "wand.and.stars",
                        title: "Personal Touch",
                        description: "Add personal context for customized messages",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "heart.fill",
                        title: "Save Favorite Messages",
                        description: "Build your collection with cloud sync",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "icloud.fill",
                        title: "Cross-Device Sync",
                        description: "Access favorites and stats on all devices",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "sparkles",
                        title: "Priority Generation",
                        description: "Fresh, unique messages with anti-repetition",
                        gradient: LinearGradient(colors: [.gray, .gray], startPoint: .leading, endPoint: .trailing)
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )
            )
            
            // Upgrade Button
            Button(action: onUpgrade) {
                HStack(spacing: 12) {
                    Image(systemName: "crown.fill")
                        .foregroundStyle(LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Upgrade to Premium")
                            .fontWeight(.semibold)
                        Text(subscriptionManager.freeTrialText())
                            .font(.caption)
                            .opacity(0.8)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.title3)
                }
                .foregroundColor(.white)
                .padding(16)
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
        }
    }
}

// MARK: - Premium Plan Content
struct PremiumPlanContent: View {
    let userStats: UserStats
    let onManageSubscription: () -> Void
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var restoreInProgress = false
    @State private var restoreAlertMessage: String?

    var body: some View {
        VStack(spacing: 20) {
            // Premium Features Section
            VStack(alignment: .leading, spacing: 12) {
                Text("✅ Premium Features")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                VStack(spacing: 8) {
                    DetailedPremiumFeatureRow(
                        icon: "infinity",
                        title: "Unlimited Daily Messages",
                        description: "Generate unlimited flirt messages every day",
                        gradient: LinearGradient(colors: [.blue, .cyan], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "face.smiling",
                        title: "All Tone Options",
                        description: "Access Witty, Romantic, and Cheesy tones",
                        gradient: LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "wand.and.stars",
                        title: "Personal Touch",
                        description: "Add personal context for customized messages",
                        gradient: LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "heart.fill",
                        title: "Save Favorite Messages",
                        description: "Build your collection with cloud sync",
                        gradient: LinearGradient(colors: [.red, .pink], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "icloud.fill",
                        title: "Cross-Device Sync",
                        description: "Access favorites and stats on all devices",
                        gradient: LinearGradient(colors: [.orange, .yellow], startPoint: .leading, endPoint: .trailing)
                    )
                    
                    DetailedPremiumFeatureRow(
                        icon: "sparkles",
                        title: "Priority Generation",
                        description: "Fresh, unique messages with anti-repetition",
                        gradient: LinearGradient(colors: [.indigo, .blue], startPoint: .leading, endPoint: .trailing)
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )
            )
            
            // Subscription Info Section
            VStack(alignment: .leading, spacing: 12) {
                Text("Subscription Details")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(spacing: 8) {
                    // Subscription Status
                    HStack {
                        Text("Status")
                        Spacer()
                        Text(userStats.subscriptionInfo.subscriptionStatusText)
                            .foregroundColor(userStats.subscriptionInfo.subscriptionStatusColor == "green" ? .green : 
                                           userStats.subscriptionInfo.subscriptionStatusColor == "orange" ? .orange :
                                           userStats.subscriptionInfo.subscriptionStatusColor == "blue" ? .blue : .secondary)
                    }
                    
                    // Billing/Expiry Date
                    if let expiryDate = subscriptionManager.subscriptionExpiryDate {
                        HStack {
                            Text(userStats.subscriptionInfo.isCancelled ? "Premium access until" : "Next billing")
                            Spacer()
                            Text(expiryDate, style: .date)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Auto-renewal status for cancelled subscriptions
                    if userStats.subscriptionInfo.isCancelled {
                        HStack {
                            Text("Auto-renewal")
                            Spacer()
                            Text("Turned off")
                                .foregroundColor(.orange)
                        }
                    }
                    
                    HStack {
                        Text("Plan type")
                        Spacer()
                        if userStats.subscriptionInfo.isInActiveFreeTrial {
                            Text("Free Trial")
                                .foregroundColor(.secondary)
                        } else {
                            let subscriptionType = subscriptionManager.getActiveSubscriptionType()
                            Text("Premium \(subscriptionType.type)")
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if !userStats.subscriptionInfo.isInActiveFreeTrial {
                        HStack {
                            Text("Amount")
                            Spacer()
                            let subscriptionType = subscriptionManager.getActiveSubscriptionType()
                            let productId: SubscriptionManager.ProductID = subscriptionType.isWeekly ? .premiumWeekly : .premiumYearly
                            Text(subscriptionManager.formattedPrice(for: productId))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if userStats.subscriptionInfo.isInActiveFreeTrial {
                        HStack {
                            Text("Trial remaining")
                            Spacer()
                            let daysRemaining = userStats.subscriptionInfo.daysRemainingInTrial
                            if daysRemaining > 0 {
                                Text("\(daysRemaining) day\(daysRemaining == 1 ? "" : "s")")
                                    .foregroundColor(.blue)
                            } else {
                                let hoursRemaining = userStats.subscriptionInfo.hoursRemainingInTrial
                                Text("\(hoursRemaining) hour\(hoursRemaining == 1 ? "" : "s")")
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
                .font(.subheadline)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            Button(action: {
                restoreInProgress = true
                Task {
                    let success = await subscriptionManager.restorePurchases()
                    await MainActor.run {
                        restoreInProgress = false
                        if success {
                            restoreAlertMessage = "Purchases restored successfully."
                        } else {
                            restoreAlertMessage = subscriptionManager.errorMessage ?? "No previous purchases found for this Apple ID."
                        }
                        subscriptionManager.errorMessage = nil
                    }
                }
            }) {
                HStack(spacing: 12) {
                    if restoreInProgress {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .frame(width: 16, height: 16)
                            .tint(.blue)
                    } else {
                        Image(systemName: "arrow.clockwise.circle")
                            .foregroundColor(.blue)
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text(restoreInProgress ? "Restoring..." : "Restore Purchases")
                            .fontWeight(.medium)
                        Text("Re-link your Premium access")
                            .font(.caption)
                            .opacity(0.8)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.title3)
                        .foregroundColor(.secondary)
                }
                .foregroundColor(.primary)
                .padding(16)
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .disabled(restoreInProgress)
        }
        .alert("Restore Purchases", isPresented: Binding(
            get: { restoreAlertMessage != nil },
            set: { if !$0 { restoreAlertMessage = nil } }
        )) {
            Button("OK", role: .cancel) { restoreAlertMessage = nil }
        } message: {
            Text(restoreAlertMessage ?? "")
        }
    }
}

// MARK: - Feature Section
struct FeatureSection: View {
    let title: String
    let titleColor: Color
    let features: [FeatureItem]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(titleColor)
            
            VStack(spacing: 8) {
                ForEach(features, id: \.text) { feature in
                    HStack(spacing: 12) {
                        Image(systemName: feature.isIncluded ? "checkmark.circle.fill" : "minus.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(feature.isIncluded ? .green : .secondary)
                        
                        Text(feature.text)
                            .font(.subheadline)
                            .foregroundColor(feature.isIncluded ? .primary : .secondary)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
        )
    }
}

// MARK: - Feature Item
struct FeatureItem {
    let text: String
    let isIncluded: Bool
}

#Preview {
    NavigationView {
        PlanDetailsView(
            userStats: UserStats(),
            onUpgrade: {},
            onManageSubscription: {}
        )
    }
}
