//
//  PremiumUpgradeCard.swift
//  HeartStarter
//
//  Created by <PERSON> for premium upgrade display to free users.
//

import SwiftUI

struct PremiumUpgradeCard: View {
    let onUpgrade: () -> Void
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        Button(action: onUpgrade) {
            HStack(spacing: 16) {
                // Crown icon
                Image(systemName: "crown.fill")
                    .font(.title2)
                    .foregroundStyle(LinearGradient(
                        colors: [.yellow, .orange],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                
                // Content
                Text("Upgrade to Premium")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Arrow
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title3)
                    .foregroundColor(.accentColor)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(LinearGradient(
                                colors: [.yellow.opacity(0.3), .orange.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ), lineWidth: 1)
                    )
                    .shadow(color: .orange.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    VStack(spacing: 20) {
        PremiumUpgradeCard(onUpgrade: {})
            .padding()
        
        // For comparison - what premium users would NOT see
        Text("Premium users don't see this card")
            .font(.caption)
            .foregroundColor(.secondary)
            .padding()
    }
    .background(Color(.systemGroupedBackground))
}