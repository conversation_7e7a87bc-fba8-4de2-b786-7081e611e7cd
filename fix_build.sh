#!/bin/bash

echo "🔧 HeartStarter Build Fix Script"
echo "================================="

# Kill Xcode processes
echo "1. Killing Xcode processes..."
killall Xcode 2>/dev/null
killall Simulator 2>/dev/null
sleep 2

# Clear all build caches
echo "2. Clearing build caches..."
rm -rf ~/Library/Developer/Xcode/DerivedData/*
rm -rf ~/Library/Caches/com.apple.dt.Xcode*
rm -rf ~/Library/Developer/Xcode/UserData/IDEEditorInteractivityHistory
rm -rf ~/Library/Developer/Xcode/UserData/IDEFindNavigatorScopes.plist

# Clear project specific caches
echo "3. Clearing project caches..."
find . -name "*.xcuserdata" -exec rm -rf {} + 2>/dev/null
find . -name "project.xcworkspace" -exec rm -rf {} + 2>/dev/null

# Verify Swift files are valid
echo "4. Validating Swift syntax..."
cd HeartStarter
for file in *.swift; do
    if ! swiftc -parse "$file" 2>/dev/null; then
        echo "❌ Syntax error in $file"
        exit 1
    fi
done
cd ..

echo "5. Build troubleshooting steps:"
echo "   a) Open Xcode"
echo "   b) Open HeartStarter.xcodeproj"
echo "   c) Go to Product → Clean Build Folder (Cmd+Shift+K)"
echo "   d) Go to Product → Build (Cmd+B)"
echo ""
echo "6. If build still fails:"
echo "   - Check Xcode version (should be 15.0+)"
echo "   - Try a different simulator (iPhone 15 instead of iPhone 17)"
echo "   - Restart your Mac if the issue persists"
echo ""
echo "✅ Cleanup complete! Try building in Xcode now."