//  FavoriteListView.swift
//  HeartStarter
//
//  Created by the assistant for premium users to view their favorite jokes.

import SwiftUI

struct FavoriteListView: View {
    let favorites: [JokeMessage]
    var onClose: () -> Void
    let onUpgradeRequested: () -> Void
    let onRemoveFavorite: (JokeMessage) -> Void

    @EnvironmentObject var statsManager: UserStatsManager
    @State private var showingShareSheet: Bool = false
    @State private var messageToShare: JokeMessage? = nil
    @State private var textsToShare: [String] = []
    @State private var showCopiedFeedback: Bool = false
    @State private var showingRemoveConfirmation: Bool = false
    @State private var messageToRemove: JokeMessage? = nil
    @State private var isSelectionMode: Bool = false
    @State private var selectedMessages: Set<String> = []
    @State private var showingBatchRemoveConfirmation: Bool = false

    var body: some View {
        NavigationView {
            if !statsManager.stats.allowsFavorites {
                premiumGateView
            } else {
                favoritesContentView
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .alert("Remove Favourite?", isPresented: Binding<Bool>(
            get: { messageToRemove != nil },
            set: { if !$0 { messageToRemove = nil } }
        )) {
            Button("Remove", role: .destructive) {
                if let message = messageToRemove {
                    onRemoveFavorite(message)
                    messageToRemove = nil
                }
            }

            Button("Cancel", role: .cancel) {
                messageToRemove = nil
            }
        } message: {
            Text("This joke will be permanently removed from your favourites.")
        }
        .alert("Remove Selected Favourites?", isPresented: $showingBatchRemoveConfirmation) {
            Button("Remove", role: .destructive) {
                batchRemoveFavorites()
            }

            Button("Cancel", role: .cancel) { }
        } message: {
            Text("These \(selectedMessages.count) jokes will be permanently removed from your favourites.")
        }
        .safeAreaInset(edge: .bottom, spacing: 0) {
            if isSelectionMode && !selectedMessages.isEmpty {
                batchActionBar
            }
        }
    }

    private var premiumGateView: some View {
        VStack(spacing: 24) {
            Spacer()

            premiumGateHeader
            premiumFeaturesList
            premiumUpgradeButton

            Spacer()
        }
        .padding(.horizontal, 24)
        .navigationTitle("Favorites")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Close") {
                    onClose()
                }
            }
        }
    }

    private var premiumGateHeader: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)

            VStack(spacing: 8) {
                Text("Favorites")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Save your favorite jokes and access them anytime")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }

    private var premiumFeaturesList: some View {
        VStack(alignment: .leading, spacing: 12) {
            FeatureRow(icon: "heart.fill", title: "Save Unlimited Jokes", description: "Keep your best jokes")
            FeatureRow(icon: "icloud.fill", title: "Sync Across Devices", description: "Access favorites on all your devices")
            FeatureRow(icon: "square.and.arrow.up", title: "Easy Sharing", description: "Copy and share your favorites instantly")
        }
        .padding(.horizontal, 16)
    }

    private var premiumUpgradeButton: some View {
        Button {
            onUpgradeRequested()
        } label: {
            Text("Upgrade to Premium")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: [.blue, .cyan],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
        }
        .buttonStyle(.plain)
    }

    private var favoritesContentView: some View {
        Group {
            if favorites.isEmpty {
                emptyFavoritesView
            } else {
                List {
                    ForEach(favorites, id: \.id) { message in
                        favoriteMessageRow(message)
                    }
                    .onDelete(perform: { offsets in
                        if let index = offsets.first {
                            messageToRemove = favorites[index]
                        }
                    })
                }
                .listStyle(.plain)
                .background(Color(.systemGroupedBackground))
            }
        }
        .navigationTitle("Favorites")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Close") {
                    onClose()
                }
            }
        }
    }

    private var emptyFavoritesView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "heart.slash")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Favorites Yet")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Start saving your favorite jokes by tapping the heart icon")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }

            Spacer()
        }
    }

    private func favoriteMessageRow(_ message: JokeMessage) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top) {
                // Selection circle (only in selection mode)
                if isSelectionMode {
                    Button {
                        toggleSelection(for: message.id)
                    } label: {
                        Image(systemName: selectedMessages.contains(message.id) ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 20))
                            .foregroundColor(selectedMessages.contains(message.id) ? .blue : .secondary)
                    }
                    .buttonStyle(.plain)
                }

                VStack(alignment: .leading, spacing: 8) {
                    // Message text
                    Text(message.text)
                        .font(.body)
                        .fixedSize(horizontal: false, vertical: true)

                    // Category and spice level badges
                    HStack(spacing: 8) {
                        // Category badge
                        if let category = message.category {
                            HStack(spacing: 3) {
                                Text(category.emoji)
                                    .font(.system(size: 9))
                                Text(category.rawValue)
                                    .font(.caption2)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                Capsule()
                                    .fill(.secondary.opacity(0.1))
                            )
                            .foregroundColor(.secondary)
                        }

                        // Spice level badge
                        HStack(spacing: 3) {
                            Text(message.spiceLevel.emoji)
                                .font(.system(size: 9))
                            Text(message.spiceLevel.rawValue)
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(message.spiceLevel.color.opacity(0.1))
                        )
                        .foregroundColor(message.spiceLevel.color)

                        // Personal Touch Badge
                        if message.hasPersonalTouch {
                            HStack(spacing: 3) {
                                Image(systemName: "person.fill")
                                    .font(.system(size: 9))
                                Text("Personal")
                                    .font(.caption2)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                Capsule()
                                    .fill(.secondary.opacity(0.1))
                            )
                            .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    // Right-side actions: Show only in non-selection mode
                    if !isSelectionMode {
                        HStack(spacing: 20) {
                            Button {
                                copyToClipboard(message.text)
                            } label: {
                                Image(systemName: "doc.on.doc")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.blue)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("Copy")

                            Button {
                                messageToShare = message
                                showingShareSheet = true
                            } label: {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.green)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("Share")

                            Button {
                                messageToRemove = message
                            } label: {
                                Image(systemName: "heart.slash")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.red)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("Remove from favorites")
                        }
                    }
                }

                // Footer date
                Text(message.createdAt, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            if isSelectionMode {
                toggleSelection(for: message.id)
            }
        }
    }

    private var batchActionBar: some View {
        BatchActionBar(
            selectedCount: selectedMessages.count,
            onCopy: batchCopyMessages,
            onShare: batchShareMessages,
            onRemove: {
                showingBatchRemoveConfirmation = true
            }
        )
    }

    private func toggleSelection(for messageId: String) {
        if selectedMessages.contains(messageId) {
            selectedMessages.remove(messageId)
        } else {
            selectedMessages.insert(messageId)
        }
    }
    
    private func batchCopyMessages() {
        let selectedTexts = favorites
            .filter { selectedMessages.contains($0.id) }
            .map { $0.text }
            .joined(separator: "\n\n")
        
        UIPasteboard.general.string = selectedTexts
        
        // Show toast feedback
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopiedFeedback = true
        }
        
        // Hide after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopiedFeedback = false
            }
        }
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
        
        // Haptic feedback
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(.success)
    }
    
    private func batchShareMessages() {
        textsToShare = favorites
            .filter { selectedMessages.contains($0.id) }
            .map { $0.text }
        
        messageToShare = nil // Clear single message
        showingShareSheet = true
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
    }
    
    private func batchRemoveFavorites() {
        let messagesToRemove = favorites.filter { selectedMessages.contains($0.id) }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.2)) {
            for message in messagesToRemove {
                statsManager.removeFavorite(message)
            }
        }
        
        let impact = UIImpactFeedbackGenerator(style: .soft)
        impact.prepare()
        impact.impactOccurred()
        
        // Exit selection mode
        isSelectionMode = false
        selectedMessages.removeAll()
    }
    
    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
        
        // Show toast feedback
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopiedFeedback = true
        }
        
        // Hide after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopiedFeedback = false
            }
        }
        
        // Haptic feedback
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(.success)
    }

struct BatchActionBar: View {
    let selectedCount: Int
    let onCopy: () -> Void
    let onShare: () -> Void
    let onRemove: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(height: 0.5)
            
            HStack {
                Text("\(selectedCount) selected")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 24) {
                    Button(action: onCopy) {
                        VStack(spacing: 4) {
                            Image(systemName: "doc.on.doc")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.blue)
                            Text("Copy")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: onShare) {
                        VStack(spacing: 4) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.green)
                            Text("Share")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: onRemove) {
                        VStack(spacing: 4) {
                            Image(systemName: "trash")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.red)
                            Text("Remove")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
        }
    }
}
}

#Preview {
    VStack(spacing: 20) {
        FavoriteListView(favorites: [], onClose: {}, onUpgradeRequested: {}, onRemoveFavorite: { _ in })
            .environmentObject(UserStatsManager())
    }
}
