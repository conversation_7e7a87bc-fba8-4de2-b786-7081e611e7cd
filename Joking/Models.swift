//
//  Models.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import SwiftUI

// MARK: - Spice Level
enum SpiceLevel: String, CaseIterable, Codable {
    case mild = "Mild"
    case medium = "Medium"
    case savage = "Savage"
    
    var emoji: String {
        switch self {
        case .mild: return "😊"
        case .medium: return "😄"
        case .savage: return "💀"
        }
    }
    
    var color: Color {
        switch self {
        case .mild: return .green
        case .medium: return .orange
        case .savage: return .red
        }
    }
    
    var description: String {
        switch self {
        case .mild: return "Light & Friendly"
        case .medium: return "Witty & Sharp"
        case .savage: return "Dark & Brutal"
        }
    }
}

// MARK: - Joke Category
enum JokeCategory: String, CaseIterable, Codable {
    case dadJokes = "Dad Jokes"
    case darkHumor = "Dark Humor"
    case puns = "Puns"
    case pickupLines = "Pick-up Lines"
    case geekJokes = "Geek Jokes"
    case workJokes = "Work Jokes"

    var emoji: String {
        switch self {
        case .dadJokes: return "👨‍👧‍👦"
        case .darkHumor: return "🖤"
        case .puns: return "🎭"
        case .pickupLines: return "💘"
        case .geekJokes: return "🤓"
        case .workJokes: return "💼"
        }
    }
}

// MARK: - Joke Message
struct JokeMessage: Codable, Identifiable, Hashable {
    let id: String
    let text: String
    let spiceLevel: SpiceLevel
    let category: JokeCategory?
    let hasPersonalTouch: Bool
    let createdAt: Date

    init(text: String, spiceLevel: SpiceLevel, category: JokeCategory? = nil, hasPersonalTouch: Bool = false) {
        self.id = UUID().uuidString
        self.text = text
        self.spiceLevel = spiceLevel
        self.category = category
        self.hasPersonalTouch = hasPersonalTouch
        self.createdAt = Date()
    }

    // Custom initializer for CloudKit integration
    init(id: String, text: String, spiceLevel: SpiceLevel, category: JokeCategory?, hasPersonalTouch: Bool = false, createdAt: Date) {
        self.id = id
        self.text = text
        self.spiceLevel = spiceLevel
        self.category = category
        self.hasPersonalTouch = hasPersonalTouch
        self.createdAt = createdAt
    }

    // MARK: - Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: JokeMessage, rhs: JokeMessage) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Subscription Tier
enum SubscriptionTier: String, Codable, CaseIterable {
    case free = "free"
    case premium = "premium"
    
    var displayName: String {
        switch self {
        case .free: return "Free Plan"
        case .premium: return "Premium"
        }
    }
    
    var dailyMessageLimit: Int {
        switch self {
        case .free: return 3
        case .premium: return Int.max // Unlimited
        }
    }
    
    var allowsCategorySelection: Bool {
        switch self {
        case .free: return false
        case .premium: return true
        }
    }
    
    var allowsPersonalTouch: Bool {
        switch self {
        case .free: return false
        case .premium: return true
        }
    }
    
    var allowsFavorites: Bool {
        switch self {
        case .free: return false
        case .premium: return true
        }
    }
}

// MARK: - Subscription Info
struct SubscriptionInfo: Codable {
    var tier: SubscriptionTier = .free
    var startDate: Date?
    var expiryDate: Date?
    var productId: String?
    var hasUsedFreeTrial: Bool = false
    var autoRenewEnabled: Bool = false
    
    // Cancellation status tracking
    var isCancelled: Bool = false
    var willAutoRenew: Bool = true
    
    // Free Trial Properties
    var isInFreeTrial: Bool = false
    var freeTrialStartDate: Date?
    var freeTrialEndDate: Date?
    var trialGracePeriodDays: Int = 3
    
    var isActive: Bool {
        // Check if in active free trial first
        if isInFreeTrial, let trialEndDate = freeTrialEndDate {
            return Date() < trialEndDate
        }
        
        // Then check regular subscription
        guard let expiryDate = expiryDate else {
            // Free tier is always active, premium without expiry (managed by Apple) is also active
            return tier == .free || tier == .premium
        }
        return Date() < expiryDate
    }
    
    var isExpired: Bool {
        // Check trial expiration
        if isInFreeTrial, let trialEndDate = freeTrialEndDate {
            return Date() >= trialEndDate
        }
        
        // Check regular subscription expiration
        guard let expiryDate = expiryDate else {
            return false // Free tier never expires
        }
        return Date() >= expiryDate
    }
    
    var subscriptionStatusText: String {
        if isInActiveFreeTrial {
            return "TRIAL"
        }
        if effectiveTier == .premium {
            return isCancelled ? "ENDING" : "ACTIVE"
        }
        return "FREE"
    }
    
    var subscriptionStatusColor: String {
        switch subscriptionStatusText {
        case "ACTIVE":
            return "green"
        case "ENDING":
            return "orange"
        case "TRIAL":
            return "blue"
        case "FREE":
            return "green"
        default:
            return "gray"
        }
    }
    
    var effectiveTier: SubscriptionTier {
        // If in active free trial, user gets premium access
        if isInActiveFreeTrial {
            return .premium
        }
        
        // Otherwise use regular logic
        return isActive ? tier : .free
    }
    
    var isInActiveFreeTrial: Bool {
        guard isInFreeTrial,
              let trialEndDate = freeTrialEndDate else {
            return false
        }
        return Date() < trialEndDate
    }
    
    var isTrialExpired: Bool {
        guard isInFreeTrial,
              let trialEndDate = freeTrialEndDate else {
            return false
        }
        return Date() >= trialEndDate
    }
    
    var daysRemainingInTrial: Int {
        guard isInActiveFreeTrial,
              let trialEndDate = freeTrialEndDate else {
            return 0
        }
        
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: Date(), to: trialEndDate).day ?? 0
        return max(0, days)
    }
    
    var hoursRemainingInTrial: Int {
        guard isInActiveFreeTrial,
              let trialEndDate = freeTrialEndDate else {
            return 0
        }
        
        let calendar = Calendar.current
        let hours = calendar.dateComponents([.hour], from: Date(), to: trialEndDate).hour ?? 0
        return max(0, hours)
    }
    
    var isEligibleForFreeTrial: Bool {
        return !hasUsedFreeTrial && tier == .free && !isInFreeTrial
    }
    
    var isInTrialGracePeriod: Bool {
        guard isTrialExpired,
              let trialEndDate = freeTrialEndDate else {
            return false
        }
        
        let calendar = Calendar.current
        guard let gracePeriodEndDate = calendar.date(byAdding: .day, value: trialGracePeriodDays, to: trialEndDate) else {
            return false
        }
        
        return Date() < gracePeriodEndDate
    }
}

// MARK: - User Stats
struct UserStats: Codable {
    var totalJokesGenerated: Int = 0
    var dailyJokeCount: Int = 0
    var lastResetDate: Date = Date()
    var seenMessageIds: Set<String> = []
    var subscriptionInfo: SubscriptionInfo = SubscriptionInfo()
    var favoriteMessages: [JokeMessage] = []
    var recentMessageHistory: [String] = []
    var recentMessageFingerprints: [String] = []
    var premiumPersonalTouchBoostRemaining: Int = 10
    
    // MARK: - Enhanced Usage Tracking & Abuse Prevention
    var dailyMessagesGenerated: Int = 0
    var hourlyMessagesGenerated: Int = 0
    var lastHourlyReset: Date = Date()
    var totalPersonalizedToday: Int = 0
    var abuseWarningCount: Int = 0
    var lastAbuseWarningDate: Date?
    var lastCooldownStart: Date?
    var cooldownDurationMinutes: Int = 60
    
    // MARK: - GPT Model Usage Tracking
    var dailyGPT5Messages: Int = 0
    var dailyGPT5MiniMessages: Int = 0
    var monthlyGPT5Messages: Int = 0
    var lastGPT5Reset: Date = Date()
    
    // MARK: - Cache Performance Metrics
    var cacheHitCount: Int = 0
    var cacheMissCount: Int = 0
    var totalAPICallsSaved: Int = 0
    
    // Computed properties for backward compatibility and convenience
    var isPremium: Bool {
        return subscriptionInfo.effectiveTier == .premium
    }
    
    var subscriptionTier: SubscriptionTier {
        return subscriptionInfo.effectiveTier
    }
    
    var dailyLimit: Int {
        return subscriptionTier.dailyMessageLimit
    }
    
    var canGenerateMore: Bool {
        return dailyJokeCount < dailyLimit
    }

    var allowsCategorySelection: Bool {
        return subscriptionTier.allowsCategorySelection
    }
    
    var allowsPersonalTouch: Bool {
        return subscriptionTier.allowsPersonalTouch
    }
    
    var allowsFavorites: Bool {
        return subscriptionTier.allowsFavorites
    }
    
    // MARK: - Usage Tracking Computed Properties
    var isInCooldown: Bool {
        guard let cooldownStart = lastCooldownStart else { return false }
        let cooldownEnd = Calendar.current.date(byAdding: .minute, value: cooldownDurationMinutes, to: cooldownStart) ?? Date()
        return Date() < cooldownEnd
    }
    
    var cooldownTimeRemaining: TimeInterval {
        guard let cooldownStart = lastCooldownStart else { return 0 }
        let cooldownEnd = Calendar.current.date(byAdding: .minute, value: cooldownDurationMinutes, to: cooldownStart) ?? Date()
        return max(0, cooldownEnd.timeIntervalSince(Date()))
    }
    
    var shouldShowAbuseWarning: Bool {
        guard isPremium else { return false }
        
        // Show warning at 50 messages if not shown today
        if dailyMessagesGenerated >= 50 {
            if let lastWarning = lastAbuseWarningDate {
                return !Calendar.current.isDate(lastWarning, inSameDayAs: Date())
            }
            return true
        }
        return false
    }
    
    var canGenerateMessage: Bool {
        // Check basic daily limits first
        guard canGenerateMore else { return false }
        
        // Premium users: check cooldown for abuse prevention
        if isPremium && isInCooldown {
            return false
        }
        
        return true
    }
    
    var cacheHitRate: Double {
        let total = cacheHitCount + cacheMissCount
        return total > 0 ? Double(cacheHitCount) / Double(total) : 0.0
    }
    
    mutating func incrementDailyCount() {
        let calendar = Calendar.current
        let today = Date()
        
        // Reset daily count if it's a new day
        if !calendar.isDate(lastResetDate, inSameDayAs: today) {
            dailyJokeCount = 0
            dailyMessagesGenerated = 0
            totalPersonalizedToday = 0
            dailyGPT5Messages = 0
            dailyGPT5MiniMessages = 0
            premiumPersonalTouchBoostRemaining = isPremium ? 10 : 0
            lastResetDate = today
        }
        
        // Reset hourly count if it's been an hour
        if !calendar.isDate(lastHourlyReset, equalTo: today, toGranularity: .hour) {
            hourlyMessagesGenerated = 0
            lastHourlyReset = today
        }
        
        // Reset monthly GPT-5 count if it's a new month
        if !calendar.isDate(lastGPT5Reset, equalTo: today, toGranularity: .month) {
            monthlyGPT5Messages = 0
            lastGPT5Reset = today
        }
        
        dailyJokeCount += 1
        dailyMessagesGenerated += 1
        hourlyMessagesGenerated += 1
        totalJokesGenerated += 1
    }
    
    mutating func markMessageAsSeen(_ messageId: String) {
        seenMessageIds.insert(messageId)
    }
    
    mutating func ingestGeneratedMessage(_ message: JokeMessage) {
        markMessageAsSeen(message.id)
        recordGeneratedMessage(message)
    }

    mutating func recordGeneratedMessage(_ message: JokeMessage) {
        recordMessageText(message.text)
    }
    
    mutating func consumePremiumPersonalTouchBoost() {
        if premiumPersonalTouchBoostRemaining > 0 {
            premiumPersonalTouchBoostRemaining -= 1
        }
    }
    
    mutating func resetPremiumPersonalTouchBoost() {
        premiumPersonalTouchBoostRemaining = 10
    }
    
    func shouldUsePremiumPersonalTouchBoost() -> Bool {
        premiumPersonalTouchBoostRemaining > 0
    }
    
    mutating func recordMessageText(_ text: String) {
        let fingerprint = fingerprint(for: text)
        guard !fingerprint.isEmpty else { return }
        if recentMessageFingerprints.contains(fingerprint) {
            return
        }
        recentMessageHistory.append(text)
        recentMessageFingerprints.append(fingerprint)
        enforceMessageHistoryLimit()
    }
    
    func hasSeenMessageText(_ text: String) -> Bool {
        let fingerprint = fingerprint(for: text)
        return recentMessageFingerprints.contains(fingerprint)
    }
    
    func recentExclusionList(maxCount: Int) -> [String] {
        guard maxCount > 0 else { return [] }
        if recentMessageHistory.count <= maxCount {
            return recentMessageHistory
        }
        return Array(recentMessageHistory.suffix(maxCount))
    }
    
    private mutating func enforceMessageHistoryLimit(_ limit: Int = 100) {
        guard limit > 0 else {
            recentMessageHistory.removeAll()
            recentMessageFingerprints.removeAll()
            return
        }
        while recentMessageHistory.count > limit {
            recentMessageHistory.removeFirst()
            recentMessageFingerprints.removeFirst()
        }
    }
    
    private func fingerprint(for text: String) -> String {
        text
            .folding(options: [.diacriticInsensitive, .caseInsensitive], locale: .current)
            .lowercased()
            .components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
    }
    
    mutating func addFavorite(_ message: JokeMessage) {
        // Avoid duplicates
        if !favoriteMessages.contains(where: { $0.id == message.id }) {
            favoriteMessages.append(message)
        }
    }
    
    // MARK: - Subscription Management
    mutating func updateSubscription(
        tier: SubscriptionTier,
        startDate: Date? = nil,
        expiryDate: Date? = nil,
        productId: String? = nil,
        isCancelled: Bool = false,
        willAutoRenew: Bool = true
    ) {
        subscriptionInfo.tier = tier

        switch tier {
        case .premium:
            let effectiveStart = startDate ?? subscriptionInfo.startDate ?? Date()
            subscriptionInfo.startDate = effectiveStart
            subscriptionInfo.expiryDate = expiryDate
            subscriptionInfo.productId = productId
            subscriptionInfo.autoRenewEnabled = willAutoRenew
            subscriptionInfo.isCancelled = isCancelled
            subscriptionInfo.willAutoRenew = willAutoRenew

        case .free:
            subscriptionInfo.startDate = startDate ?? subscriptionInfo.startDate
            subscriptionInfo.expiryDate = nil
            subscriptionInfo.productId = nil
            subscriptionInfo.autoRenewEnabled = false
            subscriptionInfo.isCancelled = false
            subscriptionInfo.willAutoRenew = false
            subscriptionInfo.isInFreeTrial = false
            subscriptionInfo.freeTrialStartDate = nil
            subscriptionInfo.freeTrialEndDate = nil

            // Reset usage counters so users get a fresh allowance when returning to free
            dailyJokeCount = 0
            dailyMessagesGenerated = 0
            hourlyMessagesGenerated = 0
            totalPersonalizedToday = 0
            lastCooldownStart = nil
            abuseWarningCount = 0
            lastAbuseWarningDate = nil
            lastResetDate = Date()
            lastHourlyReset = Date()
        }
    }

    // MARK: - Free Trial Management
    mutating func startFreeTrial(startDate: Date = Date(), endDate: Date? = nil, productId: String? = nil) {
        let calendar = Calendar.current

        subscriptionInfo.isInFreeTrial = true
        subscriptionInfo.freeTrialStartDate = startDate
        if let endDate {
            subscriptionInfo.freeTrialEndDate = endDate
        } else {
            subscriptionInfo.freeTrialEndDate = calendar.date(byAdding: .day, value: 7, to: startDate)
        }
        subscriptionInfo.hasUsedFreeTrial = true
        subscriptionInfo.productId = productId
        subscriptionInfo.tier = .premium // User gets premium access during trial
        subscriptionInfo.startDate = startDate
        subscriptionInfo.autoRenewEnabled = true
        subscriptionInfo.isCancelled = false
        subscriptionInfo.willAutoRenew = true
    }

    mutating func endFreeTrial() {
        subscriptionInfo.isInFreeTrial = false
        subscriptionInfo.freeTrialStartDate = nil
        subscriptionInfo.freeTrialEndDate = nil
        
        // If no active paid subscription, downgrade to free
        if subscriptionInfo.expiryDate == nil || subscriptionInfo.isExpired {
            subscriptionInfo.tier = .free
            subscriptionInfo.productId = nil
            subscriptionInfo.autoRenewEnabled = false
            subscriptionInfo.isCancelled = false
            subscriptionInfo.willAutoRenew = false
        }
    }
    
    mutating func convertTrialToPaidSubscription(expiryDate: Date, productId: String) {
        subscriptionInfo.isInFreeTrial = false
        subscriptionInfo.tier = .premium
        subscriptionInfo.expiryDate = expiryDate
        subscriptionInfo.productId = productId
        subscriptionInfo.autoRenewEnabled = true
        subscriptionInfo.startDate = Date()
        subscriptionInfo.freeTrialStartDate = nil
        subscriptionInfo.freeTrialEndDate = nil
        subscriptionInfo.isCancelled = false
        subscriptionInfo.willAutoRenew = true
    }
    
    mutating func setFreeTrial() {
        subscriptionInfo.hasUsedFreeTrial = true
    }
    
    mutating func cancelSubscription() {
        // Keep the tier but mark auto-renew as disabled
        subscriptionInfo.autoRenewEnabled = false
        subscriptionInfo.isCancelled = true
        subscriptionInfo.willAutoRenew = false
    }

    mutating func restoreSubscription(
        tier: SubscriptionTier, 
        expiryDate: Date?, 
        productId: String?,
        isCancelled: Bool = false,
        willAutoRenew: Bool = true
    ) {
        subscriptionInfo.tier = tier
        subscriptionInfo.expiryDate = expiryDate
        subscriptionInfo.productId = productId
        subscriptionInfo.autoRenewEnabled = tier == .premium && willAutoRenew
        subscriptionInfo.isCancelled = isCancelled
        subscriptionInfo.willAutoRenew = willAutoRenew
    }
    
    // MARK: - Enhanced Usage Tracking Methods
    mutating func recordPersonalizedMessage() {
        totalPersonalizedToday += 1
    }
    
    mutating func recordGPT5Usage() {
        dailyGPT5Messages += 1
        monthlyGPT5Messages += 1
    }
    
    mutating func recordGPT5MiniUsage() {
        dailyGPT5MiniMessages += 1
    }
    
    mutating func recordModelUsage(model: String) {
        if model.contains("gpt-5-2025") {
            recordGPT5Usage()
        } else {
            recordGPT5MiniUsage()
        }
    }
    
    mutating func recordCacheHit() {
        cacheHitCount += 1
        totalAPICallsSaved += 1
    }
    
    mutating func recordCacheMiss() {
        cacheMissCount += 1
    }
    
    mutating func showAbuseWarning() {
        abuseWarningCount += 1
        lastAbuseWarningDate = Date()
    }
    
    mutating func startCooldown() {
        lastCooldownStart = Date()
    }
    
    mutating func shouldTriggerCooldown() -> Bool {
        // Trigger cooldown for premium users after 100 messages
        return isPremium && dailyMessagesGenerated >= 100 && (dailyMessagesGenerated % 100 == 0)
    }
    
    mutating func resetCountersIfNeeded() {
        let calendar = Calendar.current
        let today = Date()
        
        // Reset daily counters
        if !calendar.isDate(lastResetDate, inSameDayAs: today) {
            let prevDaily = dailyJokeCount
            let prevGenerated = dailyMessagesGenerated
            dailyJokeCount = 0
            dailyMessagesGenerated = 0
            totalPersonalizedToday = 0
            dailyGPT5Messages = 0
            dailyGPT5MiniMessages = 0
            lastResetDate = today
            debugLog("🔄 Daily counters reset – previous dailyJokeCount=\(prevDaily), previous dailyMessagesGenerated=\(prevGenerated)")
        }
        
        // Reset hourly counters
        if !calendar.isDate(lastHourlyReset, equalTo: today, toGranularity: .hour) {
            hourlyMessagesGenerated = 0
            lastHourlyReset = today
        }
        
        // Reset monthly counters
        if !calendar.isDate(lastGPT5Reset, equalTo: today, toGranularity: .month) {
            monthlyGPT5Messages = 0
            lastGPT5Reset = today
        }
    }
}
