//
//  JokeService.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import Combine

// MARK: - JokeService
@MainActor
class JokeService: ObservableObject {
    @Published var isLoading: Bool = false
    @Published var loadingSource: LoadingSource?
    @Published var errorMessage: String?
    @Published var cooldownMessage: String?
    @Published var showAbuseWarning: Bool = false
    
    // Loading message provider for dynamic loading states
    let loadingMessageProvider = LoadingMessageProvider()
    
    // MARK: - Advanced Components (Lazy Initialization)
    private lazy var cacheManager = CacheManager()
    private lazy var usageTracker = UsageTracker()
    private lazy var moderationService = ModerationService()
    private let userDefaults = UserDefaults.standard
    
    init() {
        setupBindings()
        // DISABLED cache warming for instant UI responsiveness
        // Cache will be populated naturally when users generate messages
    }
    
    private func setupBindings() {
        // Bind usage tracker cooldown state to UI
        usageTracker.$cooldownTimeRemaining
            .sink { [weak self] timeRemaining in
                if timeRemaining > 0 {
                    self?.cooldownMessage = self?.usageTracker.getFriendlyMessage(userStats: UserStats())
                } else {
                    self?.cooldownMessage = nil
                }
            }
            .store(in: &cancellables)
    }
    
    private var cancellables: Set<AnyCancellable> = []
    
    // MARK: - Main Generate Function with Smart Caching & Abuse Prevention
    func generateJoke(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String?,
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> JokeMessage? {
        
        // Start loading state
        isLoading = true
        errorMessage = nil
        showAbuseWarning = false
        
        defer { 
            isLoading = false
            loadingSource = nil
            loadingMessageProvider.stopLoading()
        }

        // Reset counters if needed
        userStats.resetCountersIfNeeded()

        // Log current daily counts for visibility (especially for premium users)
        debugLog("🧮 Pre-generation counts – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), dailyJokeCount=\(userStats.dailyJokeCount)")
        
        // Check usage limits and cooldowns first
        if !userStats.canGenerateMessage {
            if userStats.isInCooldown {
                cooldownMessage = usageTracker.getFriendlyMessage(userStats: userStats)
                return nil
            } else {
                errorMessage = "Daily limit reached. Upgrade to Premium for unlimited messages!"
                return nil
            }
        }
        
        // Check for abuse warning
        if usageTracker.shouldShowAbuseWarning(userStats: userStats) {
            showAbuseWarning = true
        }
        
        let userTier = userStats.subscriptionTier
        let isPersonalized = personalContext != nil && !personalContext!.isEmpty
        
        // Track the generation attempt
        let canProceed = usageTracker.trackEvent(
            .messageGenerated(spiceLevel: spiceLevel, category: category, isPersonalized: isPersonalized),
            userStats: &userStats
        )
        
        guard canProceed else {
            cooldownMessage = usageTracker.getFriendlyMessage(userStats: userStats)
            return nil
        }
        
        var generatedMessage: JokeMessage?
        
        if isPersonalized {
            // Personalized messages (Premium): Always fresh API calls
            loadingSource = .api
            loadingMessageProvider.startLoading(source: .api)
            
            generatedMessage = await generatePersonalizedMessage(
                spiceLevel: spiceLevel,
                category: category,
                personalContext: personalContext!,
                userTier: userTier,
                userStats: &userStats
            )
        } else {
            // Non-personalized messages: Use smart caching with enhanced loading
            generatedMessage = await generateCachedMessageWithEnhancedLoading(
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier,
                seenIds: seenIds,
                userStats: &userStats
            )
        }
        
        // Record successful generation
        if let generatedMessage = generatedMessage {
            userStats.incrementDailyCount()
            if isPersonalized {
                userStats.recordPersonalizedMessage()
            }
            userStats.ingestGeneratedMessage(generatedMessage)

            // Post-generation log to track running total for the day
            debugLog("📈 After-generation counts – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), dailyJokeCount=\(userStats.dailyJokeCount)")
        }
        
        return generatedMessage
    }
    
    // MARK: - Smart Cached Message Generation
    private func generateCachedMessage(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        userTier: SubscriptionTier,
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> JokeMessage? {
        
        // Try to get message from cache first
        if let cachedMessage = cacheManager.getUnseenMessage(
            spiceLevel: spiceLevel,
            category: category,
            userTier: userTier,
            seenIds: seenIds
        ) {
            // Track cache hit
            _ = usageTracker.trackEvent(.cacheHit(spiceLevel: spiceLevel, category: category), userStats: &userStats)
            debugLog("✅ Cache HIT: Returning cached message")
            return cachedMessage
        }
        
        // Cache miss - generate fresh batch
        _ = usageTracker.trackEvent(.cacheMiss(spiceLevel: spiceLevel, category: category), userStats: &userStats)
        debugLog("❌ Cache MISS: Generating fresh batch")
        
        // Generate batch if needed
        if cacheManager.needsFreshMessages(
            spiceLevel: spiceLevel,
            category: category,
            userTier: userTier,
            seenIds: seenIds
        ) {
            await generateAndCacheBatch(
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier,
                userStats: &userStats
            )
            
            // Try cache again after generation
            if let newMessage = cacheManager.getUnseenMessage(
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier,
                seenIds: seenIds
            ) {
                return newMessage
            }
        }

        // Fallback to sample messages if API fails
        if let error = errorMessage, error.contains("OpenAI API key") {
            return nil
        }
        return generateSampleMessage(spiceLevel: spiceLevel, category: category)
    }
    
    // MARK: - Enhanced Cached Message Generation with Loading States
    private func generateCachedMessageWithEnhancedLoading(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        userTier: SubscriptionTier,
        seenIds: Set<String>,
        userStats: inout UserStats
    ) async -> JokeMessage? {
        
        let startTime = Date()
        
        // Try to get message from cache first
        if let cachedMessage = cacheManager.getUnseenMessage(
            spiceLevel: spiceLevel,
            category: category,
            userTier: userTier,
            seenIds: seenIds
        ) {
            // Cache HIT - Show cache loading for minimum 2 seconds
            loadingSource = .cache
            loadingMessageProvider.startLoading(source: .cache)

            // Track cache hit
            _ = usageTracker.trackEvent(.cacheHit(spiceLevel: spiceLevel, category: category), userStats: &userStats)
            debugLog("✅ Cache HIT: Returning cached message with 2s minimum loading")
            
            // Ensure minimum 2-second loading time
            let elapsedTime = Date().timeIntervalSince(startTime)
            let remainingTime = max(0, 2.0 - elapsedTime)
            if remainingTime > 0 {
                try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
            }
            
            return cachedMessage
        }
        
        // Cache MISS - Use API loading with dynamic messages
        loadingSource = .api
        loadingMessageProvider.startLoading(source: .api)
        
        // Track cache miss
        _ = usageTracker.trackEvent(.cacheMiss(spiceLevel: spiceLevel, category: category), userStats: &userStats)
        debugLog("❌ Cache MISS: Generating fresh batch with dynamic loading messages")

        // Generate batch if needed
        if cacheManager.needsFreshMessages(
            spiceLevel: spiceLevel,
            category: category,
            userTier: userTier,
            seenIds: seenIds
        ) {
            await generateAndCacheBatch(
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier,
                userStats: &userStats
            )

            // Try cache again after generation
            if let newMessage = cacheManager.getUnseenMessage(
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier,
                seenIds: seenIds
            ) {
                return newMessage
            }
        }

        // Fallback to sample messages if API fails
        if let error = errorMessage, error.contains("OpenAI API key") {
            return nil
        }
        return generateSampleMessage(spiceLevel: spiceLevel, category: category)
    }
    
    private func generateAndCacheBatch(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        userTier: SubscriptionTier,
        userStats: inout UserStats
    ) async {
        do {
            debugLog("🔄 Generating batch of 10 messages for \(spiceLevel.rawValue) \(category?.rawValue ?? "Any Category")")

            // Premium model usage policy:
            // Use GPT-5 for premium users until they have generated 50 messages today (cached or fresh).
            // After 50 total messages, switch fresh OpenAI calls to GPT-5 Mini for the rest of the day.
            let shouldUsePremiumModel: Bool = {
                if userTier == .premium {
                    let remainingBeforeSwitch = max(0, 50 - userStats.dailyMessagesGenerated)
                    debugLog("🤖 Model policy check – dailyMessagesGenerated=\(userStats.dailyMessagesGenerated), remaining GPT-5 allowance today=\(remainingBeforeSwitch)")
                    return userStats.dailyMessagesGenerated < 50
                }
                return false
            }()

            let batchMessages = try await callOpenAIAPI(
                spiceLevel: spiceLevel,
                category: category,
                count: 10, // Generate 10 messages per batch
                userTier: userTier,
                userStats: &userStats,
                preferPremiumModel: shouldUsePremiumModel
            )
            
            // Cache the batch
            cacheManager.cacheBatch(
                messages: batchMessages,
                spiceLevel: spiceLevel,
                category: category,
                userTier: userTier
            )
            
            debugLog("✅ Successfully cached \(batchMessages.count) messages")
            
        } catch {
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                errorMessage = "Missing OpenAI API key. Add it to OpenAI-Config.plist so we can fetch fresh flirts."
            } else {
                errorMessage = "Failed to generate new flirts. Please try again."
            }
            debugLog("❌ Batch generation failed: \(error)")
        }
    }
    
    // MARK: - Personalized Message Generation
    private func generatePersonalizedMessage(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        userTier: SubscriptionTier,
        userStats: inout UserStats
    ) async -> JokeMessage? {
        do {
            debugLog("🎯 Generating personalized message with context: \(personalContext.prefix(30))...")
            
            let usePremiumBoost = userTier == .premium && userStats.shouldUsePremiumPersonalTouchBoost()
            let messages = try await callPersonalizedOpenAIAPI(
                spiceLevel: spiceLevel,
                category: category,
                personalContext: personalContext,
                userTier: userTier,
                userStats: &userStats,
                usePremiumBoost: usePremiumBoost
            )

            if let message = messages.first {
                debugLog("✅ Generated personalized message: \(message.text.prefix(50))...")
                if usePremiumBoost {
                    userStats.consumePremiumPersonalTouchBoost()
                }
                return message
            } else {
                debugLog("⚠️ No personalized message generated, using fallback")
                return generateSampleMessage(spiceLevel: spiceLevel, category: category)
            }
            
        } catch {
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                errorMessage = "Add your OpenAI API key to personalize flirts."
                debugLog("❌ Personalized API Error: missing API key")
                return nil
            } else {
                errorMessage = "Failed to generate personalized flirt. Please try again."
                debugLog("❌ Personalized API Error: \(error)")
                return generateSampleMessage(spiceLevel: spiceLevel, category: category)
            }
        }
    }
    
    // MARK: - OpenAI API Integration
    private func callOpenAIAPI(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        count: Int = 10,
        userTier: SubscriptionTier,
        userStats: inout UserStats,
        preferPremiumModel: Bool
    ) async throws -> [JokeMessage] {
        let apiKey = OpenAIConfig.apiKey
        
        debugLog("JokeService: Attempting OpenAI API call - SpiceLevel: \(spiceLevel), Category: \(category?.rawValue ?? "none"), Count: \(count)")
        
        guard !apiKey.isEmpty else {
            errorMessage = "Add your OpenAI API key in OpenAI-Config.plist (or via secure storage) to generate new flirts."
            debugLog("FlirtService: No OpenAI API key configured")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        guard OpenAIConfig.validateAPIKey(apiKey) else {
            errorMessage = "The OpenAI API key format looks invalid. Please double-check it."
            debugLog("FlirtService: Invalid API key format")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        do {
            let openAIService = OpenAIService(apiKey: apiKey)
            let avoidanceList = userStats.recentExclusionList(maxCount: 30)
            let generationResult = try await openAIService.generateJokeMessages(
                spiceLevel: spiceLevel,
                category: category,
                count: count,
                userTier: userTier,
                useOptimalModel: preferPremiumModel,
                avoidPhrases: avoidanceList
            )
            let messageTexts = generationResult.messages
            
            // Track API usage
            let modelUsed = generationResult.model
            _ = usageTracker.trackEvent(
                .apiCall(
                    model: modelUsed,
                    tokenCount: generationResult.usage?.totalTokens ?? count * 50
                ),
                userStats: &userStats
            )
            userStats.recordModelUsage(model: modelUsed)
            if let usage = generationResult.usage {
                let prompt = usage.promptTokens
                let completion = usage.completionTokens
                let total = usage.totalTokens
                debugLog("FlirtService: API usage – prompt: \(prompt), completion: \(completion), total: \(total)")
            }
            if let estimatedCost = generationResult.estimatedCost {
                debugLog("FlirtService: Estimated cost for batch: $\(String(format: "%.6f", estimatedCost))")
            }
            
            debugLog("FlirtService: OpenAI API call successful, received \(messageTexts.count) messages")
            
            // Validate we got meaningful messages
            guard !messageTexts.isEmpty else {
                debugLog("FlirtService: OpenAI returned empty messages, using sample fallback")
                return generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
            }
            
            let moderatedResults = try await moderationService.moderate(messages: messageTexts)
            let safeTexts = moderatedResults.filter { !$0.isFlagged }.map { $0.text }

            if safeTexts.isEmpty {
                errorMessage = "We couldn't generate a safe flirt right now. Please try again with different settings."
                debugLog("FlirtService: Moderation flagged all generated messages")
                return generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
            }

            if safeTexts.count < messageTexts.count {
                debugLog("FlirtService: Moderation filtered \(messageTexts.count - safeTexts.count) message(s)")
            }

            let (freshTexts, familiarTexts) = segregateFreshAndFamiliarTexts(safeTexts, userStats: userStats)
            var finalTexts = freshTexts
            if finalTexts.isEmpty {
                debugLog("FlirtService: All moderated messages already appeared in history; reusing unique familiar set")
                finalTexts = familiarTexts
            }

            // Convert strings to JokeMessage objects (non-personalized)
            let jokeMessages = finalTexts.map { text in
                JokeMessage(text: text, spiceLevel: spiceLevel, category: category, hasPersonalTouch: false)
            }

            debugLog("JokeService: Successfully created \(jokeMessages.count) moderated JokeMessage objects")
            return jokeMessages
            
        } catch {
            debugLog("FlirtService: OpenAI API call failed with error: \(error)")
            
            // Log specific error types for better debugging
            if let openAIError = error as? OpenAIError {
                switch openAIError {
                case .missingAPIKey:
                    debugLog("FlirtService: Missing API key - cannot call OpenAI")
                case .invalidAPIKey:
                    debugLog("FlirtService: API key is invalid - check configuration")
                case .apiErrorWithDetails(let code, let details):
                    debugLog("FlirtService: API error \(code) - \(details)")
                case .apiError(let code):
                    debugLog("FlirtService: API error code \(code)")
                default:
                    debugLog("FlirtService: Other OpenAI error - \(openAIError)")
                }
            } else if let moderationError = error as? ModerationError {
                debugLog("FlirtService: Moderation error - \(moderationError.localizedDescription)")
                errorMessage = "We couldn't verify that message was safe. Please try again."
            }
            
            // Fallback to sample messages on errors other than missing key
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                throw openAIError
            } else {
                debugLog("FlirtService: Using sample messages as fallback")
                return generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
            }
        }
    }
    
    // MARK: - Personalized OpenAI API Integration
    private func callPersonalizedOpenAIAPI(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        userTier: SubscriptionTier,
        userStats: inout UserStats,
        count: Int = 1,
        usePremiumBoost: Bool
    ) async throws -> [JokeMessage] {
        let apiKey = OpenAIConfig.apiKey
        
        debugLog("JokeService: Attempting personalized OpenAI API call - SpiceLevel: \(spiceLevel), Category: \(category?.rawValue ?? "none"), Context: '\(personalContext)'")
        
        guard !apiKey.isEmpty else {
            errorMessage = "Add your OpenAI API key in OpenAI-Config.plist to personalize flirts."
            debugLog("FlirtService: No OpenAI API key configured for personalized request")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        guard OpenAIConfig.validateAPIKey(apiKey) else {
            errorMessage = "The OpenAI API key format looks invalid. Please double-check it."
            debugLog("FlirtService: Invalid API key format for personalized request")
            cacheManager.clearCache()
            throw OpenAIError.missingAPIKey
        }
        
        do {
            let openAIService = OpenAIService(apiKey: apiKey)
            let avoidanceList = userStats.recentExclusionList(maxCount: 30)
            let generationResult = try await openAIService.generatePersonalizedJokeMessages(
                spiceLevel: spiceLevel,
                category: category,
                personalContext: personalContext,
                count: count,
                userTier: userTier,
                useOptimalModel: usePremiumBoost,
                avoidPhrases: avoidanceList
            )
            let messageTexts = generationResult.messages
            
            // Track API usage with higher token estimate for personalized
            let modelUsed = generationResult.model
            _ = usageTracker.trackEvent(
                .apiCall(
                    model: modelUsed,
                    tokenCount: generationResult.usage?.totalTokens ?? count * 100
                ),
                userStats: &userStats
            )
            userStats.recordModelUsage(model: modelUsed)
            if let usage = generationResult.usage {
                debugLog("FlirtService: Personalized API usage – prompt: \(usage.promptTokens), completion: \(usage.completionTokens), total: \(usage.totalTokens)")
            }
            if let estimatedCost = generationResult.estimatedCost {
                debugLog("FlirtService: Personalized estimated cost: $\(String(format: "%.6f", estimatedCost))")
            }
            
            debugLog("FlirtService: Personalized OpenAI API call successful, received \(messageTexts.count) messages")
            
            // Validate we got meaningful messages
            guard !messageTexts.isEmpty else {
                debugLog("FlirtService: Personalized OpenAI returned empty messages, using sample fallback")
                let sampleMessages = generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
                return sampleMessages
            }
            
            let moderatedResults = try await moderationService.moderate(messages: messageTexts)
            let safeTexts = moderatedResults.filter { !$0.isFlagged }.map { $0.text }

            if safeTexts.isEmpty {
                errorMessage = "That personal touch produced something we can't share. Try different details."
                debugLog("FlirtService: Personalized moderation flagged all messages")
                return generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
            }

            if safeTexts.count < messageTexts.count {
                debugLog("FlirtService: Personalized moderation filtered \(messageTexts.count - safeTexts.count) message(s)")
            }

            let (freshTexts, familiarTexts) = segregateFreshAndFamiliarTexts(safeTexts, userStats: userStats)
            var finalTexts = freshTexts
            if finalTexts.isEmpty {
                debugLog("FlirtService: Personalized history filtered all moderated messages; reusing unique familiar set")
                finalTexts = familiarTexts
            }

            // Convert strings to JokeMessage objects (personalized)
            let jokeMessages = finalTexts.map { text in
                JokeMessage(text: text, spiceLevel: spiceLevel, category: category, hasPersonalTouch: true)
            }

            debugLog("JokeService: Successfully created \(jokeMessages.count) moderated personalized JokeMessage objects")
            return jokeMessages
            
        } catch {
            debugLog("FlirtService: Personalized OpenAI API call failed with error: \(error)")
            
            // Log specific error types for better debugging
            if let openAIError = error as? OpenAIError {
                switch openAIError {
                case .missingAPIKey:
                    debugLog("FlirtService: Missing API key for personalized request")
                case .invalidAPIKey:
                    debugLog("FlirtService: API key is invalid for personalized request - check configuration")
                case .apiErrorWithDetails(let code, let details):
                    debugLog("FlirtService: Personalized API error \(code) - \(details)")
                case .apiError(let code):
                    debugLog("FlirtService: Personalized API error code \(code)")
                default:
                    debugLog("FlirtService: Other personalized OpenAI error - \(openAIError)")
                }
            } else if let moderationError = error as? ModerationError {
                debugLog("FlirtService: Personalized moderation error - \(moderationError.localizedDescription)")
                errorMessage = "We couldn't verify that message was safe. Please try again."
            }
            
            if let openAIError = error as? OpenAIError, case .missingAPIKey = openAIError {
                throw openAIError
            } else {
                debugLog("FlirtService: Using sample messages as fallback for personalized request")
                return generateSampleMessages(spiceLevel: spiceLevel, category: category, count: count)
            }
        }
    }
    
    // MARK: - Sample Message Generation & Fallback
    private func generateSampleMessage(spiceLevel: SpiceLevel, category: JokeCategory?) -> JokeMessage {
        let messages = getSampleMessagesArray(spiceLevel: spiceLevel, category: category)
        let text = messages.randomElement() ?? "You're absolutely amazing! ✨"
        return JokeMessage(text: text, spiceLevel: spiceLevel, category: category, hasPersonalTouch: false)
    }
    
    private func generateSampleMessages(spiceLevel: SpiceLevel, category: JokeCategory?, count: Int) -> [JokeMessage] {
        let messages = getSampleMessagesArray(spiceLevel: spiceLevel, category: category)
        return messages.prefix(count).map { text in
            JokeMessage(text: text, spiceLevel: spiceLevel, category: category, hasPersonalTouch: false)
        }
    }
    
    private func getSampleMessagesArray(spiceLevel: SpiceLevel, category: JokeCategory?) -> [String] {
            switch (spiceLevel, category) {
            case (.mild, .dadJokes), (.mild, nil):
                return [
                    "Why don't scientists trust atoms? Because they make up everything! ⚛️",
                    "I told my wife she was drawing her eyebrows too high. She looked surprised. 😮",
                    "Why don't eggs tell jokes? They'd crack each other up! 🥚",
                    "What do you call a fake noodle? An impasta! 🍝",
                    "Why did the scarecrow win an award? He was outstanding in his field! 🌾"
                ]
            case (.mild, .puns):
                return [
                    "I used to hate facial hair, but then it grew on me. 🧔",
                    "The math teacher called in sick with algebra. 📐",
                    "I'm reading a book about anti-gravity. It's impossible to put down! 📚",
                    "Time flies like an arrow. Fruit flies like a banana. 🍌",
                    "I wondered why the baseball kept getting bigger. Then it hit me! ⚾"
                ]
            case (.mild, .pickupLines):
                return [
                    "Are you made of copper and tellurium? Because you're Cu-Te! 🧪",
                    "Do you like Star Wars? Because Yoda one for me! ⭐",
                    "Are you a banana? Because I find you a-peel-ing! 🍌",
                    "Do you work at Starbucks? Because I like you a latte! ☕",
                    "Are you a parking ticket? Because you've got FINE written all over you! 🎫"
                ]
            case (.medium, .dadJokes), (.medium, nil):
                return [
                    "What's the best thing about Switzerland? I don't know, but the flag is a big plus! 🇨🇭",
                    "Why don't scientists trust atoms? Because they make up everything! ⚛️",
                    "I invented a new word: Plagiarism! 📝",
                    "Why did the coffee file a police report? It got mugged! ☕",
                    "What do you call a bear with no teeth? A gummy bear! 🐻"
                ]
            case (.medium, .geekJokes):
                return [
                    "There are only 10 types of people in the world: those who understand binary and those who don't. 💻",
                    "Why do programmers prefer dark mode? Because light attracts bugs! 🐛",
                    "I told my computer a joke about UDP, but it didn't get it. 📡",
                    "How many programmers does it take to change a light bulb? None, that's a hardware problem! 💡",
                    "Why did the developer go broke? Because he used up all his cache! 💰"
                ]
            case (.medium, .workJokes):
                return [
                    "I told my boss three companies were after me and I needed a raise. He asked which ones. I said gas, electric, and water! 💡",
                    "My job is secure. No one else wants it! 😅",
                    "I'm not saying I hate my job, but if I won the lottery, I'd still come in... to quit! 🎰",
                    "Why don't they play poker in the office? Too many cheetahs! 🐆",
                    "I love deadlines. I like the whooshing sound they make as they fly by! ⏰"
                ]
            case (.savage, .darkHumor), (.savage, nil):
                return [
                    "I told my wife she was drawing her eyebrows too high. She looked surprised. 😮",
                    "My therapist says I have a preoccupation with vengeance. We'll see about that. 😈",
                    "I haven't spoken to my wife in years. I didn't want to interrupt her. 🤐",
                    "They say money talks. Mine always says goodbye. 💸",
                    "I'm reading a book about anti-gravity. It's about time! ⏰"
                ]
            case (.savage, .geekJokes):
                return [
                    "There are only 10 types of people: those who understand binary and those who don't. 💻",
                    "Why do Java developers wear glasses? Because they can't C#! 👓",
                    "A SQL query goes into a bar, walks up to two tables and asks: 'Can I join you?' 🍺",
                    "How do you comfort a JavaScript bug? You console it! 🐛",
                    "Why did the programmer quit his job? He didn't get arrays! 📊"
                ]
            case (.savage, .workJokes):
                return [
                    "I told my boss I needed a raise because three companies were after me. He asked which ones. I said gas, electric, and water! 💡",
                    "My job is so secret, I don't even know what I'm doing! 🤫",
                    "I love deadlines. I like the whooshing sound they make as they fly by! ⏰",
                    "The early bird might get the worm, but the second mouse gets the cheese! 🐭",
                    "I'm not lazy, I'm on energy saving mode! 🔋"
                ]
            // Missing cases for complete coverage
            case (.mild, .darkHumor):
                return [
                    "I told my wife she was drawing her eyebrows too high. She looked surprised. 😮",
                    "My therapist says I have a preoccupation with vengeance. We'll see about that. 😈",
                    "I haven't spoken to my wife in years. I didn't want to interrupt her. 🤐",
                    "They say money talks. Mine always says goodbye. 💸",
                    "I'm reading a book about anti-gravity. It's about time! ⏰"
                ]
            case (.mild, .geekJokes):
                return [
                    "There are only 10 types of people in the world: those who understand binary and those who don't. 💻",
                    "Why do programmers prefer dark mode? Because light attracts bugs! 🐛",
                    "I told my computer a joke about UDP, but it didn't get it. 📡",
                    "How many programmers does it take to change a light bulb? None, that's a hardware problem! 💡",
                    "Why did the developer go broke? Because he used up all his cache! 💰"
                ]
            case (.mild, .workJokes):
                return [
                    "I told my boss three companies were after me and I needed a raise. He asked which ones. I said gas, electric, and water! 💡",
                    "My job is secure. No one else wants it! 😅",
                    "I'm not saying I hate my job, but if I won the lottery, I'd still come in... to quit! 🎰",
                    "Why don't they play poker in the office? Too many cheetahs! 🐆",
                    "I love deadlines. I like the whooshing sound they make as they fly by! ⏰"
                ]
            case (.medium, .darkHumor):
                return [
                    "I told my wife she was drawing her eyebrows too high. She looked surprised. 😮",
                    "My therapist says I have a preoccupation with vengeance. We'll see about that. 😈",
                    "I haven't spoken to my wife in years. I didn't want to interrupt her. 🤐",
                    "They say money talks. Mine always says goodbye. 💸",
                    "I'm reading a book about anti-gravity. It's about time! ⏰"
                ]
            case (.medium, .puns):
                return [
                    "I used to hate facial hair, but then it grew on me. 🧔",
                    "The math teacher called in sick with algebra. 📐",
                    "I'm reading a book about anti-gravity. It's impossible to put down! 📚",
                    "Time flies like an arrow. Fruit flies like a banana. 🍌",
                    "I wondered why the baseball kept getting bigger. Then it hit me! ⚾"
                ]
            case (.medium, .pickupLines):
                return [
                    "Are you made of copper and tellurium? Because you're Cu-Te! 🧪",
                    "Do you like Star Wars? Because Yoda one for me! ⭐",
                    "Are you a banana? Because I find you a-peel-ing! 🍌",
                    "Do you work at Starbucks? Because I like you a latte! ☕",
                    "Are you a parking ticket? Because you've got FINE written all over you! 🎫"
                ]
            case (.savage, .dadJokes):
                return [
                    "What's the best thing about Switzerland? I don't know, but the flag is a big plus! 🇨🇭",
                    "Why don't scientists trust atoms? Because they make up everything! ⚛️",
                    "I invented a new word: Plagiarism! 📝",
                    "Why did the coffee file a police report? It got mugged! ☕",
                    "What do you call a bear with no teeth? A gummy bear! 🐻"
                ]
            case (.savage, .puns):
                return [
                    "I used to hate facial hair, but then it grew on me. 🧔",
                    "The math teacher called in sick with algebra. 📐",
                    "I'm reading a book about anti-gravity. It's impossible to put down! 📚",
                    "Time flies like an arrow. Fruit flies like a banana. 🍌",
                    "I wondered why the baseball kept getting bigger. Then it hit me! ⚾"
                ]
            case (.savage, .pickupLines):
                return [
                    "Are you made of copper and tellurium? Because you're Cu-Te! 🧪",
                    "Do you like Star Wars? Because Yoda one for me! ⭐",
                    "Are you a banana? Because I find you a-peel-ing! 🍌",
                    "Do you work at Starbucks? Because I like you a latte! ☕",
                    "Are you a parking ticket? Because you've got FINE written all over you! 🎫"
                ]
            }
    }
    
    // MARK: - Cache Warming & Analytics
    private func warmUpCacheIfNeeded() {
        Task {
            // Warm up cache for free tier initially
            cacheManager.warmUpCache(for: .free) { [weak self] keysToWarmUp in
                Task { @MainActor in
                    await self?.warmUpCacheForKeys(keysToWarmUp)
                }
            }
        }
    }
    
    private func warmUpCacheForKeys(_ keys: [CacheKey]) async {
        debugLog("🔥 Warming up cache for \(keys.count) key combinations")
        
        var dummyStats = UserStats() // Dummy stats for warming
        
        for key in keys.prefix(3) { // Limit to 3 keys to avoid excessive API calls
            await generateAndCacheBatch(
                spiceLevel: key.spiceLevel,
                category: key.category,
                userTier: key.userTier,
                userStats: &dummyStats
            )
            
            // Small delay between batch generations
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        }
        
        debugLog("🎯 Cache warm-up completed")
    }
    
    // MARK: - Analytics & Debug
    func getCachePerformanceInfo() -> String {
        return cacheManager.getCacheInfo()
    }
    
    func getUsageAnalytics() -> [String: Any] {
        return usageTracker.getAnalyticsData()
    }
    
    func estimatedCostSavings() -> Double {
        return cacheManager.estimatedCostSavings()
    }
    
    // MARK: - Public Cache Management
    func clearAllCaches() {
        cacheManager.clearCache()
        debugLog("🗑️ All caches cleared")
    }
    
    func preloadCacheForUser(userTier: SubscriptionTier) {
        Task {
            cacheManager.warmUpCache(for: userTier) { [weak self] keysToWarmUp in
                Task { @MainActor in
                    await self?.warmUpCacheForKeys(keysToWarmUp)
                }
            }
        }
    }

    // MARK: - Helper Functions
    private func segregateFreshAndFamiliarTexts(_ texts: [String], userStats: UserStats) -> ([String], [String]) {
        var fresh: [String] = []
        var familiar: [String] = []
        var seenFingerprints: Set<String> = []

        for text in texts {
            let fingerprint = normalizeMessage(text)
            guard !fingerprint.isEmpty else { continue }
            if !seenFingerprints.insert(fingerprint).inserted {
                continue
            }

            if userStats.hasSeenMessageText(text) {
                familiar.append(text)
            } else {
                fresh.append(text)
            }
        }

        return (fresh, familiar)
    }

    private func normalizeMessage(_ text: String) -> String {
        text
            .folding(options: [.diacriticInsensitive, .caseInsensitive], locale: .current)
            .components(separatedBy: CharacterSet.alphanumerics.inverted)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
    }
}
