//
//  OpenAIService.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation

struct OpenAIService {
    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1/chat/completions"

    init(apiKey: String) {
        self.apiKey = apiKey
    }
    
    struct OpenAIRequest: Codable {
        let model: String
        let messages: [Message]
        let maxCompletionTokens: Int
        let temperature: Double
        let n: Int
        
        enum CodingKeys: String, CodingKey {
            case model, messages, temperature, n
            case maxCompletionTokens = "max_completion_tokens"
        }
    }
    
    struct Message: Codable {
        let role: String
        let content: String
    }
    
    struct OpenAIResponse: Codable {
        let choices: [Choice]
        let usage: Usage?
    }
    
    struct Choice: Codable {
        let message: Message
        let finishReason: String?
        
        enum CodingKeys: String, CodingKey {
            case message
            case finishReason = "finish_reason"
        }
    }
    
    struct Usage: Codable {
        let promptTokens: Int
        let completionTokens: Int
        let totalTokens: Int
        
        enum CodingKeys: String, CodingKey {
            case promptTokens = "prompt_tokens"
            case completionTokens = "completion_tokens"  
            case totalTokens = "total_tokens"
        }
    }
    
    struct ChatGenerationResult {
        let messages: [String]
        let usage: Usage?
        let estimatedCost: Double?
        let model: String
    }

    func generateJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        count: Int = 10,
        userTier: SubscriptionTier = .free,
        useOptimalModel: Bool = false,
        avoidPhrases: [String] = []
    ) async throws -> ChatGenerationResult {
        // Validate parameters
        try validateParameters(count: count, temperature: 1.0, maxTokens: 1000)
        
        let model = selectOptimalModel(userTier: userTier, useOptimalModel: useOptimalModel)
        let systemPrompt = createSystemPrompt(forBatchGeneration: count > 5, avoidancePhrases: avoidPhrases)
        let userPrompt = createUserPrompt(spiceLevel: spiceLevel, category: category, count: count, avoidancePhrases: avoidPhrases)
        
        // Log the prompts for debugging
        debugLog("OpenAI System Prompt: \(systemPrompt)")
        debugLog("OpenAI User Prompt: \(userPrompt)")
        debugLog("OpenAI Selected Model: \(model)")
        
        let maxTokens = getOptimalTokenLimit(model: model, count: count)
        debugLog("OpenAI Max Completion Tokens Set: \(maxTokens)")
        
        let request = OpenAIRequest(
            model: model,
            messages: [
                Message(role: "system", content: systemPrompt),
                Message(role: "user", content: userPrompt)
            ],
            maxCompletionTokens: maxTokens,
            temperature: getOptimalTemperature(model: model),
            n: 1
        )
        
        var urlRequest = URLRequest(url: URL(string: baseURL)!)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONEncoder().encode(request)
        urlRequest.httpBody = jsonData
        
        // Log the request for debugging
        if let requestBody = String(data: jsonData, encoding: .utf8) {
            debugLog("OpenAI Request Body: \(requestBody)")
        }
        
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            debugLog("OpenAI Error: Invalid response type")
            throw OpenAIError.invalidResponse
        }

        // Log response for debugging
        debugLog("OpenAI Response Status: \(httpResponse.statusCode)")
        if let responseBody = String(data: data, encoding: .utf8) {
            debugLog("OpenAI Response Body: \(responseBody)")
        }

        if httpResponse.statusCode == 401 {
            debugLog("OpenAI Error: Invalid API key (401)")
            throw OpenAIError.invalidAPIKey
        }

        guard httpResponse.statusCode == 200 else {
            // Try to parse error details from response body
            if let errorBody = String(data: data, encoding: .utf8) {
                debugLog("OpenAI API Error \(httpResponse.statusCode): \(errorBody)")
                throw OpenAIError.apiErrorWithDetails(httpResponse.statusCode, errorBody)
            } else {
                debugLog("OpenAI API Error \(httpResponse.statusCode): No error details available")
                throw OpenAIError.apiError(httpResponse.statusCode)
            }
        }

        let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)
        
        // Log token usage for cost tracking
        if let usage = openAIResponse.usage {
            debugLog("OpenAI Usage - Prompt: \(usage.promptTokens), Completion: \(usage.completionTokens), Total: \(usage.totalTokens)")
            debugLog("OpenAI Model: \(model)")
            
            // Check if GPT-5 used all tokens for reasoning
            if model.contains("gpt-5") && !model.contains("mini") && usage.completionTokens >= 5800 {
                debugLog("OpenAI WARNING: GPT-5 may have used all tokens for reasoning (completion: \(usage.completionTokens))")
            }

            // Calculate approximate cost
            let estimatedCost = calculateCost(model: model, usage: usage)
            debugLog("OpenAI Estimated Cost: $\(String(format: "%.6f", estimatedCost))")
        }
        
        guard let choice = openAIResponse.choices.first else {
            debugLog("OpenAI Error: No choices in response")
            throw OpenAIError.noResponse
        }
        
        debugLog("OpenAI Choice Message Content Length: \(choice.message.content.count)")
        debugLog("OpenAI Choice Finish Reason: \(choice.finishReason ?? "nil")")
        
        let messages = parseJokeMessages(from: choice.message.content)
        let estimatedCost = openAIResponse.usage.flatMap { usage in
            calculateCost(model: model, usage: usage)
        }
        
        return ChatGenerationResult(messages: messages, usage: openAIResponse.usage, estimatedCost: estimatedCost, model: model)
    }
    
    func generatePersonalizedJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        count: Int = 1,
        userTier: SubscriptionTier = .premium,
        useOptimalModel: Bool = true,
        avoidPhrases: [String] = []
    ) async throws -> ChatGenerationResult {
        // Validate parameters
        try validateParameters(count: count, temperature: 1.0, maxTokens: 1000)
        
        let model = selectOptimalModel(userTier: userTier, useOptimalModel: useOptimalModel)
        let systemPrompt = createPersonalizedSystemPrompt(avoidancePhrases: avoidPhrases)
        let userPrompt = createPersonalizedUserPrompt(
            spiceLevel: spiceLevel,
            category: category,
            personalContext: personalContext,
            count: count,
            avoidancePhrases: avoidPhrases
        )
        
        // Log the prompts for debugging
        debugLog("OpenAI Personalized System Prompt: \(systemPrompt)")
        debugLog("OpenAI Personalized User Prompt: \(userPrompt)")
        debugLog("OpenAI Personalized Model: \(model)")
        
        let maxTokens = getOptimalTokenLimit(model: model, count: count)
        debugLog("OpenAI Personalized Max Completion Tokens Set: \(maxTokens)")
        
        let request = OpenAIRequest(
            model: model,
            messages: [
                Message(role: "system", content: systemPrompt),
                Message(role: "user", content: userPrompt)
            ],
            maxCompletionTokens: maxTokens,
            temperature: getOptimalTemperature(model: model),
            n: 1
        )
        
        var urlRequest = URLRequest(url: URL(string: baseURL)!)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONEncoder().encode(request)
        urlRequest.httpBody = jsonData
        
        // Log the request for debugging
        if let requestBody = String(data: jsonData, encoding: .utf8) {
            debugLog("OpenAI Request Body: \(requestBody)")
        }
        
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            debugLog("OpenAI Error: Invalid response type")
            throw OpenAIError.invalidResponse
        }

        // Log response for debugging
        debugLog("OpenAI Response Status: \(httpResponse.statusCode)")
        if let responseBody = String(data: data, encoding: .utf8) {
            debugLog("OpenAI Response Body: \(responseBody)")
        }

        if httpResponse.statusCode == 401 {
            debugLog("OpenAI Error: Invalid API key (401)")
            throw OpenAIError.invalidAPIKey
        }
        
        guard httpResponse.statusCode == 200 else {
            // Try to parse error details from response body
            if let errorBody = String(data: data, encoding: .utf8) {
                debugLog("OpenAI API Error \(httpResponse.statusCode): \(errorBody)")
                throw OpenAIError.apiErrorWithDetails(httpResponse.statusCode, errorBody)
            } else {
                debugLog("OpenAI API Error \(httpResponse.statusCode): No error details available")
                throw OpenAIError.apiError(httpResponse.statusCode)
            }
        }
        
        let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)
        
        // Log token usage for cost tracking
        if let usage = openAIResponse.usage {
            debugLog("OpenAI Personalized Usage - Prompt: \(usage.promptTokens), Completion: \(usage.completionTokens), Total: \(usage.totalTokens)")
            debugLog("OpenAI Personalized Model: \(model)")
            
            // Check if GPT-5 used all tokens for reasoning
            if model.contains("gpt-5") && !model.contains("mini") && usage.completionTokens >= 5800 {
                debugLog("OpenAI Personalized WARNING: GPT-5 may have used all tokens for reasoning (completion: \(usage.completionTokens))")
            }

            // Calculate approximate cost
            let estimatedCost = calculateCost(model: model, usage: usage)
            debugLog("OpenAI Personalized Estimated Cost: $\(String(format: "%.6f", estimatedCost))")
        }
        
        guard let choice = openAIResponse.choices.first else {
            debugLog("OpenAI Personalized Error: No choices in response")
            throw OpenAIError.noResponse
        }
        
        debugLog("OpenAI Personalized Choice Message Content Length: \(choice.message.content.count)")
        debugLog("OpenAI Personalized Choice Finish Reason: \(choice.finishReason ?? "nil")")
        
        let messages = parseJokeMessages(from: choice.message.content)
        let estimatedCost = openAIResponse.usage.flatMap { usage in
            calculateCost(model: model, usage: usage)
        }
        
        return ChatGenerationResult(messages: messages, usage: openAIResponse.usage, estimatedCost: estimatedCost, model: model)
    }
    
    // MARK: - Parameter Validation
    private func validateParameters(count: Int, temperature: Double, maxTokens: Int) throws {
        guard count > 0 && count <= 10 else {
            throw OpenAIError.invalidParameter("Count must be between 1 and 10, got \(count)")
        }
        
        guard temperature >= 0.0 && temperature <= 2.0 else {
            throw OpenAIError.invalidParameter("Temperature must be between 0.0 and 2.0, got \(temperature)")
        }
        
        guard maxTokens > 0 && maxTokens <= 4096 else {
            throw OpenAIError.invalidParameter("Max tokens must be between 1 and 4096, got \(maxTokens)")
        }
        
        debugLog("OpenAI Parameters validated - Count: \(count), Temperature: \(temperature), MaxTokens: \(maxTokens)")
    }
    
    // MARK: - Model Selection & Optimization
    private func selectOptimalModel(userTier: SubscriptionTier, useOptimalModel: Bool) -> String {
        if userTier == .premium && useOptimalModel {
            return "gpt-5-2025-08-07"
        }
        return "gpt-5-mini-2025-08-07"
    }
    
    private func getOptimalTokenLimit(model: String, count: Int) -> Int {
        // GPT-5 needs significantly more tokens due to reasoning overhead
        if model.contains("gpt-5-mini") {
            let baseTokens = 1000
            let batchMultiplier = max(1, count / 5)
            return min(baseTokens * batchMultiplier, 2000)
        } else {
            // GPT-5 uses many tokens for reasoning, needs much higher limits
            let baseTokens = 2000  // Double base tokens for GPT-5
            let batchMultiplier = max(1, count / 3)  // More aggressive scaling
            let calculatedTokens = baseTokens * batchMultiplier
            debugLog("OpenAI Token Calculation: base=\(baseTokens), multiplier=\(batchMultiplier), total=\(calculatedTokens)")
            return min(calculatedTokens, 6000)  // Increase max to 6000 for GPT-5
        }
    }
    
    private func getOptimalTemperature(model: String) -> Double {
        debugLog("OpenAI Model: \(model)")
        // GPT-5 Mini only supports temperature 1.0, so use that for all models for now
        debugLog("OpenAI Temperature: Using 1.0 (required for GPT-5 Mini)")
        return 1.0
    }
    
    private func createSystemPrompt(forBatchGeneration: Bool = false, avoidancePhrases: [String]) -> String {
        let batchInstructions = forBatchGeneration ? """

        BATCH DIVERSITY RULES:
        - Deliver exactly the requested number of jokes.
        - Each joke must have a completely different structure and approach.
        - Vary joke types: one-liners, setup-punchline, observational, wordplay, etc.
        - Use different comedic techniques: irony, exaggeration, misdirection, timing.
        - Ensure no two jokes feel similar in style, topic, or delivery.
        """ : ""

        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))

        let avoidanceSection: String
        if limitedPhrases.isEmpty {
            avoidanceSection = ""
        } else {
            avoidanceSection = """

        EXCLUSION LIST: Avoid repeating or paraphrasing any of the \(limitedPhrases.count) previously used jokes.
        """
        }

        return """
        You are an expert comedian and joke writer. Create original, high-quality jokes that are genuinely funny and memorable.

        CORE REQUIREMENTS:
        - Output ONLY the jokes (no numbering, bullet points, or explanations).
        - Each joke should be 1-2 sentences, maximum 150 characters.
        - Every joke must be completely original and fresh—no recycled or overused material.
        - Use diverse comedic structures: setup-punchline, one-liners, observational, wordplay.
        - Ensure perfect timing and rhythm for maximum comedic impact.
        - Never repeat themes, setups, or punchlines from previous outputs.
        \(batchInstructions)\(avoidanceSection)

        SPICE LEVEL GUIDELINES:
        • MILD: Clean, family-friendly humor. Wholesome wordplay, innocent observations, light situational comedy.
        • MEDIUM: Clever, witty humor with sharper edge. Smart wordplay, ironic observations, mild sarcasm.
        • SAVAGE: Dark, edgy humor with bite. Brutal honesty, dark irony, cutting observations (but tasteful).

        CATEGORY EXPERTISE:
        • DAD JOKES: Classic groan-worthy puns, wholesome wordplay, innocent double meanings.
        • DARK HUMOR: Morbid comedy, existential dread, gallows humor with clever twists.
        • PUNS: Sophisticated wordplay, double meanings, linguistic cleverness.
        • PICK-UP LINES: Cheesy romantic humor, flirty wordplay, charming one-liners.
        • GEEK JOKES: Tech humor, science puns, gaming references, nerd culture comedy.
        • WORK JOKES: Office life satire, meeting humor, workplace observations, corporate comedy.

        Create jokes that people will remember, share, and genuinely laugh at.
        """
    }
    
    private func createUserPrompt(spiceLevel: SpiceLevel, category: JokeCategory?, count: Int, avoidancePhrases: [String]) -> String {
        let spiceDescription = spiceLevel.rawValue.uppercased()
        let categoryDescription = category?.rawValue ?? "mixed categories"
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceReminder: String
        if limitedPhrases.isEmpty {
            avoidanceReminder = ""
        } else {
            avoidanceReminder = "\n\nORIGINALITY REQUIREMENT: Ensure complete originality - avoid any similarity to the \(limitedPhrases.count) previous jokes."
        }

        // Create specific guidance based on category
        let categoryGuidance = createCategorySpecificGuidance(category: category, spiceLevel: spiceLevel)

        return """
        Generate \(count) original \(spiceDescription) spice level jokes for: \(categoryDescription)

        \(categoryGuidance)

        REQUIREMENTS:
        - Each joke must be completely unique and original
        - Use different comedic structures and approaches
        - Ensure perfect timing and rhythm for maximum impact
        - Keep each joke under 150 characters
        - Make them genuinely funny and shareable\(avoidanceReminder)

        Return only the jokes, one per line, no formatting.
        """
    }

    private func createCategorySpecificGuidance(category: JokeCategory?, spiceLevel: SpiceLevel) -> String {
        guard let category = category else {
            return "Mix different joke styles and topics for variety."
        }

        let spiceModifier = getSpiceModifier(spiceLevel)

        switch category {
        case .dadJokes:
            return """
            DAD JOKE FOCUS:
            - Classic groan-worthy puns and wordplay
            - Innocent double meanings and clever twists
            - Family-friendly humor with unexpected punchlines
            - \(spiceModifier)
            """

        case .darkHumor:
            return """
            DARK HUMOR FOCUS:
            - Morbid comedy with clever twists
            - Existential observations with dark irony
            - Gallows humor that's thought-provoking
            - \(spiceModifier)
            """

        case .puns:
            return """
            PUN FOCUS:
            - Sophisticated wordplay and double meanings
            - Linguistic cleverness and sound-alike words
            - Multiple layers of meaning in single phrases
            - \(spiceModifier)
            """

        case .pickupLines:
            return """
            PICK-UP LINE FOCUS:
            - Cheesy romantic humor with charm
            - Flirty wordplay and compliments
            - Self-aware cheese that's endearing
            - \(spiceModifier)
            """

        case .geekJokes:
            return """
            GEEK HUMOR FOCUS:
            - Tech, science, and gaming references
            - Programming and internet culture humor
            - Smart references that geeks will appreciate
            - \(spiceModifier)
            """

        case .workJokes:
            return """
            WORKPLACE HUMOR FOCUS:
            - Office life and corporate culture satire
            - Meeting and email humor
            - Boss and coworker dynamics
            - \(spiceModifier)
            """
        }
    }

    private func getSpiceModifier(_ spiceLevel: SpiceLevel) -> String {
        switch spiceLevel {
        case .mild:
            return "Keep it wholesome and appropriate for all audiences"
        case .medium:
            return "Add wit and clever edge while staying tasteful"
        case .savage:
            return "Go dark and edgy with brutal honesty, but keep it clever"
        }
    }
    
    private func createPersonalizedSystemPrompt(avoidancePhrases: [String]) -> String {
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceSection: String
        if limitedPhrases.isEmpty {
            avoidanceSection = ""
        } else {
            avoidanceSection = """

        EXCLUSION LIST: Ensure complete originality - avoid similarity to \(limitedPhrases.count) previous jokes.
        """
        }

        return """
        You are an expert comedian specializing in personalized humor. Create one custom joke using the provided personal context.

        PERSONALIZATION REQUIREMENTS:
        - Return ONLY the joke (no explanation, analysis, or commentary).
        - 1-2 sentences maximum, under 150 characters total.
        - Seamlessly weave the personal details into the joke's setup or punchline.
        - Make the personal elements feel natural, not forced.
        - Ensure the joke is genuinely funny and tailored to the specific context.

        QUALITY STANDARDS:
        - The joke must be completely original and fresh.
        - Use the personal context as the foundation for humor, not just a random insertion.
        - Maintain appropriate tone based on the specified spice level.
        - Create something the person would genuinely want to share.
        - Never reuse themes, setups, or punchlines from previous outputs.\(avoidanceSection)

        Focus on creating a memorable, personalized joke that feels custom-made for this specific person.
        """
    }
    
    private func createPersonalizedUserPrompt(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        count: Int,
        avoidancePhrases: [String]
    ) -> String {
        let spiceDescription = spiceLevel.rawValue.uppercased()
        let categoryDescription = category?.rawValue ?? "general humor"
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceReminder: String
        if limitedPhrases.isEmpty {
            avoidanceReminder = ""
        } else {
            avoidanceReminder = "\n\nORIGINALITY REQUIREMENT: Ensure complete originality from your \(limitedPhrases.count) previous jokes."
        }

        // Get category-specific guidance for personalized jokes
        let personalizedGuidance = createPersonalizedCategoryGuidance(category: category, spiceLevel: spiceLevel)

        return """
        PERSONALIZATION BRIEF:
        Spice Level: \(spiceDescription)
        Category: \(categoryDescription)
        Personal Context: "\(personalContext)"

        \(personalizedGuidance)

        TASK: Create one perfectly tailored joke that:
        - Uses specific details from the personal context as the foundation
        - Feels custom-made for this person's situation
        - Maintains the \(spiceDescription.lowercased()) spice level appropriately
        - Is genuinely funny and shareable\(avoidanceReminder)

        Return only the personalized joke.
        """
    }

    private func createPersonalizedCategoryGuidance(category: JokeCategory?, spiceLevel: SpiceLevel) -> String {
        guard let category = category else {
            return "Create a personalized joke that naturally incorporates their context into any comedic style."
        }

        let spiceModifier = getSpiceModifier(spiceLevel)

        switch category {
        case .dadJokes:
            return """
            PERSONALIZED DAD JOKE APPROACH:
            - Turn their personal details into wholesome puns
            - Use their context for innocent wordplay
            - \(spiceModifier)
            """

        case .darkHumor:
            return """
            PERSONALIZED DARK HUMOR APPROACH:
            - Find the darkly ironic angle in their situation
            - Use their context for existential or morbid comedy
            - \(spiceModifier)
            """

        case .puns:
            return """
            PERSONALIZED PUN APPROACH:
            - Create wordplay based on their specific details
            - Find double meanings in their personal context
            - \(spiceModifier)
            """

        case .pickupLines:
            return """
            PERSONALIZED PICK-UP LINE APPROACH:
            - Turn their details into charming romantic humor
            - Use their context for flirty compliments
            - \(spiceModifier)
            """

        case .geekJokes:
            return """
            PERSONALIZED GEEK HUMOR APPROACH:
            - Connect their context to tech/science/gaming references
            - Use their details for smart nerd culture humor
            - \(spiceModifier)
            """

        case .workJokes:
            return """
            PERSONALIZED WORK HUMOR APPROACH:
            - Turn their context into workplace comedy
            - Use their details for office life satire
            - \(spiceModifier)
            """
        }
    }
    
    private func parseJokeMessages(from content: String) -> [String] {
        debugLog("OpenAI Raw Response Content: '\(content)'")
        debugLog("OpenAI Content Length: \(content.count)")
        debugLog("OpenAI Content isEmpty: \(content.isEmpty)")
        
        guard !content.isEmpty else {
            debugLog("OpenAI CRITICAL: Empty response content - GPT-5 may be using all tokens for reasoning")
            return []
        }
        
        // Split by newlines and clean up
        let lines = content.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        debugLog("OpenAI Split Lines: \(lines)")
        
        // Remove common formatting patterns
        let cleanedMessages = lines
            .filter { !$0.hasPrefix("1.") && !$0.hasPrefix("2.") && !$0.hasPrefix("3.") && !$0.hasPrefix("4.") && !$0.hasPrefix("5.") }
            .filter { !$0.hasPrefix("•") && !$0.hasPrefix("*") }
            .map { message in
                var cleaned = message
                
                // Remove bullet points and dashes
                if cleaned.hasPrefix("- ") {
                    cleaned = String(cleaned.dropFirst(2))
                } else if cleaned.hasPrefix("• ") {
                    cleaned = String(cleaned.dropFirst(2))
                } else if cleaned.hasPrefix("* ") {
                    cleaned = String(cleaned.dropFirst(2))
                }
                
                // Remove quotes if the entire message is quoted
                if cleaned.hasPrefix("\"") && cleaned.hasSuffix("\"") && cleaned.count > 2 {
                    cleaned = String(cleaned.dropFirst().dropLast())
                }
                
                return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
            }
            .filter { !$0.isEmpty }
            .filter { $0.count >= 10 } // Ensure messages are substantial
        
        debugLog("OpenAI Cleaned Messages: \(cleanedMessages)")

        if cleanedMessages.isEmpty {
            debugLog("OpenAI Warning: No valid messages found after parsing")
        }
        
        return cleanedMessages
    }
    
    // MARK: - Cost Calculation
    private func calculateCost(model: String, usage: Usage) -> Double {
        // OpenAI pricing as of 2025 (per 1M tokens)
        let pricing: (input: Double, output: Double) = {
            if model.contains("gpt-5-mini") {
                return (0.250, 2.000) // GPT-5 Mini pricing
            } else if model.contains("gpt-5") {
                return (1.250, 10.000) // GPT-5 pricing (future)
            } else {
                return (0.250, 2.000) // Default to GPT-5 Mini
            }
        }()
        
        let inputCost = Double(usage.promptTokens) * pricing.input / 1_000_000
        let outputCost = Double(usage.completionTokens) * pricing.output / 1_000_000
        
        return inputCost + outputCost
    }
    
    // MARK: - Batch Optimization for Joke Generation
    func generateBatchJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        userTier: SubscriptionTier,
        batchSize: Int = 10
    ) async throws -> [String] {
        return try await generateJokeMessages(
            spiceLevel: spiceLevel,
            category: category,
            count: batchSize,
            userTier: userTier,
            useOptimalModel: false // Use cost-effective GPT-5 Mini for batch joke generation
        ).messages
    }
}

enum OpenAIError: LocalizedError {
    case missingAPIKey
    case invalidAPIKey
    case invalidResponse
    case apiError(Int)
    case apiErrorWithDetails(Int, String)
    case noResponse
    case networkError
    case invalidParameter(String)
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "Missing OpenAI API key."
        case .invalidAPIKey:
            return "Invalid OpenAI API key. Please check your configuration."
        case .invalidResponse:
            return "Invalid response from OpenAI API."
        case .apiError(let code):
            return "OpenAI API error with code: \(code)"
        case .apiErrorWithDetails(let code, let details):
            return "OpenAI API error \(code): \(details)"
        case .noResponse:
            return "No response received from OpenAI API."
        case .networkError:
            return "Network error occurred while connecting to OpenAI."
        case .invalidParameter(let details):
            return "Invalid parameter: \(details)"
        }
    }
}

// MARK: - Configuration Helper
struct OpenAIConfig {
    static var apiKey: String {
        // Priority order: Keychain -> Plist -> UserDefaults
        
        // 1. Check Keychain first (most secure)
        if let keychainKey = KeychainHelper.shared.getOpenAIAPIKey(),
           !keychainKey.isEmpty {
            debugLog("OpenAI Config: Found API key in Keychain (length: \(keychainKey.count))")
            return keychainKey
        }
        
        // 2. Check plist for development
        if let path = Bundle.main.path(forResource: "OpenAI-Config", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let key = plist["API_KEY"] as? String,
           key != "YOUR_API_KEY_HERE" && !key.isEmpty {
            debugLog("OpenAI Config: Found API key in plist (length: \(key.count))")
            
            // Auto-migrate to Keychain for better security
            KeychainHelper.shared.saveOpenAIAPIKey(key)
            debugLog("OpenAI Config: Migrated API key from plist to Keychain")
            
            return key
        }

        // 3. Fallback to UserDefaults for legacy support
        let fallbackKey = UserDefaults.standard.string(forKey: "OpenAI_API_Key") ?? ""
        
        if !fallbackKey.isEmpty {
            debugLog("OpenAI Config: Using fallback key from UserDefaults (length: \(fallbackKey.count))")
            
            // Auto-migrate to Keychain
            KeychainHelper.shared.saveOpenAIAPIKey(fallbackKey)
            UserDefaults.standard.removeObject(forKey: "OpenAI_API_Key")
            debugLog("OpenAI Config: Migrated API key from UserDefaults to Keychain")
            
            return fallbackKey
        }
        
        debugLog("OpenAI Config: WARNING - No API key configured! App will use fallback sample messages only.")
        return ""
    }

    static func setAPIKey(_ key: String) {
        KeychainHelper.shared.saveOpenAIAPIKey(key)
        debugLog("OpenAI Config: API key saved to Keychain")
    }
    
    static func deleteAPIKey() {
        KeychainHelper.shared.deleteOpenAIAPIKey()
        debugLog("OpenAI Config: API key deleted from Keychain")
    }
    
    static func hasAPIKey() -> Bool {
        return KeychainHelper.shared.hasOpenAIAPIKey()
    }
    
    static func validateAPIKey(_ apiKey: String) -> Bool {
        let isValid = !apiKey.isEmpty && 
                      apiKey.hasPrefix("sk-") && 
                      apiKey.count > 20
        
        debugLog("OpenAI Config: API key validation - Valid: \(isValid), Length: \(apiKey.count), Prefix: \(apiKey.hasPrefix("sk-"))")

        if !isValid {
            if apiKey.isEmpty {
                debugLog("OpenAI Config: Error - API key is empty")
            } else if !apiKey.hasPrefix("sk-") {
                debugLog("OpenAI Config: Error - API key doesn't start with 'sk-'")
            } else {
                debugLog("OpenAI Config: Error - API key is too short")
            }
        }

        return isValid
    }

    static func testAPIKey() async -> Bool {
        let apiKey = OpenAIConfig.apiKey

        guard validateAPIKey(apiKey) else {
            debugLog("OpenAI Config: API key validation failed")
            return false
        }

        // Simple test request to verify API key works with joke generation
        let testService = OpenAIService(apiKey: apiKey)
        do {
            debugLog("OpenAI Config: Testing API key with simple joke generation request...")
            let result = try await testService.generateJokeMessages(spiceLevel: .mild, category: .dadJokes, count: 1)
            debugLog("OpenAI Config: API key test successful - Generated \(result.messages.count) joke(s)")
            return true
        } catch {
            debugLog("OpenAI Config: API key test failed - \(error)")
            return false
        }
    }
}
