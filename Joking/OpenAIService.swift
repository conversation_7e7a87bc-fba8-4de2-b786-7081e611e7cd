//
//  OpenAIService.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation

struct OpenAIService {
    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1/chat/completions"

    init(apiKey: String) {
        self.apiKey = apiKey
    }
    
    struct OpenAIRequest: Codable {
        let model: String
        let messages: [Message]
        let maxCompletionTokens: Int
        let temperature: Double
        let n: Int
        
        enum CodingKeys: String, CodingKey {
            case model, messages, temperature, n
            case maxCompletionTokens = "max_completion_tokens"
        }
    }
    
    struct Message: Codable {
        let role: String
        let content: String
    }
    
    struct OpenAIResponse: Codable {
        let choices: [Choice]
        let usage: Usage?
    }
    
    struct Choice: Codable {
        let message: Message
        let finishReason: String?
        
        enum CodingKeys: String, CodingKey {
            case message
            case finishReason = "finish_reason"
        }
    }
    
    struct Usage: Codable {
        let promptTokens: Int
        let completionTokens: Int
        let totalTokens: Int
        
        enum CodingKeys: String, CodingKey {
            case promptTokens = "prompt_tokens"
            case completionTokens = "completion_tokens"  
            case totalTokens = "total_tokens"
        }
    }
    
    struct ChatGenerationResult {
        let messages: [String]
        let usage: Usage?
        let estimatedCost: Double?
        let model: String
    }

    func generateJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        count: Int = 10,
        userTier: SubscriptionTier = .free,
        useOptimalModel: Bool = false,
        avoidPhrases: [String] = []
    ) async throws -> ChatGenerationResult {
        // Validate parameters
        try validateParameters(count: count, temperature: 1.0, maxTokens: 1000)
        
        let model = selectOptimalModel(userTier: userTier, useOptimalModel: useOptimalModel)
        let systemPrompt = createSystemPrompt(forBatchGeneration: count > 5, avoidancePhrases: avoidPhrases)
        let userPrompt = createUserPrompt(spiceLevel: spiceLevel, category: category, count: count, avoidancePhrases: avoidPhrases)
        
        // Log the prompts for debugging
        debugLog("OpenAI System Prompt: \(systemPrompt)")
        debugLog("OpenAI User Prompt: \(userPrompt)")
        debugLog("OpenAI Selected Model: \(model)")
        
        let maxTokens = getOptimalTokenLimit(model: model, count: count)
        debugLog("OpenAI Max Completion Tokens Set: \(maxTokens)")
        
        let request = OpenAIRequest(
            model: model,
            messages: [
                Message(role: "system", content: systemPrompt),
                Message(role: "user", content: userPrompt)
            ],
            maxCompletionTokens: maxTokens,
            temperature: getOptimalTemperature(model: model),
            n: 1
        )
        
        var urlRequest = URLRequest(url: URL(string: baseURL)!)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONEncoder().encode(request)
        urlRequest.httpBody = jsonData
        
        // Log the request for debugging
        if let requestBody = String(data: jsonData, encoding: .utf8) {
            debugLog("OpenAI Request Body: \(requestBody)")
        }
        
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            debugLog("OpenAI Error: Invalid response type")
            throw OpenAIError.invalidResponse
        }

        // Log response for debugging
        debugLog("OpenAI Response Status: \(httpResponse.statusCode)")
        if let responseBody = String(data: data, encoding: .utf8) {
            debugLog("OpenAI Response Body: \(responseBody)")
        }

        if httpResponse.statusCode == 401 {
            debugLog("OpenAI Error: Invalid API key (401)")
            throw OpenAIError.invalidAPIKey
        }

        guard httpResponse.statusCode == 200 else {
            // Try to parse error details from response body
            if let errorBody = String(data: data, encoding: .utf8) {
                debugLog("OpenAI API Error \(httpResponse.statusCode): \(errorBody)")
                throw OpenAIError.apiErrorWithDetails(httpResponse.statusCode, errorBody)
            } else {
                debugLog("OpenAI API Error \(httpResponse.statusCode): No error details available")
                throw OpenAIError.apiError(httpResponse.statusCode)
            }
        }

        let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)
        
        // Log token usage for cost tracking
        if let usage = openAIResponse.usage {
            debugLog("OpenAI Usage - Prompt: \(usage.promptTokens), Completion: \(usage.completionTokens), Total: \(usage.totalTokens)")
            debugLog("OpenAI Model: \(model)")
            
            // Check if GPT-5 used all tokens for reasoning
            if model.contains("gpt-5") && !model.contains("mini") && usage.completionTokens >= 5800 {
                debugLog("OpenAI WARNING: GPT-5 may have used all tokens for reasoning (completion: \(usage.completionTokens))")
            }

            // Calculate approximate cost
            let estimatedCost = calculateCost(model: model, usage: usage)
            debugLog("OpenAI Estimated Cost: $\(String(format: "%.6f", estimatedCost))")
        }
        
        guard let choice = openAIResponse.choices.first else {
            debugLog("OpenAI Error: No choices in response")
            throw OpenAIError.noResponse
        }
        
        debugLog("OpenAI Choice Message Content Length: \(choice.message.content.count)")
        debugLog("OpenAI Choice Finish Reason: \(choice.finishReason ?? "nil")")
        
        let messages = parseJokeMessages(from: choice.message.content)
        let estimatedCost = openAIResponse.usage.flatMap { usage in
            calculateCost(model: model, usage: usage)
        }
        
        return ChatGenerationResult(messages: messages, usage: openAIResponse.usage, estimatedCost: estimatedCost, model: model)
    }
    
    func generatePersonalizedJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        count: Int = 1,
        userTier: SubscriptionTier = .premium,
        useOptimalModel: Bool = true,
        avoidPhrases: [String] = []
    ) async throws -> ChatGenerationResult {
        // Validate parameters
        try validateParameters(count: count, temperature: 1.0, maxTokens: 1000)
        
        let model = selectOptimalModel(userTier: userTier, useOptimalModel: useOptimalModel)
        let systemPrompt = createPersonalizedSystemPrompt(avoidancePhrases: avoidPhrases)
        let userPrompt = createPersonalizedUserPrompt(
            spiceLevel: spiceLevel,
            category: category,
            personalContext: personalContext,
            count: count,
            avoidancePhrases: avoidPhrases
        )
        
        // Log the prompts for debugging
        debugLog("OpenAI Personalized System Prompt: \(systemPrompt)")
        debugLog("OpenAI Personalized User Prompt: \(userPrompt)")
        debugLog("OpenAI Personalized Model: \(model)")
        
        let maxTokens = getOptimalTokenLimit(model: model, count: count)
        debugLog("OpenAI Personalized Max Completion Tokens Set: \(maxTokens)")
        
        let request = OpenAIRequest(
            model: model,
            messages: [
                Message(role: "system", content: systemPrompt),
                Message(role: "user", content: userPrompt)
            ],
            maxCompletionTokens: maxTokens,
            temperature: getOptimalTemperature(model: model),
            n: 1
        )
        
        var urlRequest = URLRequest(url: URL(string: baseURL)!)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let jsonData = try JSONEncoder().encode(request)
        urlRequest.httpBody = jsonData
        
        // Log the request for debugging
        if let requestBody = String(data: jsonData, encoding: .utf8) {
            debugLog("OpenAI Request Body: \(requestBody)")
        }
        
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            debugLog("OpenAI Error: Invalid response type")
            throw OpenAIError.invalidResponse
        }

        // Log response for debugging
        debugLog("OpenAI Response Status: \(httpResponse.statusCode)")
        if let responseBody = String(data: data, encoding: .utf8) {
            debugLog("OpenAI Response Body: \(responseBody)")
        }

        if httpResponse.statusCode == 401 {
            debugLog("OpenAI Error: Invalid API key (401)")
            throw OpenAIError.invalidAPIKey
        }
        
        guard httpResponse.statusCode == 200 else {
            // Try to parse error details from response body
            if let errorBody = String(data: data, encoding: .utf8) {
                debugLog("OpenAI API Error \(httpResponse.statusCode): \(errorBody)")
                throw OpenAIError.apiErrorWithDetails(httpResponse.statusCode, errorBody)
            } else {
                debugLog("OpenAI API Error \(httpResponse.statusCode): No error details available")
                throw OpenAIError.apiError(httpResponse.statusCode)
            }
        }
        
        let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)
        
        // Log token usage for cost tracking
        if let usage = openAIResponse.usage {
            debugLog("OpenAI Personalized Usage - Prompt: \(usage.promptTokens), Completion: \(usage.completionTokens), Total: \(usage.totalTokens)")
            debugLog("OpenAI Personalized Model: \(model)")
            
            // Check if GPT-5 used all tokens for reasoning
            if model.contains("gpt-5") && !model.contains("mini") && usage.completionTokens >= 5800 {
                debugLog("OpenAI Personalized WARNING: GPT-5 may have used all tokens for reasoning (completion: \(usage.completionTokens))")
            }

            // Calculate approximate cost
            let estimatedCost = calculateCost(model: model, usage: usage)
            debugLog("OpenAI Personalized Estimated Cost: $\(String(format: "%.6f", estimatedCost))")
        }
        
        guard let choice = openAIResponse.choices.first else {
            debugLog("OpenAI Personalized Error: No choices in response")
            throw OpenAIError.noResponse
        }
        
        debugLog("OpenAI Personalized Choice Message Content Length: \(choice.message.content.count)")
        debugLog("OpenAI Personalized Choice Finish Reason: \(choice.finishReason ?? "nil")")
        
        let messages = parseJokeMessages(from: choice.message.content)
        let estimatedCost = openAIResponse.usage.flatMap { usage in
            calculateCost(model: model, usage: usage)
        }
        
        return ChatGenerationResult(messages: messages, usage: openAIResponse.usage, estimatedCost: estimatedCost, model: model)
    }
    
    // MARK: - Parameter Validation
    private func validateParameters(count: Int, temperature: Double, maxTokens: Int) throws {
        guard count > 0 && count <= 10 else {
            throw OpenAIError.invalidParameter("Count must be between 1 and 10, got \(count)")
        }
        
        guard temperature >= 0.0 && temperature <= 2.0 else {
            throw OpenAIError.invalidParameter("Temperature must be between 0.0 and 2.0, got \(temperature)")
        }
        
        guard maxTokens > 0 && maxTokens <= 4096 else {
            throw OpenAIError.invalidParameter("Max tokens must be between 1 and 4096, got \(maxTokens)")
        }
        
        debugLog("OpenAI Parameters validated - Count: \(count), Temperature: \(temperature), MaxTokens: \(maxTokens)")
    }
    
    // MARK: - Model Selection & Optimization
    private func selectOptimalModel(userTier: SubscriptionTier, useOptimalModel: Bool) -> String {
        if userTier == .premium && useOptimalModel {
            return "gpt-5-2025-08-07"
        }
        return "gpt-5-mini-2025-08-07"
    }
    
    private func getOptimalTokenLimit(model: String, count: Int) -> Int {
        // GPT-5 needs significantly more tokens due to reasoning overhead
        if model.contains("gpt-5-mini") {
            let baseTokens = 1000
            let batchMultiplier = max(1, count / 5)
            return min(baseTokens * batchMultiplier, 2000)
        } else {
            // GPT-5 uses many tokens for reasoning, needs much higher limits
            let baseTokens = 2000  // Double base tokens for GPT-5
            let batchMultiplier = max(1, count / 3)  // More aggressive scaling
            let calculatedTokens = baseTokens * batchMultiplier
            debugLog("OpenAI Token Calculation: base=\(baseTokens), multiplier=\(batchMultiplier), total=\(calculatedTokens)")
            return min(calculatedTokens, 6000)  // Increase max to 6000 for GPT-5
        }
    }
    
    private func getOptimalTemperature(model: String) -> Double {
        debugLog("OpenAI Model: \(model)")
        // GPT-5 Mini only supports temperature 1.0, so use that for all models for now
        debugLog("OpenAI Temperature: Using 1.0 (required for GPT-5 Mini)")
        return 1.0
    }
    
    private func createSystemPrompt(forBatchGeneration: Bool = false, avoidancePhrases: [String]) -> String {
        let batchInstructions = forBatchGeneration ? """

        BATCH DIVERSITY RULES:
        - Deliver exactly the requested number of messages.
        - Give each line a different hook (question, bold statement, playful dare, poetic compliment, etc.).
        - Vary sentence length and rhythm so nothing feels templated.
        - Change up imagery, scenarios, and emotional angles across the batch.
        """ : ""

        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))

        let avoidanceSection: String
        if limitedPhrases.isEmpty {
            avoidanceSection = ""
        } else {
            avoidanceSection = """

        EXCLUSION LIST: Avoid repeating or paraphrasing any of the \(limitedPhrases.count) previously used messages.
        """
        }

        return """
        You are a creative joke generator. Craft inventive, high-quality jokes that are funny, clever, and appropriate for sharing.

        QUALITY & VARIETY RULES:
        - Output ONLY the jokes (no numbering, explanations, or extra text).
        - 1-2 sentences per joke, max 150 characters.
        - Every joke must feel fresh, original, and clever—no overused or stale jokes.
        - Start each joke differently (question, setup-punchline, observation, wordplay, etc.).
        - Include clever wordplay, unexpected twists, or relatable situations.
        - Never recycle wording, punchlines, or setups from earlier outputs. Duplicate or near-duplicate content is a hard failure.
        \(batchInstructions)\(avoidanceSection)

        SPICE LEVELS:
        • Mild: Light, friendly humor suitable for all audiences
        • Medium: Witty, sharp humor with clever wordplay
        • Savage: Dark, brutal humor with edgy punchlines (but tasteful)

        CATEGORIES:
        • Dad Jokes: Classic, wholesome, groan-worthy puns
        • Dark Humor: Edgy, unexpected, morbid comedy
        • Puns: Clever wordplay and double meanings
        • Pick-up Lines: Funny, cheesy romantic one-liners
        • Geek Jokes: Tech, science, gaming, and nerd culture humor
        • Work Jokes: Office life, meetings, and workplace humor

        Generate creative, genuinely funny jokes that people would be excited to share.
        """
    }
    
    private func createUserPrompt(spiceLevel: SpiceLevel, category: JokeCategory?, count: Int, avoidancePhrases: [String]) -> String {
        let spiceDescription = spiceLevel.rawValue.lowercased()
        let categoryDescription = category?.rawValue.lowercased() ?? "any category"
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceReminder: String
        if limitedPhrases.isEmpty {
            avoidanceReminder = ""
        } else {
            avoidanceReminder = "\nEnsure complete originality - avoid any similarity to your \(limitedPhrases.count) previous messages."
        }

        return """
        Create \(count) \(spiceDescription) spice level jokes in the \(categoryDescription) category.
        Make each joke feel unique and original with different setups and punchlines.
        Keep the humor clever, engaging, and genuinely funny—no stale or overused jokes.\(avoidanceReminder)

        Return only the jokes, one per line.
        """
    }
    
    private func createPersonalizedSystemPrompt(avoidancePhrases: [String]) -> String {
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceSection: String
        if limitedPhrases.isEmpty {
            avoidanceSection = ""
        } else {
            avoidanceSection = """

        EXCLUSION LIST: Ensure complete originality - avoid similarity to \(limitedPhrases.count) previous messages.
        """
        }

        return """
        Generate one personalized joke using the provided context.

        Requirements:
        - Return ONLY the joke (no explanation or analysis).
        - 1-2 sentences, under 150 characters.
        - Incorporate the provided personal details into the joke setup or punchline.
        - Keep it funny, clever, and appropriately tailored to the context.
        - Never reuse wording from previous outputs or from the exclusion list.\(avoidanceSection)
        """
    }
    
    private func createPersonalizedUserPrompt(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        personalContext: String,
        count: Int,
        avoidancePhrases: [String]
    ) -> String {
        let spiceDescription = spiceLevel.rawValue.lowercased()
        let categoryDescription = category?.rawValue.lowercased() ?? "any"
        let trimmedPhrases = avoidancePhrases
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let limitedPhrases = Array(trimmedPhrases.prefix(20))
        let avoidanceReminder: String
        if limitedPhrases.isEmpty {
            avoidanceReminder = ""
        } else {
            avoidanceReminder = "\n\nEnsure complete originality from your \(limitedPhrases.count) previous messages."
        }
        
        return """
        Spice level: \(spiceDescription.capitalized)
        Category: \(categoryDescription)
        Personal details to weave in: \(personalContext)
        Craft one joke that incorporates a specific detail and feels authentically tailored to the context.
        Keep it funny yet appropriate.\(avoidanceReminder)
        """
    }
    
    private func parseJokeMessages(from content: String) -> [String] {
        debugLog("OpenAI Raw Response Content: '\(content)'")
        debugLog("OpenAI Content Length: \(content.count)")
        debugLog("OpenAI Content isEmpty: \(content.isEmpty)")
        
        guard !content.isEmpty else {
            debugLog("OpenAI CRITICAL: Empty response content - GPT-5 may be using all tokens for reasoning")
            return []
        }
        
        // Split by newlines and clean up
        let lines = content.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        debugLog("OpenAI Split Lines: \(lines)")
        
        // Remove common formatting patterns
        let cleanedMessages = lines
            .filter { !$0.hasPrefix("1.") && !$0.hasPrefix("2.") && !$0.hasPrefix("3.") && !$0.hasPrefix("4.") && !$0.hasPrefix("5.") }
            .filter { !$0.hasPrefix("•") && !$0.hasPrefix("*") }
            .map { message in
                var cleaned = message
                
                // Remove bullet points and dashes
                if cleaned.hasPrefix("- ") {
                    cleaned = String(cleaned.dropFirst(2))
                } else if cleaned.hasPrefix("• ") {
                    cleaned = String(cleaned.dropFirst(2))
                } else if cleaned.hasPrefix("* ") {
                    cleaned = String(cleaned.dropFirst(2))
                }
                
                // Remove quotes if the entire message is quoted
                if cleaned.hasPrefix("\"") && cleaned.hasSuffix("\"") && cleaned.count > 2 {
                    cleaned = String(cleaned.dropFirst().dropLast())
                }
                
                return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
            }
            .filter { !$0.isEmpty }
            .filter { $0.count >= 10 } // Ensure messages are substantial
        
        debugLog("OpenAI Cleaned Messages: \(cleanedMessages)")

        if cleanedMessages.isEmpty {
            debugLog("OpenAI Warning: No valid messages found after parsing")
        }
        
        return cleanedMessages
    }
    
    // MARK: - Cost Calculation
    private func calculateCost(model: String, usage: Usage) -> Double {
        // OpenAI pricing as of 2025 (per 1M tokens)
        let pricing: (input: Double, output: Double) = {
            if model.contains("gpt-5-mini") {
                return (0.250, 2.000) // GPT-5 Mini pricing
            } else if model.contains("gpt-5") {
                return (1.250, 10.000) // GPT-5 pricing (future)
            } else {
                return (0.250, 2.000) // Default to GPT-5 Mini
            }
        }()
        
        let inputCost = Double(usage.promptTokens) * pricing.input / 1_000_000
        let outputCost = Double(usage.completionTokens) * pricing.output / 1_000_000
        
        return inputCost + outputCost
    }
    
    // MARK: - Batch Optimization
    func generateBatchJokeMessages(
        spiceLevel: SpiceLevel,
        category: JokeCategory?,
        userTier: SubscriptionTier,
        batchSize: Int = 10
    ) async throws -> [String] {
        return try await generateJokeMessages(
            spiceLevel: spiceLevel,
            category: category,
            count: batchSize,
            userTier: userTier,
            useOptimalModel: false // Use cost-effective model for batch generation
        ).messages
    }
}

enum OpenAIError: LocalizedError {
    case missingAPIKey
    case invalidAPIKey
    case invalidResponse
    case apiError(Int)
    case apiErrorWithDetails(Int, String)
    case noResponse
    case networkError
    case invalidParameter(String)
    
    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "Missing OpenAI API key."
        case .invalidAPIKey:
            return "Invalid OpenAI API key. Please check your configuration."
        case .invalidResponse:
            return "Invalid response from OpenAI API."
        case .apiError(let code):
            return "OpenAI API error with code: \(code)"
        case .apiErrorWithDetails(let code, let details):
            return "OpenAI API error \(code): \(details)"
        case .noResponse:
            return "No response received from OpenAI API."
        case .networkError:
            return "Network error occurred while connecting to OpenAI."
        case .invalidParameter(let details):
            return "Invalid parameter: \(details)"
        }
    }
}

// MARK: - Configuration Helper
struct OpenAIConfig {
    static var apiKey: String {
        // Priority order: Keychain -> Plist -> UserDefaults
        
        // 1. Check Keychain first (most secure)
        if let keychainKey = KeychainHelper.shared.getOpenAIAPIKey(),
           !keychainKey.isEmpty {
            debugLog("OpenAI Config: Found API key in Keychain (length: \(keychainKey.count))")
            return keychainKey
        }
        
        // 2. Check plist for development
        if let path = Bundle.main.path(forResource: "OpenAI-Config", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let key = plist["API_KEY"] as? String,
           key != "YOUR_API_KEY_HERE" && !key.isEmpty {
            debugLog("OpenAI Config: Found API key in plist (length: \(key.count))")
            
            // Auto-migrate to Keychain for better security
            KeychainHelper.shared.saveOpenAIAPIKey(key)
            debugLog("OpenAI Config: Migrated API key from plist to Keychain")
            
            return key
        }

        // 3. Fallback to UserDefaults for legacy support
        let fallbackKey = UserDefaults.standard.string(forKey: "OpenAI_API_Key") ?? ""
        
        if !fallbackKey.isEmpty {
            debugLog("OpenAI Config: Using fallback key from UserDefaults (length: \(fallbackKey.count))")
            
            // Auto-migrate to Keychain
            KeychainHelper.shared.saveOpenAIAPIKey(fallbackKey)
            UserDefaults.standard.removeObject(forKey: "OpenAI_API_Key")
            debugLog("OpenAI Config: Migrated API key from UserDefaults to Keychain")
            
            return fallbackKey
        }
        
        debugLog("OpenAI Config: WARNING - No API key configured! App will use fallback sample messages only.")
        return ""
    }

    static func setAPIKey(_ key: String) {
        KeychainHelper.shared.saveOpenAIAPIKey(key)
        debugLog("OpenAI Config: API key saved to Keychain")
    }
    
    static func deleteAPIKey() {
        KeychainHelper.shared.deleteOpenAIAPIKey()
        debugLog("OpenAI Config: API key deleted from Keychain")
    }
    
    static func hasAPIKey() -> Bool {
        return KeychainHelper.shared.hasOpenAIAPIKey()
    }
    
    static func validateAPIKey(_ apiKey: String) -> Bool {
        let isValid = !apiKey.isEmpty && 
                      apiKey.hasPrefix("sk-") && 
                      apiKey.count > 20
        
        debugLog("OpenAI Config: API key validation - Valid: \(isValid), Length: \(apiKey.count), Prefix: \(apiKey.hasPrefix("sk-"))")

        if !isValid {
            if apiKey.isEmpty {
                debugLog("OpenAI Config: Error - API key is empty")
            } else if !apiKey.hasPrefix("sk-") {
                debugLog("OpenAI Config: Error - API key doesn't start with 'sk-'")
            } else {
                debugLog("OpenAI Config: Error - API key is too short")
            }
        }

        return isValid
    }

    static func testAPIKey() async -> Bool {
        let apiKey = OpenAIConfig.apiKey

        guard validateAPIKey(apiKey) else {
            debugLog("OpenAI Config: API key validation failed")
            return false
        }

        // Simple test request to verify API key works
        let testService = OpenAIService(apiKey: apiKey)
        do {
            debugLog("OpenAI Config: Testing API key with simple request...")
            _ = try await testService.generateJokeMessages(spiceLevel: .mild, category: nil, count: 1)
            debugLog("OpenAI Config: API key test successful")
            return true
        } catch {
            debugLog("OpenAI Config: API key test failed - \(error)")
            return false
        }
    }
}
