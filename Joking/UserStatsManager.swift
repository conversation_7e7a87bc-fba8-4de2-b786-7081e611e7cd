//
//  UserStatsManager.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import Foundation
import Combine
import CloudKit

@MainActor
class UserStatsManager: ObservableObject {
    @Published var stats: UserStats
    @Published var syncStatus: SyncStatus = .synced
    
    private let userDefaults = UserDefaults.standard
    private let statsKey = "HeartStarter_UserStats"
    private let cloudKitService = CloudKitService.shared
    private var authService: AuthenticationService?
    private var subscriptionManager = SubscriptionManager.shared
    
    enum SyncStatus {
        case syncing
        case synced
        case failed(String)
        case quotaExceeded(retryAfter: TimeInterval)
        case offline
    }
    
    init() {
        if let data = userDefaults.data(forKey: statsKey),
           let decoded = try? JSONDecoder().decode(UserStats.self, from: data) {
            self.stats = decoded
        } else {
            self.stats = UserStats()
        }
        
        // Check if we need to reset daily count
        checkDailyReset()
        
        // Listen for subscription updates
        NotificationCenter.default.addObserver(
            forName: .subscriptionUpdated,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                await self?.handleSubscriptionUpdate(notification)
            }
        }
        
        // Listen for free trial events
        NotificationCenter.default.addObserver(
            forName: .freeTrialStarted,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                await self?.handleFreeTrialStarted(notification)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .freeTrialExpired,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                await self?.handleFreeTrialExpired(notification)
            }
        }
    }
    
    func setAuthService(_ authService: AuthenticationService) {
        self.authService = authService
        
        // DISABLED CloudKit sync during startup for instant UI responsiveness
        // CloudKit will sync naturally when user interacts with the app
        debugLog("✅ Auth service set - CloudKit sync deferred for performance")
    }
    
    /// Validates subscription status using StoreKit when CloudKit is unavailable
    func validateSubscriptionWithStoreKit() async {
        let (storeKitTier, expiryDate) = await subscriptionManager.validateSubscriptionOffline()
        
        debugLog("🔍 StoreKit validation: StoreKit=\(storeKitTier), Local=\(stats.subscriptionTier)")
        debugLog("🔍 Expiry date from StoreKit: \(expiryDate?.description ?? "none")")
        debugLog("🔍 Current allowsPersonalTouch: \(stats.allowsPersonalTouch)")
        
        // If StoreKit shows premium but local stats show free, update local stats
        if storeKitTier == .premium && stats.subscriptionTier == .free {
            debugLog("🔄 StoreKit validation: User has premium, updating local stats with expiry: \(expiryDate?.description ?? "none")")
            updateSubscription(tier: .premium, productId: nil, expiryDate: expiryDate)
        } else if storeKitTier == .free && stats.subscriptionTier == .premium {
            debugLog("🔄 StoreKit validation: Subscription expired, updating local stats")
            updateSubscription(tier: .free, productId: nil, expiryDate: nil)
        } else {
            debugLog("✅ StoreKit validation: Local stats match StoreKit (\(storeKitTier))")
            debugLog("✅ Current stats after validation: allowsPersonalTouch=\(stats.allowsPersonalTouch)")
        }
    }
    
    func recordJokeGenerated(_ message: JokeMessage) {
        stats.ingestGeneratedMessage(message)
        saveStats()
        
        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    // MARK: - Enhanced Usage Tracking Methods
    func updateUserStatsAfterGeneration(with updatedStats: UserStats) {
        stats = updatedStats
        saveStats()
        
        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    func markMessageAsSeen(_ message: JokeMessage) {
        stats.markMessageAsSeen(message.id)
        stats.recordGeneratedMessage(message)
        saveStats()

        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    func upgradeToPremium() {
        stats.updateSubscription(tier: .premium, startDate: Date())
        saveStats()
        
        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    // MARK: - Subscription Management
    func updateSubscription(tier: SubscriptionTier, productId: String? = nil, expiryDate: Date? = nil, isCancelled: Bool = false, willAutoRenew: Bool = true) {
        let previousTier = stats.subscriptionTier
        let previousCancelledStatus = stats.subscriptionInfo.isCancelled
        let previousDailyCount = stats.dailyJokeCount
        
        debugLog("🔄 Updating subscription: \(previousTier) → \(tier), dailyFlirtCount: \(previousDailyCount)")
        
        stats.updateSubscription(tier: tier, startDate: Date(), expiryDate: expiryDate, productId: productId, isCancelled: isCancelled, willAutoRenew: willAutoRenew)
        
        debugLog("📊 After subscription update: dailyJokeCount: \(previousDailyCount) → \(stats.dailyJokeCount), tier: \(stats.subscriptionTier)")
        
        if tier == .free && previousTier == .premium {
            debugLog("🔄 User downgraded from premium to free - dailyFlirtCount reset for fresh start")
        }

        if previousTier != .premium && tier == .premium {
            stats.resetPremiumPersonalTouchBoost()
        }
        
        // Force @Published property to fire by reassigning the entire stats object
        let updatedStats = stats
        stats = updatedStats
        
        // Force UI update on main thread - especially important for cancellation status changes
        Task { @MainActor in
            self.objectWillChange.send()
        }
        
        // Additional UI refresh for cancellation status changes
        if isCancelled != previousCancelledStatus {
            debugLog("🔄 Cancellation status changed in UserStatsManager: \(previousCancelledStatus) → \(isCancelled)")
            DispatchQueue.main.async {
                self.objectWillChange.send()
            }
        }
        
        saveStats()
        
        // Always publish the subscription change immediately for UI updates
        debugLog("✅ Subscription updated locally: \(previousTier) → \(tier)")
        debugLog("🔍 Debug subscription info: tier=\(stats.subscriptionInfo.tier), isActive=\(stats.subscriptionInfo.isActive), expiryDate=\(stats.subscriptionInfo.expiryDate?.description ?? "none")")
        debugLog("🔍 Cancellation info: isCancelled=\(stats.subscriptionInfo.isCancelled), willAutoRenew=\(stats.subscriptionInfo.willAutoRenew)")
        debugLog("🔄 UI should now reflect: allowsPersonalTouch=\(stats.allowsPersonalTouch), tier=\(stats.subscriptionTier), statusText=\(stats.subscriptionInfo.subscriptionStatusText), statusColor=\(stats.subscriptionInfo.subscriptionStatusColor)")
        debugLog("🔄 Final check - isPremium: \(stats.isPremium), effectiveTier: \(stats.subscriptionInfo.effectiveTier)")
        
        // Try to sync with CloudKit if user is signed in (but don't block on this)
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
                debugLog("✅ Subscription sync attempted (status handled internally)")
                
                // Try to save subscription history (separate from main sync)
                do {
                    try await cloudKitService.saveSubscriptionHistory(
                        userId: userId,
                        action: tier == .premium ? "upgrade" : "downgrade",
                        fromTier: previousTier,
                        toTier: tier,
                        transactionId: productId ?? UUID().uuidString
                    )
                    debugLog("✅ Subscription history saved to CloudKit")
                } catch {
                    debugLog("⚠️ Failed to save subscription history (not critical): \(error)")
                }
            }
        }
    }
    
    func cancelSubscription() {
        stats.cancelSubscription()
        saveStats()
        
        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
                
                // Cancel in CloudKit
                do {
                    try await cloudKitService.cancelSubscription(userId: userId)
                } catch {
                    debugLog("Failed to cancel subscription in CloudKit: \(error)")
                }
            }
        }
    }
    
    func restoreSubscription(tier: SubscriptionTier, expiryDate: Date?, productId: String?) {
        stats.restoreSubscription(tier: tier, expiryDate: expiryDate, productId: productId)
        saveStats()
        
        // Sync with CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    func validateSubscriptionStatus() async {
        guard let authService = authService,
              authService.isSignedIn,
              let userId = authService.userIdentifier else {
            return
        }
        
        do {
            let cloudTier = try await cloudKitService.validateSubscriptionStatus(userId: userId)
            
            if cloudTier != stats.subscriptionTier {
                // Update local stats to match CloudKit
                stats.updateSubscription(tier: cloudTier)
                saveStats()
            }
        } catch {
            debugLog("Failed to validate subscription status: \(error)")
        }
    }
    
    private func handleSubscriptionUpdate(_ notification: Notification) async {
        guard let userInfo = notification.userInfo,
              let tier = userInfo["tier"] as? SubscriptionTier else {
            debugLog("❌ handleSubscriptionUpdate: Missing tier in notification")
            return
        }
        
        // Make productId optional for restore cases
        let productId = userInfo["productId"] as? String
        let expiryDate = userInfo["expirationDate"] as? Date
        let isTrialPeriod = userInfo["isTrialPeriod"] as? Bool ?? false
        let isRestored = userInfo["isRestored"] as? Bool ?? false
        let isCancelled = userInfo["isCancelled"] as? Bool ?? false
        let willAutoRenew = userInfo["willAutoRenew"] as? Bool ?? true
        
        debugLog("📢 UserStatsManager received subscriptionUpdated notification:")
        debugLog("   - tier: \(tier)")
        debugLog("   - productId: \(productId ?? "none")")
        debugLog("   - expiryDate: \(expiryDate?.description ?? "none")")
        debugLog("   - isRestored: \(isRestored)")
        debugLog("   - isCancelled: \(isCancelled)")
        debugLog("   - willAutoRenew: \(willAutoRenew)")
        
        if isTrialPeriod {
            // Handle trial conversion (requires productId)
            if let productId = productId {
                stats.convertTrialToPaidSubscription(expiryDate: expiryDate ?? Date(), productId: productId)
            } else {
                debugLog("❌ Trial conversion requires productId")
            }
        } else {
            // Regular subscription update (productId is optional)
            updateSubscription(tier: tier, productId: productId, expiryDate: expiryDate, isCancelled: isCancelled, willAutoRenew: willAutoRenew)
        }
    }
    
    // MARK: - Free Trial Management
    private func handleFreeTrialStarted(_ notification: Notification) async {
        guard let userInfo = notification.userInfo,
              let productId = userInfo["productId"] as? String else {
            return
        }

        // Start free trial locally (always works, even without CloudKit)
        let startDate = userInfo["trialStartDate"] as? Date ?? Date()
        let endDate = userInfo["trialEndDate"] as? Date
        stats.startFreeTrial(startDate: startDate, endDate: endDate, productId: productId)
        saveStats()
        debugLog("✅ Free trial started locally: \(productId)")
        
        // Try to sync to CloudKit (but don't block on this)
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
        
        debugLog("✅ Free trial started for product: \(productId)")
    }
    
    private func handleFreeTrialExpired(_ notification: Notification) async {
        guard let userInfo = notification.userInfo,
              let productId = userInfo["productId"] as? String else {
            return
        }
        
        // End free trial locally
        stats.endFreeTrial()
        saveStats()
        
        // Sync to CloudKit
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
        
        debugLog("⚠️ Free trial expired for product: \(productId)")
    }
    
    func startFreeTrial(productId: String) {
        stats.startFreeTrial(productId: productId)
        saveStats()
        
        // Sync to CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncStatsToCloudKit(userId: userId)
            }
        }
    }
    
    func checkTrialExpiration() {
        // Check if trial has expired and handle accordingly
        if stats.subscriptionInfo.isTrialExpired && stats.subscriptionInfo.isInFreeTrial {
            stats.endFreeTrial()
            saveStats()
            
            // Sync to CloudKit if user is signed in
            if let authService = authService,
               authService.isSignedIn,
               let userId = authService.userIdentifier {
                Task {
                    await syncStatsToCloudKit(userId: userId)
                }
            }
        }
    }
    
    func addFavorite(_ message: JokeMessage) {
        stats.addFavorite(message)
        saveStats()
        
        // Sync favorite to CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await syncFavoriteToCloudKit(message: message, userId: userId)
            }
        }
    }
    
    func removeFavorite(_ message: JokeMessage) {
        stats.favoriteMessages.removeAll { $0.id == message.id }
        saveStats()
        
        // Remove favorite from CloudKit if user is signed in
        if let authService = authService,
           authService.isSignedIn,
           let userId = authService.userIdentifier {
            Task {
                await removeFavoriteFromCloudKit(messageId: message.id, userId: userId)
            }
        }
    }
    
    // MARK: - CloudKit Sync Methods
    func syncWithCloudKit(userId: String) async {
        syncStatus = .syncing
        
        do {
            // Validate subscription status first
            await validateSubscriptionStatus()
            
            let (syncedStats, syncedFavorites) = try await cloudKitService.syncUserData(
                userId: userId,
                localStats: stats,
                localFavorites: stats.favoriteMessages
            )
            
            // Update local data with synced data
            stats = syncedStats
            stats.favoriteMessages = syncedFavorites
            
            // Reset dailyFlirtCount for free users to prevent showing premium usage
            if stats.subscriptionTier == .free && stats.dailyJokeCount > 3 {
                debugLog("📊 Resetting dailyJokeCount for free user (was from premium usage): \(stats.dailyJokeCount) → 0")
                stats.dailyJokeCount = 0
            }
            
            saveStats()
            syncStatus = .synced
            
        } catch {
            debugLog("CloudKit sync failed: \(error)")
            
            // Handle CloudKit specific errors
            if let ckError = error as? CKError {
                switch ckError.code {
                case .quotaExceeded:
                    let retryAfter = ckError.retryAfterSeconds ?? 300
                    syncStatus = .quotaExceeded(retryAfter: retryAfter)
                    
                    // Validate subscription offline when CloudKit is quota exceeded
                    debugLog("📱 CloudKit quota exceeded - validating subscription with StoreKit")
                    await validateSubscriptionWithStoreKit()
                    
                    // Auto-retry after the specified delay
                    Task {
                        try? await Task.sleep(nanoseconds: UInt64(retryAfter * 1_000_000_000))
                        await self.syncWithCloudKit(userId: userId)
                    }
                case .networkUnavailable, .networkFailure:
                    syncStatus = .offline
                    // Also validate subscription offline when network is unavailable
                    await validateSubscriptionWithStoreKit()
                default:
                    syncStatus = .failed(error.localizedDescription)
                    // Validate subscription offline for other CloudKit errors too
                    await validateSubscriptionWithStoreKit()
                }
            } else {
                syncStatus = .failed(error.localizedDescription)
            }
        }
    }
    
    private func syncStatsToCloudKit(userId: String) async {
        do {
            try await cloudKitService.saveUserStats(stats, userId: userId)
        } catch {
            debugLog("Failed to sync stats to CloudKit: \(error)")
            await handleCloudKitError(error, userId: userId, operation: { await self.syncStatsToCloudKit(userId: userId) })
        }
    }
    
    private func syncFavoriteToCloudKit(message: JokeMessage, userId: String) async {
        do {
            try await cloudKitService.saveFavoriteMessage(message, userId: userId)
        } catch {
            debugLog("Failed to sync favorite to CloudKit: \(error)")
            await handleCloudKitError(error, userId: userId, operation: { await self.syncFavoriteToCloudKit(message: message, userId: userId) })
        }
    }
    
    private func removeFavoriteFromCloudKit(messageId: String, userId: String) async {
        do {
            try await cloudKitService.deleteFavoriteMessage(messageId: messageId)
        } catch {
            debugLog("Failed to remove favorite from CloudKit: \(error)")
            await handleCloudKitError(error, userId: userId, operation: { await self.removeFavoriteFromCloudKit(messageId: messageId, userId: userId) })
        }
    }
    
    // MARK: - Local Data Management
    private func checkDailyReset() {
        let calendar = Calendar.current
        let today = Date()
        
        if !calendar.isDate(stats.lastResetDate, inSameDayAs: today) {
            stats.dailyJokeCount = 0
            stats.lastResetDate = today
            saveStats()
        }

        // Also guard against inflated counters when user is on free tier.
        // If local storage still has a premium-era count (> free limit), reset to 0 for a clean slate.
        if stats.subscriptionTier == .free && stats.dailyJokeCount > stats.dailyLimit {
            debugLog("📊 Resetting dailyJokeCount for free user on launch: \(stats.dailyJokeCount) → 0")
            stats.dailyJokeCount = 0
            saveStats()
        }
    }
    
    private func saveStats() {
        if let encoded = try? JSONEncoder().encode(stats) {
            userDefaults.set(encoded, forKey: statsKey)
        }
    }
    
    // MARK: - CloudKit Error Handling
    private func handleCloudKitError(_ error: Error, userId: String, operation: @escaping () async -> Void) async {
        if let ckError = error as? CKError {
            switch ckError.code {
            case .quotaExceeded:
                let retryAfter = ckError.retryAfterSeconds ?? 300
                syncStatus = .quotaExceeded(retryAfter: retryAfter)
                
                // Auto-retry after the specified delay
                Task {
                    try? await Task.sleep(nanoseconds: UInt64(retryAfter * 1_000_000_000))
                    await operation()
                }
            case .networkUnavailable, .networkFailure:
                syncStatus = .offline
            default:
                syncStatus = .failed("Sync failed")
            }
        } else {
            syncStatus = .failed("Sync failed")
        }
    }
    
    // MARK: - Manual Sync
    func forceSyncWithCloudKit() async {
        guard let authService = authService,
              authService.isSignedIn,
              let userId = authService.userIdentifier else {
            syncStatus = .failed("User not signed in")
            return
        }
        
        await syncWithCloudKit(userId: userId)
    }
}
