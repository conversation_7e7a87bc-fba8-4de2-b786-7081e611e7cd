import SwiftUI
import StoreKit

struct SettingsView: View {
    let onClose: () -> Void
    let onManageSubscription: () -> Void
    
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var statsManager: UserStatsManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @State private var showingShareSheet: Bool = false
    @State private var shareItems: [Any] = []
    @State private var showingSignOutAlert = false
    @State private var restoreInProgress = false
    @State private var restoreAlertMessage: String?
    
    private var userStats: UserStats { statsManager.stats }
    
    var body: some View {
        NavigationView {
            List {
                // Account
                Section(header: Text("Account")) {
                    if authService.isSignedIn {
                        // Signed In View
                        HStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(LinearGradient(
                                        colors: [.blue.opacity(0.1), .blue.opacity(0.05)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ))
                                    .frame(width: 40, height: 40)
                                
                                Image(systemName: "person.crop.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.blue)
                            }
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(authService.userName?.isEmpty == false ? authService.userName! : "Joking User")
                                    .font(.body)
                                    .fontWeight(.medium)
                                Text(authService.userEmail?.isEmpty == false ? authService.userEmail! : "Signed in with Apple ID")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "checkmark.seal.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.green)
                        }
                        .padding(.vertical, 4)
                        
                        // Sign Out Button
                        Button {
                            let impact = UIImpactFeedbackGenerator(style: .light)
                            impact.impactOccurred()
                            showingSignOutAlert = true
                        } label: {
                            HStack {
                                Image(systemName: "rectangle.portrait.and.arrow.right")
                                    .foregroundColor(.red)
                                Text("Sign Out")
                                    .foregroundColor(.red)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                    } else {
                        // Not Signed In View
                        HStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(Color.gray.opacity(0.1))
                                    .frame(width: 40, height: 40)
                                
                                Image(systemName: "person.crop.circle")
                                    .font(.system(size: 24))
                                    .foregroundColor(.gray)
                            }
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Guest User")
                                    .font(.body)
                                    .fontWeight(.medium)
                                Text("Sign in to sync your data")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 4)
                        
                        // Sign In Button
                        Button {
                            let impact = UIImpactFeedbackGenerator(style: .medium)
                            impact.impactOccurred()
                            authService.signInWithApple()
                        } label: {
                            HStack {
                                Image(systemName: "applelogo")
                                    .foregroundColor(.primary)
                                Text("Sign in with Apple")
                                    .foregroundColor(.primary)
                                Spacer()
                                if authService.isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(.secondary)
                                        .font(.caption)
                                }
                            }
                        }
                        .disabled(authService.isLoading)
                        
                        // Benefits of signing in
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Benefits of signing in:")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack(spacing: 8) {
                                    Image(systemName: "icloud")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                    Text("Sync favorites across devices")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                HStack(spacing: 8) {
                                    Image(systemName: "chart.bar.fill")
                                        .font(.caption)
                                        .foregroundColor(.green)
                                    Text("Track usage statistics")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                HStack(spacing: 8) {
                                    Image(systemName: "shield.fill")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                    Text("Secure backup of your data")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        .padding(.top, 8)
                        .padding(.bottom, 4)
                    }
                }
                
                // Subscription
                Section(header: Text("Subscription")) {
                    // Current Plan Card - Tappable
                    NavigationLink(destination: PlanDetailsView(
                        userStats: userStats,
                        onUpgrade: { onManageSubscription() },
                        onManageSubscription: { onManageSubscription() }
                    )) {
                        VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            // Plan Badge
                            HStack(spacing: 8) {
                                Image(systemName: userStats.isPremium ? "crown.fill" : "person.circle")
                                    .font(.system(size: 20))
                                    .foregroundStyle(userStats.isPremium ? 
                                        LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                        LinearGradient(colors: [.gray], startPoint: .topLeading, endPoint: .bottomTrailing)
                                    )
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(userStats.subscriptionTier.displayName)
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                    
                                    if userStats.subscriptionInfo.isInActiveFreeTrial {
                                        let daysRemaining = userStats.subscriptionInfo.daysRemainingInTrial
                                        if daysRemaining > 0 {
                                            Text("\(daysRemaining) day\(daysRemaining == 1 ? "" : "s") left in trial")
                                                .font(.caption2)
                                                .foregroundColor(.blue)
                                        } else {
                                            let hoursRemaining = userStats.subscriptionInfo.hoursRemainingInTrial
                                            Text("\(hoursRemaining) hour\(hoursRemaining == 1 ? "" : "s") left in trial")
                                                .font(.caption2)
                                                .foregroundColor(.orange)
                                        }
                                    } else if userStats.isPremium {
                                        // Show subscription type (Weekly/Yearly)
                                        let subscriptionType = subscriptionManager.getActiveSubscriptionType()
                                        Text("\(subscriptionType.type) subscription")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    } else {
                                        Text("\(userStats.dailyJokeCount)/\(userStats.dailyLimit) jokes today")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                            
                            Spacer()
                            
                            let statusText = userStats.subscriptionInfo.subscriptionStatusText
                            let statusColor = userStats.subscriptionInfo.subscriptionStatusColor
                            
                            Text(statusText)
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(statusColor == "gray" ? .secondary : .white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color(statusColor == "green" ? .green : 
                                                 statusColor == "orange" ? .orange :
                                                 statusColor == "blue" ? .blue : 
                                                 Color(.systemGray5)))
                                .cornerRadius(6)
                        }
                        
                        // Subscription Status Details
                        if userStats.subscriptionInfo.isInActiveFreeTrial {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Free Trial Active")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                                
                                if let trialEndDate = userStats.subscriptionInfo.freeTrialEndDate {
                                    Text("Trial ends \(trialEndDate, style: .date)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                                
                                Text("Enjoying all premium features during trial")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        } else if userStats.isPremium && userStats.subscriptionInfo.isCancelled {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Subscription canceled")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.orange)
                                
                                if let expiryDate = subscriptionManager.subscriptionExpiryDate {
                                    Text("You have Premium access until \(expiryDate, style: .date)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                } else {
                                    Text("Auto-renewal has been cancelled")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                                
                            }
                        } else if userStats.isPremium {
                            VStack(alignment: .leading, spacing: 4) {
                                if let expiryDate = subscriptionManager.subscriptionExpiryDate {
                                    let subscriptionType = subscriptionManager.getActiveSubscriptionType()
                                    if userStats.subscriptionInfo.isCancelled {
                                        Text("Premium access until \(expiryDate, style: .date)")
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    } else {
                                        Text("\(subscriptionType.isWeekly ? "Next billing" : "Renews"): \(expiryDate, style: .date)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                } else {
                                    Text("Auto-renewing subscription")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Text("All premium features unlocked")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                    }
                    
                    // Subscription Actions
                    if userStats.isPremium {
                        // Native subscription management for premium users
                        Button {
                            let impact = UIImpactFeedbackGenerator(style: .medium)
                            impact.impactOccurred()
                            subscriptionManager.openSubscriptionManagement()
                        } label: {
                            HStack {
                                Image(systemName: "gearshape.fill")
                                    .foregroundColor(.blue)
                                Text("Manage Subscription")
                                    .foregroundColor(.primary)
                                    .fontWeight(.medium)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                    } else {
                        // Upgrade to premium for free users
                        Button {
                            let impact = UIImpactFeedbackGenerator(style: .medium)
                            impact.impactOccurred()
                            onManageSubscription()
                        } label: {
                            HStack {
                                Image(systemName: "crown.fill")
                                    .foregroundStyle(LinearGradient(
                                        colors: [.yellow, .orange],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ))
                                Text("Upgrade to Premium")
                                    .foregroundColor(.primary)
                                    .fontWeight(.medium)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                    }

                    Button {
                        let impact = UIImpactFeedbackGenerator(style: .light)
                        impact.impactOccurred()
                        restoreInProgress = true
                        Task {
                            let success = await subscriptionManager.restorePurchases()
                            await MainActor.run {
                                restoreInProgress = false
                                if success {
                                    restoreAlertMessage = "Purchases restored successfully."
                                } else {
                                    restoreAlertMessage = subscriptionManager.errorMessage ?? "No previous purchases found for this Apple ID."
                                }
                                subscriptionManager.errorMessage = nil
                            }
                        }
                    } label: {
                        HStack {
                            if restoreInProgress {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .frame(width: 16, height: 16)
                                    .tint(.blue)
                            } else {
                                Image(systemName: "arrow.clockwise.circle")
                                    .foregroundColor(.blue)
                            }
                            Text(restoreInProgress ? "Restoring..." : "Restore Purchases")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    .disabled(restoreInProgress)
                }
                
                // Appearance
                Section(header: Text("Appearance")) {
                    Toggle(isOn: Binding(
                        get: { UserDefaults.standard.object(forKey: "Joking_ShowHeartsOnLaunch") as? Bool ?? true },
                        set: { UserDefaults.standard.set($0, forKey: "Joking_ShowHeartsOnLaunch") }
                    )) {
                        HStack {
                            Image(systemName: "heart.fill")
                                .font(.system(size: 18))
                                .foregroundStyle(LinearGradient(
                                    colors: [.pink, .red], 
                                    startPoint: .topLeading, 
                                    endPoint: .bottomTrailing
                                ))
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Hearts Animation")
                                    .fontWeight(.medium)
                                Text("Display bouncing hearts when launching the app and generating new jokes")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .tint(.pink)
                }
                
                // Environment Info (Development/TestFlight only)
                if subscriptionManager.getEnvironmentInfo().showEnvironmentIndicator {
                    Section(header: Text("Environment")) {
                        let envInfo = subscriptionManager.getEnvironmentInfo()
                        HStack {
                            Image(systemName: envInfo.displayName == "Development" ? "hammer.fill" : "airplane")
                                .foregroundColor(envInfo.displayName == "Development" ? .orange : .blue)
                            VStack(alignment: .leading, spacing: 2) {
                                Text(envInfo.displayName)
                                    .fontWeight(.medium)
                                Text(envInfo.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Text(envInfo.displayName.uppercased())
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(envInfo.displayName == "Development" ? Color.orange : 
                                          envInfo.displayName == "TestFlight" ? Color.blue : Color.green)
                                .cornerRadius(4)
                        }
                        .padding(.vertical, 2)
                    }
                }
                
                // Share
                if let shareURL = AppLinks.shareURL {
                    Section(header: Text("Share")) {
                        Button {
                            shareItems = [
                                "Check out Joking — AI-powered jokes to brighten your day! 😄",
                                shareURL
                            ]
                            showingShareSheet = true
                            let impact = UIImpactFeedbackGenerator(style: .light)
                            impact.impactOccurred()
                        } label: {
                            HStack {
                                Image(systemName: "square.and.arrow.up")
                                Text("Share with Friends")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                    }
                }
                
                // Legal
                Section(header: Text("Legal")) {
                    Link(destination: URL(string: "https://sites.google.com/view/joking-terms-of-use")!) {
                        HStack {
                            Image(systemName: "doc.text")
                            Text("Terms of Use")
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    Link(destination: URL(string: "https://sites.google.com/view/joking-privacy-policy")!) {
                        HStack {
                            Image(systemName: "lock.shield")
                            Text("Privacy Policy")
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                }
                
                // About
                Section(header: Text("About")) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("Version")
                        Spacer()
                        Text(appVersion)
                            .foregroundColor(.secondary)
                    }
                    Button {
                        openSupportEmail()
                        let impact = UIImpactFeedbackGenerator(style: .light)
                        impact.impactOccurred()
                    } label: {
                        HStack {
                            Image(systemName: "questionmark.circle")
                            Text("Support & Contact")
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                }
            }
            .listStyle(.insetGrouped)
            .navigationTitle("Settings")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        let impact = UIImpactFeedbackGenerator(style: .light)
                        impact.impactOccurred()
                        onClose()
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: shareItems)
        }
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task {
                    await authService.signOut()
                }
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will remain synced when you sign back in.")
        }
        .alert("Restore Purchases", isPresented: Binding(
            get: { restoreAlertMessage != nil },
            set: { if !$0 { restoreAlertMessage = nil } }
        )) {
            Button("OK", role: .cancel) { restoreAlertMessage = nil }
        } message: {
            Text(restoreAlertMessage ?? "")
        }
        .onReceive(authService.$errorMessage) { errorMessage in
            if let error = errorMessage {
                // Handle authentication errors
                debugLog("Authentication error: \(error)")
            }
        }
        .onAppear {
            // Check for subscription cancellation status updates when settings view appears
            Task {
                await subscriptionManager.checkAndUpdateCancellationStatus()
            }
        }
    }
    
    private var appVersion: String {
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        return "v\(version) (\(build))"
    }
    
    private func openSupportEmail() {
        let deviceModel = UIDevice.current.model
        let systemName = UIDevice.current.systemName
        let systemVersion = UIDevice.current.systemVersion
        let appVersion = self.appVersion
        let subscriptionStatus = userStats.isPremium ? "Premium" : "Free"
        let userID = authService.userIdentifier ?? "Not signed in"
        
        let emailBody = """


---
Please describe your issue above this line
---

Debug Information:
• App Version: \(appVersion)
• Device: \(deviceModel)
• iOS Version: \(systemName) \(systemVersion)
• Subscription: \(subscriptionStatus)
• User ID: \(userID)
• Daily Jokes Used: \(userStats.dailyJokeCount)/\(userStats.dailyLimit)
• Total Jokes: \(userStats.totalJokesGenerated)
• Signed In: \(authService.isSignedIn ? "Yes" : "No")
"""
        
        var components = URLComponents()
        components.scheme = "mailto"
        components.path = "<EMAIL>"
        components.queryItems = [
            URLQueryItem(name: "subject", value: "Joking Support"),
            URLQueryItem(name: "body", value: emailBody)
        ]
        
        if let emailURL = components.url {
            UIApplication.shared.open(emailURL)
        }
    }
}

#Preview {
    SettingsView(
        onClose: {},
        onManageSubscription: {}
    )
    .environmentObject(AuthenticationService())
    .environmentObject(UserStatsManager())
    .environmentObject(SubscriptionManager.shared)
}
