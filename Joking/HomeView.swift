//
//  HomeView.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI
import Combine

struct HomeView: View {
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var jokeService: JokeService
    @EnvironmentObject var statsManager: UserStatsManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var heartCoordinator: HeartAnimationCoordinator
    @Environment(\.openURL) private var openURL
    
    // Simple direct state variables for instant responsiveness
    @State private var selectedSpiceLevel: SpiceLevel = .medium
    @State private var selectedCategory: JokeCategory? = nil
    @State private var personalContext = ""
    @State private var isPersonalTouchActive = false
    @State private var isGenerating = false
    @State private var currentJoke: JokeMessage?
    @State private var errorMessage: String?
    @State private var showingShareSheet = false
    @State private var showingPremiumSheet = false
    @State private var showingFavorites = false
    @State private var showingSettings = false
    @State private var showPersonalTouchSection = false
    @FocusState private var isPersonalTextFieldFocused: Bool
    @State private var showingAbuseWarning = false
    @State private var abuseWarningMessage = ""
    @State private var showToast = false
    @State private var toastMessage = ""
    @State private var toastIcon = ""
    @State private var toastColor: Color = .green
    @State private var isDarkMode = UserDefaults.standard.bool(forKey: "Joking_IsDarkMode")
    @State private var showingReportDialog = false
    @State private var reportCandidate: JokeMessage?
    @State private var contentHeight: CGFloat = 0
    @State private var screenHeight: CGFloat = 0
    @State private var shouldFloatButton: Bool = false
    
    var body: some View {
        NavigationStack {
            ZStack(alignment: .bottom) {
                mainContentView
                
                // Floating button overlay
                if shouldFloatButton {
                    FloatingButtonContainer(
                        isFloating: true,
                        isLoading: isGenerating,
                        canGenerate: statsManager.stats.canGenerateMore,
                        loadingMessageProvider: jokeService.loadingMessageProvider,
                        action: generateJoke
                    )
                }
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            screenHeight = geometry.size.height
                            updateFloatingButtonState()
                        }
                        .onChange(of: geometry.size) { _, newSize in
                            screenHeight = newSize.height
                            updateFloatingButtonState()
                        }
                }
            )
        }
        .safeAreaInset(edge: .top, spacing: 0) {
            toastOverlay
        }
        .sheet(isPresented: $showingShareSheet) {
            shareSheetView
        }
        .sheet(isPresented: $showingPremiumSheet) {
            premiumSheetView
        }
        .sheet(isPresented: $showingFavorites) {
            favoritesSheetView
        }
        .sheet(isPresented: $showingSettings) {
            settingsSheetView
        }
        .alert("🎉 You're On Fire!", isPresented: $showingAbuseWarning) {
            abuseWarningAlert
        } message: {
            Text(abuseWarningMessage)
        }
        .preferredColorScheme(isDarkMode ? .dark : .light)
    }
    
    private var mainContentView: some View {
        ScrollViewReader { scrollProxy in
            scrollViewContent
                .onChange(of: currentJoke) { _, newJoke in
                    if newJoke != nil {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            scrollProxy.scrollTo("joke-display", anchor: .center)
                        }
                    }
                }
                .onAppear {
                    handleViewAppearance()
                }
        }
    }

    private var scrollViewContent: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 0) {
                NetworkStatusView()
                    .id("top")

                mainControlsSection
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                    .background(
                        ContentSizeReader { size in
                            contentHeight = size.height
                            updateFloatingButtonState()
                        }
                    )

                usageStatsSection
                    .padding(.bottom, shouldFloatButton ? 100 : 20)
            }
        }
        .scrollContentBackground(.hidden)
        .background(Color(UIColor.systemGroupedBackground))
        .scrollDismissesKeyboard(.interactively)
        .simultaneousGesture(
            TapGesture().onEnded {
                if isPersonalTextFieldFocused {
                    hideKeyboard()
                    isPersonalTextFieldFocused = false
                }
            }
        )
        .toolbar {
            ToolbarItemGroup(placement: .keyboard) {
                Spacer()
                Button("Done") {
                    isPersonalTextFieldFocused = false
                }
                .fontWeight(.semibold)
            }
            
            ToolbarItemGroup(placement: .topBarLeading) {
                Image("Joking_logo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 38, height: 38)
            }
            
            ToolbarItemGroup(placement: .topBarTrailing) {
                Button {
                    toggleDarkMode()
                } label: { 
                    Image(systemName: isDarkMode ? "moon.fill" : "sun.max")
                        .foregroundStyle(.secondary)
                        .font(.system(size: 16))
                }
                
                Button {
                    showingFavorites = true
                } label: { 
                    HStack(spacing: 3) {
                        Image(systemName: statsManager.stats.favoriteMessages.isEmpty ? "heart" : "heart.fill")
                            .foregroundColor(statsManager.stats.favoriteMessages.isEmpty ? Color.secondary : Color.pink)
                            .font(.system(size: 16))
                        if !statsManager.stats.allowsFavorites {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 10))
                                .foregroundStyle(LinearGradient(
                                    colors: [.yellow, .orange],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ))
                        }
                    }
                }
                
                Button { 
                    showingSettings = true 
                } label: { 
                    Image(systemName: "gearshape")
                        .foregroundStyle(.secondary)
                        .font(.system(size: 16))
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            if let joke = currentJoke {
                ShareSheet(items: [joke.text])
            }
        }
        .sheet(isPresented: $showingPremiumSheet) {
            PremiumView(onPurchase: {
                statsManager.upgradeToPremium()
                showingPremiumSheet = false
            })
        }
        .sheet(isPresented: $showingFavorites) {
            FavoriteListView(
                favorites: statsManager.stats.favoriteMessages,
                onClose: { showingFavorites = false },
                onUpgradeRequested: {
                    showingFavorites = false
                    showingPremiumSheet = true
                },
                onRemoveFavorite: { message in
                    statsManager.removeFavorite(message)
                }
            )
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView(
                onClose: { showingSettings = false },
                onManageSubscription: { 
                    showingSettings = false
                    showingPremiumSheet = true 
                }
            )
            .environmentObject(authService)
            .environmentObject(statsManager)
            .environmentObject(subscriptionManager)
        }
        .alert("🎉 You're On Fire!", isPresented: $showingAbuseWarning) {
            Button("Got It! 👍", role: .cancel) { }
            Button("Upgrade to Premium") {
                showingPremiumSheet = true
            }
        } message: {
            Text(abuseWarningMessage)
        }
        .confirmationDialog(
            "Report Message?",
            isPresented: $showingReportDialog,
            presenting: reportCandidate
        ) { message in
            Button("Report via Email", role: .destructive) {
                sendReport(for: message)
            }
            Button("Cancel", role: .cancel) {
                reportCandidate = nil
            }
        } message: { message in
            Text("We'll open Mail so you can send this message to our moderation team."
                 + "\n\n\"\(message.text)\"")
        }
        .onChange(of: showingReportDialog) { _, isPresented in
            if !isPresented {
                reportCandidate = nil
            }
        }
        .preferredColorScheme(isDarkMode ? .dark : .light)
    }
    
    // MARK: - Helper Views
    
    private var mainControlsSection: some View {
        VStack(spacing: 14) {
            SpiceLevelSelector(selectedSpiceLevel: $selectedSpiceLevel)
            
            CategorySelector(
                selectedCategory: $selectedCategory,
                isLocked: !statsManager.stats.allowsCategorySelection,
                onUpgradeRequested: {
                    showingPremiumSheet = true
                }
            )
            
            personalTouchSection
            
            errorSection
            
            jokeDisplaySection
                .id("joke-display")
            
            // Show inline generate button only when not floating
            if !shouldFloatButton {
                FloatingButtonContainer(
                    isFloating: false,
                    isLoading: isGenerating,
                    canGenerate: statsManager.stats.canGenerateMore,
                    loadingMessageProvider: jokeService.loadingMessageProvider,
                    action: generateJoke
                )
                .padding(.vertical, 8)
            }
        }
    }
    
    private var personalTouchSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Personal Touch")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                if !statsManager.stats.allowsPersonalTouch {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 10))
                        .foregroundStyle(LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                }
                
                Spacer()
                
                Toggle("", isOn: $showPersonalTouchSection)
                    .toggleStyle(SwitchToggleStyle(tint: .purple))
                    .scaleEffect(0.75)
                    .onChange(of: showPersonalTouchSection) { _, isOn in
                        if !isOn {
                            personalContext = ""
                            isPersonalTouchActive = false
                        }
                        // Allow free users to toggle open/close for preview mode
                        // Upgrade prompt happens on text field tap (already implemented)
                    }
            }
            
            // Expandable content
            if showPersonalTouchSection {
                personalTouchTextInput
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(personalTouchBackground)
    }
    
    private var premiumUpgradePrompt: some View {
        VStack(spacing: 8) {
            Text("Add personal context for unique messages")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Upgrade to Premium") {
                showingPremiumSheet = true
            }
            .font(.subheadline)
            .foregroundColor(.purple)
        }
        .padding()
        .background(Color.purple.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var personalTouchTextInput: some View {
        VStack(spacing: 8) {
            HStack(spacing: 8) {
                TextField("e.g., Their name is Alex, loves music, we met at a concert", text: $personalContext, axis: .vertical)
                    .textFieldStyle(.roundedBorder)
                    .lineLimit(2...3)
                    .focused($isPersonalTextFieldFocused)
                    .submitLabel(.done)
                    .disabled(!statsManager.stats.allowsPersonalTouch)
                    .overlay(
                        // Invisible overlay for free users to capture taps
                        Group {
                            if !statsManager.stats.allowsPersonalTouch {
                                Color.clear
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        showingPremiumSheet = true
                                    }
                            }
                        }
                    )
                    .onChange(of: personalContext) { _, newValue in
                        if newValue.count > 200 {
                            personalContext = String(newValue.prefix(200))
                        }
                        isPersonalTouchActive = !personalContext.isEmpty
                    }
                    .onSubmit {
                        isPersonalTextFieldFocused = false
                    }
                
                if !personalContext.isEmpty {
                    Button(action: {
                        personalContext = ""
                        isPersonalTouchActive = false
                        isPersonalTextFieldFocused = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 16))
                    }
                    .buttonStyle(.plain)
                }
            }
            
            if !personalContext.isEmpty {
                HStack {
                    Spacer()
                    Text("\(personalContext.count)/200")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var personalTouchBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(!statsManager.stats.allowsPersonalTouch ? Color.orange.opacity(0.05) : Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(!statsManager.stats.allowsPersonalTouch ? Color.orange.opacity(0.3) : .gray.opacity(0.2), lineWidth: 1)
            )
    }
    
    // MARK: - Helper Functions
    
    private func updateFloatingButtonState() {
        // Determine if button should float based on content height vs available space
        let threshold: CGFloat = screenHeight * 0.75 // Float when content uses >75% of screen
        let newShouldFloat = contentHeight > threshold && currentJoke != nil
        
        if newShouldFloat != shouldFloatButton {
            withAnimation(.easeInOut(duration: 0.3)) {
                shouldFloatButton = newShouldFloat
            }
        }
    }
    
    private var errorSection: some View {
        Group {
            if let cooldownMessage = jokeService.cooldownMessage {
                CompactErrorView(message: cooldownMessage, isWarning: true) {
                    // Cooldown messages don't dismiss manually
                }
            } else if let error = errorMessage {
                CompactErrorView(message: error) {
                    errorMessage = nil
                }
            }
        }
    }
    
    private var jokeDisplaySection: some View {
        Group {
            if let joke = currentJoke {
                JokeDisplayView(
                    message: joke,
                    onCopy: {
                        statsManager.markMessageAsSeen(joke)
                    },
                    onShare: {
                        showingShareSheet = true
                    },
                    onFavorite: {
                        if let joke = currentJoke {
                            if statsManager.stats.allowsFavorites {
                                statsManager.addFavorite(joke)
                            } else {
                                showingPremiumSheet = true
                            }
                        }
                    },
                    onReport: {
                        reportCandidate = joke
                        showingReportDialog = true
                    },
                    onClear: {
                        currentJoke = nil
                    },
                    onShowToast: { message, icon, color in
                        showToast(message: message, icon: icon, color: color)
                    },
                    messageIsFavorited: statsManager.stats.favoriteMessages.contains(where: { $0.id == joke.id }),
                    canFavorite: statsManager.stats.allowsFavorites
                )
            }
        }
    }
    
    private var toastOverlay: some View {
        Group {
            if showToast {
                ToastView(
                    message: toastMessage,
                    icon: toastIcon,
                    color: toastColor,
                    isShowing: $showToast
                )
                .padding(.top, 8)
                .animation(.easeInOut(duration: 0.2), value: showToast)
            }
        }
    }
    
    private var shareSheetView: some View {
        Group {
            if let joke = currentJoke {
                ShareSheet(items: [joke.text])
            }
        }
    }
    
    private var premiumSheetView: some View {
        PremiumView(onPurchase: {
            statsManager.upgradeToPremium()
            showingPremiumSheet = false
        })
    }
    
    private var favoritesSheetView: some View {
        FavoriteListView(
            favorites: statsManager.stats.favoriteMessages,
            onClose: { showingFavorites = false },
            onUpgradeRequested: {
                showingFavorites = false
                showingPremiumSheet = true
            },
            onRemoveFavorite: { message in
                statsManager.removeFavorite(message)
            }
        )
    }
    
    private var settingsSheetView: some View {
        SettingsView(
            onClose: { showingSettings = false },
            onManageSubscription: { 
                showingSettings = false
                showingPremiumSheet = true 
            }
        )
        .environmentObject(authService)
        .environmentObject(statsManager)
        .environmentObject(subscriptionManager)
    }
    
    private var abuseWarningAlert: some View {
        Group {
            Button("Got It! 👍", role: .cancel) { }
            Button("Upgrade to Premium") {
                showingPremiumSheet = true
            }
        }
    }
    
    private var usageStatsSection: some View {
        Group {
            if !statsManager.stats.isPremium {
                VStack(spacing: 0) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.12))
                        .frame(height: 0.5)
                        .padding(.horizontal, 32)
                        .padding(.vertical, 16)
                    
                    UsageStatsView(
                        stats: statsManager.stats,
                        onUpgrade: {
                            showingPremiumSheet = true
                        }
                    )
                    .padding(.horizontal, 16)
                }
            }
        }
    }
    
    // Simple toast function
    private func showToast(message: String, icon: String, color: Color) {
        toastMessage = message
        toastIcon = icon
        toastColor = color
        showToast = true
        
        // Auto-hide after 2 seconds
        Task {
            try? await Task.sleep(nanoseconds: 2_000_000_000)
            await MainActor.run {
                showToast = false
            }
        }
    }
    
    private func sendReport(for message: JokeMessage) {
        let subject = "Joking Content Report"
        let body = """
        Please review the following generated message:

        "\(message.text)"

        Spice Level: \(message.spiceLevel.rawValue)
        Category: \(message.category?.rawValue ?? "Random")
        Personal Touch: \(message.hasPersonalTouch ? "Yes" : "No")

        Report sent from the iOS app.
        """

        guard let subjectEncoded = subject.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let bodyEncoded = body.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed),
              let url = URL(string: "mailto:<EMAIL>?subject=\(subjectEncoded)&body=\(bodyEncoded)") else {
            showToast(message: "Unable to open Mail.", icon: "exclamationmark.triangle", color: .orange)
            reportCandidate = nil
            return
        }
        openURL(url)
        showToast(message: "Thanks for flagging this message!", icon: "checkmark.seal", color: .green)
        reportCandidate = nil
    }

    // Simple dark mode toggle
    private func toggleDarkMode() {
        isDarkMode.toggle()
        UserDefaults.standard.set(isDarkMode, forKey: "Joking_IsDarkMode")
        
        // Simple haptic feedback
        let impact = UIImpactFeedbackGenerator(style: .light)
        impact.impactOccurred()
    }
    
    // Hide keyboard helper function
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    private func handleViewAppearance() {
        // Force refresh subscription status to sync home page with current status
        Task {
            await subscriptionManager.updateCustomerProductStatus()
        }

        // Trigger hearts animation if enabled
        if UserDefaults.standard.object(forKey: "Joking_ShowHeartsOnLaunch") as? Bool ?? true {
            // Get center position of screen for animation
            let screenBounds = UIScreen.main.bounds
            let centerPosition = CGPoint(x: screenBounds.width / 2, y: screenBounds.height / 2)

            // Delay slightly to ensure view is loaded
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                heartCoordinator.triggerAnimation(from: centerPosition)
            }
        }
    }

    private func generateJoke() {
        // Start loading immediately
        isGenerating = true
        errorMessage = nil
        
        // Start the loading message provider immediately
        let hasPersonalContext = !personalContext.isEmpty && isPersonalTouchActive
        let loadingSource: LoadingSource = hasPersonalContext ? .api : .cache
        jokeService.loadingMessageProvider.startLoading(source: loadingSource)
        
        // Check subscription restrictions
        if !statsManager.stats.canGenerateMore {
            // Show upgrade prompt for daily limit exceeded but keep loading for UI
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isGenerating = false
                jokeService.loadingMessageProvider.stopLoading()
                showingPremiumSheet = true
            }
            return
        }
        
        // Check if user is trying to use Personal Touch without premium
        if isPersonalTouchActive && !statsManager.stats.allowsPersonalTouch {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isGenerating = false
                jokeService.loadingMessageProvider.stopLoading()
                showingPremiumSheet = true
            }
            return
        }
        
        // For Free users, force selectedCategory to nil (random)
        let effectiveCategory = statsManager.stats.allowsCategorySelection ? selectedCategory : nil

        Task {
            var userStats = statsManager.stats

            let joke = await jokeService.generateJoke(
                spiceLevel: selectedSpiceLevel,
                category: effectiveCategory,
                personalContext: hasPersonalContext ? personalContext : nil,
                seenIds: userStats.seenMessageIds,
                userStats: &userStats
            )
            
            // Update stats manager with the modified stats
            statsManager.updateUserStatsAfterGeneration(with: userStats)
            
            // Show abuse warning if needed
            if jokeService.showAbuseWarning {
                abuseWarningMessage = """
                🎉 Wow! You're absolutely crushing it with \(userStats.dailyMessagesGenerated) jokes today!

                You're clearly loving the app! To ensure the best experience for everyone, we'll add a short cooldown after your next batch of jokes.

                Keep laughing, but remember - quality over quantity! 😄
                """
                showingAbuseWarning = true
            }

            if let joke = joke {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentJoke = joke
                }
                statsManager.recordJokeGenerated(joke)
                
                // Auto-clear and close personal touch section after successful generation
                if isPersonalTouchActive {
                    personalContext = ""
                    isPersonalTouchActive = false
                    showPersonalTouchSection = false
                }
            } else {
                if let cooldownMessage = jokeService.cooldownMessage {
                    errorMessage = cooldownMessage
                } else if let serviceError = jokeService.errorMessage {
                    errorMessage = serviceError
                } else {
                    errorMessage = "Failed to generate joke. Please try again."
                }
            }
            
            // Stop loading
            isGenerating = false
            jokeService.loadingMessageProvider.stopLoading()
        }
    }

    private func regenerateJoke() {
        if let joke = currentJoke {
            statsManager.markMessageAsSeen(joke)
        }
        generateJoke()
    }
}

// MARK: - Improved Header View
struct ImprovedAppHeaderView: View {
    var body: some View {
        VStack(spacing: 16) {
            // Heart icon with better styling
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.pink.opacity(0.1), .red.opacity(0.05)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                
                Image(systemName: "heart.text.square.fill")
                    .font(.system(size: 50, weight: .medium))
                    .foregroundColor(Color.pink)
            }
            
            VStack(spacing: 8) {
                Text("Joking")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(Color.pink)
                
                Text("AI-powered jokes to brighten your day")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                
                // Cute heart emoji accent
                Text("💕")
                    .font(.title2)
            }
        }
        .padding(.top, 20)
        .padding(.bottom, 10)
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No update needed
    }
}

#Preview {
    HomeView()
        .environmentObject(AuthenticationService())
        .environmentObject(JokeService())
        .environmentObject(UserStatsManager())
}
