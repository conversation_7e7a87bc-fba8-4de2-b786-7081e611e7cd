//
//  SimpleSplashView.swift
//  Joking
//
//  Created by <PERSON> for Professional iOS Pattern
//

import SwiftUI

/// Simple, quick splash screen - professional iOS standard
struct SimpleSplashView: View {
    var body: some View {
        ZStack {
            // Simple background
            Color(.systemBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // App icon/logo
                Image("Joking_logo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)

                // App name
                Text("Joking")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
        }
    }
}

#Preview {
    SimpleSplashView()
}