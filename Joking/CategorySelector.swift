//
//  CategorySelector.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct CategorySelector: View {
    @Binding var selectedCategory: JokeCategory?
    let isLocked: Bool
    let onUpgradeRequested: () -> Void
    @State private var isCollapsed = false  // Start expanded for better UX
    
    var body: some View {
        VStack(spacing: 12) {
            Button {
                withAnimation(.easeInOut(duration: 0.25)) {
                    isCollapsed.toggle()
                }
            } label: {
                HStack {
                    Text("Choose Your Category")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if isLocked {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 10))
                            .foregroundStyle(LinearGradient(
                                colors: [.yellow, .orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    }
                    
                    Spacer()
                    
                    if isCollapsed {
                        // Show current selection when collapsed
                        HStack(spacing: 4) {
                            if let category = selectedCategory {
                                Text(category.emoji)
                                    .font(.caption)
                                Text(category.rawValue)
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("✨")
                                    .font(.caption)
                                Text("Any")
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 3)
                        .background(
                            Capsule()
                                .fill(.purple.opacity(0.1))
                        )
                    }
                    
                    Image(systemName: isCollapsed ? "chevron.down" : "chevron.up")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)
            
            if !isCollapsed {
                HStack(spacing: 6) {
                // "Any" option
                CategoryOptionCard(
                    title: "Any",
                    emoji: "✨",
                    isSelected: selectedCategory == nil,
                    isLocked: isLocked,
                    action: {
                        if isLocked {
                            onUpgradeRequested()
                        } else {
                            selectedCategory = nil
                            // Don't auto-collapse - let user control it
                        }
                    }
                )
                
                ForEach(JokeCategory.allCases, id: \.self) { category in
                    CategoryOptionCard(
                        title: category.rawValue,
                        emoji: category.emoji,
                        isSelected: selectedCategory == category,
                        isLocked: isLocked,
                        action: {
                            if isLocked {
                                onUpgradeRequested()
                            } else {
                                selectedCategory = category
                                // Don't auto-collapse - let user control it
                            }
                        }
                    )
                }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 12)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .move(edge: .top)),
                    removal: .opacity.combined(with: .move(edge: .top))
                ))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isLocked ? Color.orange.opacity(0.05) : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isLocked ? Color.orange.opacity(0.3) : .gray.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct CategoryOptionCard: View {
    let title: String
    let emoji: String
    let isSelected: Bool
    let isLocked: Bool
    let action: () -> Void
    
    // Performance optimization: Cache colors to avoid repeated calculations
    private let colorCache = CategoryColorCache.shared
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                Text(emoji)
                    .font(.system(size: 24))
                    .scaleEffect(isSelected ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.15), value: isSelected)
                
                Text(title)
                    .font(.system(size: 13))
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isLocked && !isSelected ? .secondary.opacity(0.6) : (isSelected ? .primary : .secondary))
                
                if isSelected {
                    Text(categoryDescription)
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .padding(.horizontal, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? colorCache.backgroundColorFor(title) : (isLocked ? Color.gray.opacity(0.05) : Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? colorCache.borderColorFor(title) : (isLocked ? Color.gray.opacity(0.3) : .gray.opacity(0.3)), lineWidth: isSelected ? 2 : 1)
                    )
            )
            .opacity(isLocked && !isSelected ? 0.6 : 1.0)
        }
        .buttonStyle(.plain)
    }
    
    private var categoryDescription: String {
        return colorCache.descriptionFor(title)
    }
}

// Performance optimization: Singleton color cache for categories
class CategoryColorCache {
    static let shared = CategoryColorCache()
    
    private let backgroundColors: [String: Color]
    private let borderColors: [String: Color]
    private let descriptions: [String: String]
    
    private init() {
        // Pre-compute all colors and descriptions once
        backgroundColors = [
            "Any": .blue.opacity(0.1),
            "Dad Jokes": .green.opacity(0.1),
            "Dark Humor": .black.opacity(0.1),
            "Puns": .purple.opacity(0.1),
            "Pick-up Lines": .pink.opacity(0.1),
            "Geek Jokes": .cyan.opacity(0.1),
            "Work Jokes": .orange.opacity(0.1)
        ]

        borderColors = [
            "Any": .blue,
            "Dad Jokes": .green,
            "Dark Humor": .black,
            "Puns": .purple,
            "Pick-up Lines": .pink,
            "Geek Jokes": .cyan,
            "Work Jokes": .orange
        ]

        descriptions = [
            "Any": "Random",
            "Dad Jokes": "Classic",
            "Dark Humor": "Edgy",
            "Puns": "Wordplay",
            "Pick-up Lines": "Flirty",
            "Geek Jokes": "Nerdy",
            "Work Jokes": "Office"
        ]
    }
    
    func backgroundColorFor(_ category: String) -> Color {
        return backgroundColors[category] ?? .blue.opacity(0.1)
    }

    func borderColorFor(_ category: String) -> Color {
        return borderColors[category] ?? .blue
    }

    func descriptionFor(_ category: String) -> String {
        return descriptions[category] ?? ""
    }
}

#Preview {
    VStack(spacing: 20) {
        CategorySelector(
            selectedCategory: .constant(.dadJokes),
            isLocked: false,
            onUpgradeRequested: {}
        )
        .padding()

        CategorySelector(
            selectedCategory: .constant(nil),
            isLocked: true,
            onUpgradeRequested: {}
        )
        .padding()
    }
}