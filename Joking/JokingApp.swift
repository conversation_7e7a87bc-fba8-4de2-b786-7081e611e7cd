//
//  JokingApp.swift
//  Joking
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI
import CloudKit

@main
struct JokingApp: App {
    // Simple professional pattern - direct @StateObject
    @StateObject private var authService = AuthenticationService()
    @StateObject private var statsManager = UserStatsManager()
    @StateObject private var jokeService = JokeService()
    @StateObject private var heartCoordinator = HeartAnimationCoordinator()
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    // Simple splash state
    @State private var showSplash = true
    
    var body: some Scene {
        WindowGroup {
            if showSplash {
                SimpleSplashView()
                    .task {
                        await showQuickSplash()
                    }
            } else {
                RootView()
                    .environmentObject(authService)
                    .environmentObject(statsManager)
                    .environmentObject(jokeService)
                    .environmentObject(heartCoordinator)
                    .environmentObject(subscriptionManager)
                    .task {
                        // Defer ALL initialization to avoid blocking UI
                        try? await Task.sleep(nanoseconds: 2_000_000_000) // Wait 2 seconds after UI shows
                        
                        // Then initialize in background
                        Task.detached(priority: .background) {
                            if await authService.isSignedIn {
                                await statsManager.setAuthService(authService)
                            }
                        }
                    }
            }
        }
    }
    
    @MainActor
    private func showQuickSplash() async {
        // 1.5 second splash for proper user experience
        try? await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds
        
        withAnimation(.easeOut(duration: 0.3)) {
            showSplash = false
        }
    }
    
    private func checkCloudKitStatus() async {
        do {
            let status = try await CloudKitService.shared.checkAccountStatus()
            debugLog("CloudKit account status: \(status)")
            
            switch status {
            case .available:
                debugLog("CloudKit is available")
            case .noAccount:
                debugLog("No iCloud account")
            case .restricted:
                debugLog("iCloud account is restricted")
            case .couldNotDetermine:
                debugLog("Could not determine iCloud status")
            case .temporarilyUnavailable:
                debugLog("iCloud temporarily unavailable")
            @unknown default:
                debugLog("Unknown iCloud status")
            }
        } catch {
            debugLog("Failed to check CloudKit status: \(error)")
        }
    }
}

struct RootView: View {
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var statsManager: UserStatsManager
    @EnvironmentObject var jokeService: JokeService
    @EnvironmentObject var heartCoordinator: HeartAnimationCoordinator
    
    var body: some View {
        ZStack {
            // Simple, direct app content - no skeleton loading
            Group {
                if authService.isSignedIn {
                    HomeView()
                        .transition(.opacity)
                } else {
                    SignInView(authService: authService)
                        .transition(.opacity)
                }
            }
            .animation(.easeOut(duration: 0.3), value: authService.isSignedIn)
            
            // Global hearts overlay
            GlobalHeartsOverlay()
                .environmentObject(heartCoordinator)
        }
    }
}
