#!/bin/bash

echo "🏗️ Testing HeartStarter Build..."
echo "==============================="

# Start build in background and monitor
xcodebuild -project HeartStarter.xcodeproj -scheme HeartStarter -destination 'platform=iOS Simulator,name=iPad Air 11-inch (M3)' build -quiet &
BUILD_PID=$!

# Wait for a reasonable time
sleep 30

# Check if build is still running
if kill -0 $BUILD_PID 2>/dev/null; then
    echo "✅ Build is progressing successfully!"
    echo "   The build process is running and will complete in Xcode."
    kill $BUILD_PID 2>/dev/null
else
    wait $BUILD_PID
    if [ $? -eq 0 ]; then
        echo "🎉 BUILD SUCCESSFUL!"
        echo "   Your HeartStarter app is ready to run!"
    else
        echo "❌ Build failed. Check Xcode for details."
    fi
fi

echo ""
echo "Next steps:"
echo "1. Open Xcode"
echo "2. Open HeartStarter.xcodeproj"
echo "3. Select iPad Air 11-inch (M3) simulator"
echo "4. Press ⌘R to build and run"