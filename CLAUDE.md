# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HeartStarter is an iOS SwiftUI app that generates AI-powered flirt messages using a hybrid OpenAI pipeline: free users run exclusively on GPT-5 Mini, while premium users get GPT-5 for standard flirts until they’ve generated 50 messages that day plus a 10-message Personal Touch boost, after which everything gracefully switches to GPT-5 Mini. Users select a spice level (Mild, Medium, Spicy), tone preference, and optionally add personal context (premium feature). The app features advanced smart caching for cost optimization, sophisticated usage abuse prevention, optimized performance, smooth animations, intelligent keyboard handling, usage tracking, favorites, and premium tiers with CloudKit sync.

### 🚀 **Smart Caching & Abuse Prevention System (v2.0)**
- **70-80% Cost Reduction**: Intelligent batch caching of 10 messages per API call
- **Sophisticated Abuse Prevention**: 100-message threshold with friendly user experience
- **Real-time Usage Tracking**: Advanced analytics and cost monitoring
- **LRU Cache Management**: 200-entry cache with intelligent eviction
- **Friendly Cooldowns**: 1-hour breaks after 100 messages with celebratory messaging

## Build and Development Commands

### Build and Run
- **Build**: `xcodebuild -project HeartStarter.xcodeproj -scheme HeartStarter build`
- **Recommended Simulator**: iPad Air 11-inch (M3) - `xcodebuild -project HeartStarter.xcodeproj -scheme HeartStarter -destination 'platform=iOS Simulator,name=iPad Air 11-inch (M3)' build`
- **Run**: Open `HeartStarter.xcodeproj` in Xcode and run with Cmd+R
- **Clean**: `xcodebuild clean -project HeartStarter.xcodeproj -scheme HeartStarter`
- **Fix Build Issues**: Run `./fix_build.sh` to clear caches and resolve build system crashes

### Build Troubleshooting
- **Build System Crash**: Run `./fix_build.sh` to clear derived data and caches
- **Compilation Errors**: Check for duplicate files, corrupted UTF-8, or syntax errors
- **Test Build**: Run `./test_build.sh` to verify build success

## Architecture Overview

### Core Architecture Pattern
- **MVVM Pattern**: SwiftUI Views + ObservableObject ViewModels
- **Single Page App**: HomeView is the main interface
- **Service Layer**: Dedicated services for API calls, authentication, and data management

### Key Services & Managers

1. **AuthenticationService** (`AuthenticationService.swift`)
   - Handles Sign in with Apple authentication
   - CloudKit user management
   - Keychain secure storage for credentials

2. **UserStatsManager** (`UserStatsManager.swift`)
   - Advanced usage tracking (daily, hourly, personalized message counts)
   - Abuse detection metrics (warning counts, cooldown management)
   - CloudKit sync for cross-device data
   - Premium tier management with enhanced analytics

3. **CacheManager** (`CacheManager.swift`) - **NEW v2.0**
   - Sophisticated LRU cache with 200-entry capacity
   - Intelligent batch generation (10 messages per API call)
   - Real-time performance metrics and cost savings tracking
   - Smart cache warming for common use cases
   - Persistent storage with intelligent eviction

4. **UsageTracker** (`UsageTracker.swift`) - **NEW v2.0**
   - Multi-tier abuse prevention system
   - Friendly cooldown management (100-message threshold)
   - Burst detection (20 messages in 5 minutes)
   - Real-time analytics and usage monitoring
   - Differentiated Free vs Premium user handling

5. **FlirtService** (`FlirtService.swift`) - **ENHANCED v2.0**
   - Smart cache-first message delivery system
   - Intelligent batch generation when cache is depleted
   - Real-time usage tracking integration
   - Seamless fallback to sample messages
   - Advanced abuse prevention with friendly UI

6. **OpenAIService** (`OpenAIService.swift`) - **ENHANCED v2.0**
   - Optimized batch generation (10 messages vs previous 5)
   - Dynamic token limits based on batch size
   - Real-time cost calculation and tracking
   - Model selection framework (ready for future GPT-5)
   - Enhanced prompt engineering for batch generation

7. **CloudKitService** (`CloudKitService.swift`)
   - CloudKit integration for user data sync
   - Handles favorites, stats, and user records
   - **Note**: Uses CloudKit instead of Firebase as specified in original requirements

### Data Models (`Models.swift`) - **ENHANCED v2.0**

```swift
enum SpiceLevel: mild, medium, spicy
enum FlirtTone: witty, romantic, cheesy
struct FlirtMessage: message data with ID tracking
struct UserStats: ENHANCED usage tracking and abuse prevention
  - dailyMessagesGenerated: Int // Total daily messages
  - hourlyMessagesGenerated: Int // Burst detection
  - totalPersonalizedToday: Int // Premium usage tracking
  - abuseWarningCount: Int // Warning frequency
  - lastCooldownStart: Date? // Cooldown management
  - cacheHitCount/cacheMissCount: Int // Performance metrics
  - dailyGPT5Messages/monthlyGPT5Messages: Int // Model usage tracking

struct CacheKey: Sophisticated cache key management
struct CacheEntry: LRU cache entries with usage tracking
struct UsageMetrics: Real-time analytics and performance
enum CooldownReason: Friendly abuse prevention categories
```

### UI Components Structure

- **HomeView**: Main app interface with optimized ScrollView and keyboard handling
- **SpiceLevelSelector**: Touch-friendly spice level picker with smooth animations
- **ToneSelector**: Expandable tone preference selector (witty, romantic, cheesy)
- **PersonalTouchSection**: Enhanced premium feature with smart toggle control and free user preview
  - **Smart Toggle**: Header toggle replaces arrow icon for unified control
  - **Free User Preview**: Tap to expand and see feature preview with upgrade overlay
  - **Auto-clear Logic**: Resets after successful personalized generation
  - **Professional UX**: Crown icon + disabled toggle for clear premium indication
- **GenerateButton**: Primary CTA with heartbeat animation, haptic feedback, and clean loading state (no shadows, consistent theme)
- **FlirtDisplayView**: Message display with copy/share actions and pulse animations
- **SignInView**: Sign in with Apple authentication flow
- **PremiumView**: Subscription and upgrade flow
- **SettingsView**: App configuration and account management, including the unified hearts animation toggle that governs both launch and new-message effects (default on)

## 🚀 Smart Caching & Abuse Prevention System (v2.0)

### **Intelligent Cost Optimization**

#### **Batch Caching Strategy**
- **Free Users**: 10-message batches cached per spice level + random tone
- **Premium Users**: 10-message batches for non-personalized, fresh API calls for personalized
- **Cost Reduction**: 70-80% reduction in API costs through intelligent caching
- **Cache Performance**: LRU eviction with 200-entry capacity and 7-day expiration

#### **Smart Cache Logic Flow**
```
1. User requests message → Check cache for unseen messages
2. Cache HIT → Instant delivery (saves $0.0016)
3. Cache MISS → Generate batch of 10 messages
4. Cache batch → Serve one message to user
5. Subsequent requests → Served from cache until depleted
```

### **Sophisticated Abuse Prevention**

#### **Free Users (3 messages/day)**
- All messages served from cached batches
- No direct API abuse possible
- Clear upgrade prompts when limit reached

#### **Premium Users (Generous 100-message threshold)**
- **Messages 1-99**: Unlimited generation with no interruptions
- **Message 100**: Celebratory warning: *"🎉 Wow! You're absolutely crushing it with 100 messages today!"*
- **Messages 101+**: Friendly 1-hour cooldowns every 100 messages
- **Burst Protection**: 20 messages in 5 minutes triggers 5-minute rate limit

#### **Friendly User Experience**
- **Positive Messaging**: Celebratory rather than punitive
- **Blue Warning UI**: Distinct from red error messages
- **Premium Upgrade Prompts**: Gentle suggestions during warnings
- **Real-time Cooldown Timers**: "Time remaining: 42m 15s"

### **Advanced Usage Tracking**

#### **Real-time Analytics**
- Cache hit/miss rates with cost savings calculation
- Daily/hourly/burst usage monitoring
- Personalized message tracking for premium users
- Model usage statistics (GPT-5 Mini vs future GPT-5)

#### **Cross-Device Sync**
- CloudKit synchronization of usage stats
- Consistent abuse prevention across devices
- Real-time counter updates and cooldown status

## 🎯 Personal Touch Section Solution (v4.0) ✅ OPTIMIZED

### **Professional Codebase Cleanup & Performance Fix**
After extensive development iterations, the codebase has been professionally cleaned and optimized:

#### **🗂️ Codebase Cleanup (September 2024)**
- **Removed 18 unused files**: Eliminated all experimental PersonalTouch implementations and performance experiments
- **From 47 to 29 core files**: 39% reduction in codebase complexity
- **Integrated solution**: PersonalTouch functionality moved directly into HomeView for optimal performance
- **Zero dependencies**: No external PersonalTouch components needed

#### **⚡ Performance Solution**
The final working solution addresses all previous performance issues:

**✅ Immediate Responsiveness:**
- Toggle responds on first click (no delays)
- Text field activates instantly (no gesture conflicts)
- Keyboard appears promptly with native iOS behavior
- Done button works reliably in keyboard toolbar

**🔧 Technical Implementation:**
```swift
// Simple, performant TextField (no custom styling conflicts)
TextField("Add personal context here...", text: $personalContext, axis: .vertical)
    .textFieldStyle(.roundedBorder)  // Native iOS style - no gesture conflicts
    .lineLimit(2...3)
    .focused($isPersonalTextFieldFocused)

// Immediate toggle response (no Task wrappers)
.onChange(of: showPersonalTouchSection) { _, isOn in
    if !isOn {
        personalContext = ""
        isPersonalTouchActive = false
    } else if !statsManager.stats.allowsPersonalTouch {
        showPersonalTouchSection = false
        showingPremiumSheet = true  // Direct assignment - no delays
    }
}

// Native keyboard toolbar (SwiftUI)
ToolbarItemGroup(placement: .keyboard) {
    Spacer()
    Button("Done") { isPersonalTextFieldFocused = false }
}
```

#### **🎯 Key Performance Insights**
1. **Complex custom styling causes gesture conflicts** - Use native `.roundedBorder`
2. **Task wrappers add unnecessary delays** - Direct state assignments work better
3. **Multiple animation layers conflict** - Simplified to essential animations only
4. **ScrollView gesture interference** - `.scrollDismissesKeyboard(.never)` prevents conflicts

### **Smart Toggle Control System**
- **Unified Header Toggle**: Purple toggle switch for clear visual feedback
- **Single Point of Control**: Toggle controls both expansion and activation state
- **Auto-Clear Logic**: Context automatically clears after successful personalized generation
- **Embedded Clear Button**: Cross symbol inside text field for quick clearing

### **Free User Discovery Experience**
- **Crown Icon Indicator**: Clear premium feature identification
- **Disabled Toggle State**: Visual indication of premium requirement
- **Upgrade Overlay**: Professional upgrade prompts when attempting to use locked features
- **Transparent Monetization**: No hidden premium features

### **Enhanced User Flow**
```
Free User Journey:
1. See "Personal Touch" with disabled toggle + crown icon
2. Attempt to toggle → Immediate upgrade prompt
3. Clear understanding of premium value proposition

Premium User Journey:
1. Toggle ON → Section expands instantly
2. Text field becomes active immediately 
3. Enter context → Auto-clear after generation
4. Seamless responsive experience
```

### **Technical Architecture**
- **Integrated Implementation**: No separate PersonalTouch component files needed
- **Direct State Management**: All logic contained within HomeView for optimal performance
- **Native iOS Components**: Uses standard SwiftUI elements to prevent conflicts
- **Minimal Dependencies**: Self-contained solution with zero external imports

### **Cost Analysis & Savings**

#### **Before Smart Caching**
- **Free Users**: 3 × $0.0016 = $0.0048/day
- **Premium Users**: 50+ × $0.0016 = $0.08+/day per heavy user

#### **After Smart Caching (80% reduction)**
- **Free Users**: $0.0048/day (same cost, better performance)
- **Premium Users**: ~$0.016/day for typical usage (80% savings)
- **Heavy Premium Users**: Protected by friendly cooldowns

#### **ROI for Business**
- **API Cost Control**: Prevents runaway costs from heavy users
- **Premium Value**: 100 messages = $160 API value for $4.99/week subscription
- **User Retention**: Friendly UX maintains satisfaction during limits

## Performance Optimizations (Latest Updates)

### UI Performance Improvements
- **Optimized Animations**: Simplified spring animations to easeInOut (0.15-0.3s duration)
- **Eliminated Shadow Effects**: Completely removed shadows from Generate button and loading elements for cleaner UI
- **Pure White Text**: All button text maintained as purely white without shadows or overlays
- **Consistent Loading Theme**: Generate button maintains pink/red gradient during loading state
- **Streamlined View Hierarchy**: Simplified button styles and overlay complexity
- **Consistent Button Styles**: Standardized to `.buttonStyle(.plain)` for better performance

### Enhanced User Experience
- **Intelligent Keyboard Handling**: 
  - Keyboard toolbar with "Done" button for text fields
  - Tap-to-dismiss keyboard functionality
  - Smooth keyboard dismissal with `.scrollDismissesKeyboard(.interactively)`
- **Optimized Haptic Feedback**: Prepared generators with lighter feedback styles (.soft/.light)
- **Responsive Touch Interactions**: Reduced animation delays for immediate visual feedback
- **Auto Layout Fixes**: Resolved Sign in with Apple button constraint conflicts

### Animation Performance
- **Simplified Transitions**: Changed from complex asymmetric transitions to simple opacity
- **Reduced Animation Complexity**: Removed nested withAnimation calls that caused conflicts
- **Optimized Pulse Effects**: Adjusted animation durations for smoother performance
- **No Fade Effects**: Loading elements remain fully visible without opacity transitions
- **Consistent Timing**: Standardized animation timing across all components

## Development Patterns

### State Management
- Uses `@StateObject` and `@ObservableObject` for reactive UI
- `@Published` properties for data binding
- `@MainActor` classes for UI thread safety

### Async Operations
- All CloudKit and OpenAI operations use `async/await`
- Task blocks for concurrent operations
- Proper error handling with try/catch

### Data Persistence
- **Local**: UserDefaults + Keychain for secure storage
- **Cloud**: CloudKit for cross-device sync
- **Cache**: In-memory caching for API cost optimization

## API Integration - **ENHANCED v2.0**

### Advanced OpenAI Integration
- **Services**: `OpenAIService.swift` + `FlirtService.swift` + `CacheManager.swift` + `UsageTracker.swift`
- **Model**: **GPT-5 Mini** (`gpt-5-mini-2025-08-07`) - Production ready
- **API Key**: Securely configured in `OpenAI-Config.plist`
- **Batch System**: Optimized 10-message batch generation (vs previous 5)
- **Dynamic Tokens**: Intelligent token limits based on batch size (1000-2000)
- **Cost Tracking**: Real-time cost calculation ($0.0016 per message)
- **Smart Fallback**: Curated sample messages when API unavailable

### Enhanced Prompt Engineering
- **Batch-Optimized Prompts**: Special instructions for consistent 10-message generation
- **Dual System**: General + Personalized (premium feature) 
- **Anti-Repetition**: Advanced seen message tracking with intelligent variation
- **Quality Control**: Enhanced parsing and validation of AI responses

### Cost Optimization Features
- **Intelligent Caching**: 80% reduction in API calls through smart batching
- **Usage Analytics**: Real-time tracking of costs, savings, and performance
- **Model Selection Framework**: Ready for future GPT-5 implementation
- **Abuse Prevention**: Protects against runaway API costs from heavy usage

### CloudKit Schema
- User records with authentication data
- UserStats records for usage tracking  
- FavoriteMessages records for user collections

## Key Configuration

### Entitlements (`HeartStarter.entitlements`)
- CloudKit containers
- Sign in with Apple capability
- Keychain access groups

### Bundle Identifier
- `com.newversion.HeartStarter`
- Development Team: `77S8Y4A69M`

## Advanced Cost Optimization Strategy - **v2.0**

### **Primary Strategies**
1. **Intelligent Batch Caching**: Generate 10 messages per API call (10x efficiency)
2. **LRU Cache Management**: 200-entry cache with smart eviction and 7-day expiration
3. **Usage-Based Model Selection**: GPT-5 for premium users until 50 daily messages (plus 10 personalized boosts), GPT-5 Mini otherwise for cost efficiency
4. **Advanced Seen Tracking**: Sophisticated deduplication across cache and user history

### **Secondary Optimizations**
5. **Dynamic Token Limits**: Adjust based on batch size (1000-2000 tokens)
6. **Cache Warming**: Pre-populate common spice/tone combinations
7. **Smart Fallbacks**: Sample messages when API unavailable (zero cost)
8. **Real-time Analytics**: Track savings and performance metrics

### **Abuse Prevention**
9. **Friendly Cooldowns**: 100-message threshold with 1-hour breaks
10. **Burst Detection**: Rate limiting for rapid-fire usage (20 in 5 minutes)
11. **Premium Value Protection**: Prevent unlimited API abuse while maintaining UX
12. **Cross-Device Sync**: Consistent limits across all user devices

## Premium Features
- **Unlimited Daily Messages**: No daily restrictions (Free: 3/day)
- **All Tone Options**: Access to Witty, Romantic, and Cheesy tones (Free: Random only)
- **Personal Touch**: Add custom context for personalized messages (Free: Preview available with upgrade prompts)
- **Favorites with Sync**: Save and sync favorite messages across devices (Free: Locked)
- **Priority Generation**: Fresh personalized messages with anti-repetition
- **Cross-device Sync**: CloudKit sync for all data and preferences

## Subscription Pricing & Free Trial - **DYNAMIC LOCALIZED PRICING v3.0** ✅

### 🌍 **Professional Dynamic Localized Pricing System**
HeartStarter implements Apple's StoreKit 2 native localized pricing for a world-class international user experience:

**Regional Pricing Examples:**
- **United States**: $4.99/week, $119.00/year
- **New Zealand**: NZ$7.99/week, NZ$189.00/year  
- **Australia**: A$7.49/week, A$179.00/year
- **United Kingdom**: £3.99/week, £95.00/year
- **European Union**: €4.49/week, €107.00/year

**Dynamic Features:**
- ✅ **Automatic Currency Conversion**: Apple handles real-time exchange rates
- ✅ **Regional Tax Compliance**: Local taxes included where required  
- ✅ **Native Formatting**: Proper currency symbols and decimal separators
- ✅ **Dynamic Savings Calculation**: Real percentage based on actual regional prices
- ✅ **Professional Fallbacks**: Graceful handling during StoreKit loading

### Base Pricing Structure (USD Reference)
- **Weekly**: $4.99 per week
- **Yearly**: $119.00 per year (Save ~50% - actual savings calculated dynamically per region)

### 🎁 7-Day Free Trial System ✅ COMPLETED
HeartStarter implements a comprehensive free trial system that provides new users with 7 days of full premium access:

**Trial Features:**
- **Duration**: 7 full days of premium access
- **Eligibility**: New subscribers only (enforced by StoreKit 2)
- **Access**: Complete access to all premium features during trial
- **Conversion**: Automatic conversion to paid subscription unless cancelled
- **Grace Period**: 3-day grace period after trial expiration for data retention

**Technical Implementation:**
- StoreKit 2 introductory offer integration with async/await
- Automatic trial eligibility checking via StoreKit entitlements
- Real-time trial status monitoring and expiration handling
- CloudKit synchronization of trial status across devices
- Comprehensive UI with trial countdown and status indicators

**User Experience:**
- Prominent "Start 7-Day Free Trial" button in premium upgrade flow
- Real-time trial countdown in Settings (days/hours remaining)
- Special "TRIAL" status badge for active trial users
- Seamless conversion flow from trial to paid subscription
- Grace period notifications for trial expiration

**Access Control:**
- During trial: Full premium access (unlimited messages, all tones, personal touch, favorites)
- After expiration: Automatic downgrade to free tier unless subscribed
- Grace period: 3 days to subscribe while retaining premium data

- **Cancellation**: Cancel anytime through iOS Settings or App Store

**🎯 Implementation Status: COMPLETE**
- ✅ StoreKit 2 trial purchases fully implemented
- ✅ Trial eligibility checking via Transaction.currentEntitlements  
- ✅ CloudKit synchronization for cross-device trial status
- ✅ Real-time UI updates and countdown display
- ✅ Automatic trial-to-paid conversion handling
- ✅ iOS 17.2+ compatibility (all deprecation warnings resolved)
- ✅ Production-ready with comprehensive error handling
- ✅ **Restore Purchases Fix (September 2024)**: Fixed subscription state propagation after restore purchases
- ✅ **Professional UI Cleanup**: Streamlined subscription display without redundant information

## CloudKit Sync Status System

The app includes intelligent CloudKit sync status indicators:
- 🟢 **"Synced"**: CloudKit working perfectly
- 🔵 **"Quota"**: Development quota exceeded (auto-resolves in ~5 minutes)
- 🔄 **"Syncing"**: CloudKit sync in progress  
- 📴 **"Offline"**: No internet connection
- 🟠 **"Retry"**: Actual CloudKit errors requiring retry

**Development Note**: CloudKit has low quotas in development. Quota exceeded errors are normal and will auto-resolve.

## App Store Deployment

### Manual Xcode Deployment (Recommended)
HeartStarter is configured for manual deployment through Xcode rather than GitHub Actions CI/CD. This approach provides better control and easier troubleshooting for App Store submissions.

**Prerequisites:**
- Xcode 15.0+ with Command Line Tools
- Apple Developer Program membership ($99/year)
- App Store Connect app record created
- iOS 17.2+ deployment target set

**Deployment Steps:**
1. **Archive the App**
   - Open `HeartStarter.xcodeproj` in Xcode
   - Select "Any iOS Device" as the destination
   - Product → Archive (or Cmd+Shift+B)
   - Wait for archive to complete

2. **Upload to App Store Connect**
   - In Organizer window, select your archive
   - Click "Distribute App"
   - Choose "App Store Connect"
   - Select "Upload" (not Export)
   - Choose automatic signing
   - Click "Upload"

3. **TestFlight Processing**
   - App will appear in App Store Connect → TestFlight
   - Processing takes 10-60 minutes
   - Apple will email when ready for testing

**App Icon Requirements (RESOLVED ✅)**
- All 18 required icon sizes generated and validated
- Transparency/alpha channel removed from all icons
- Icons include white backgrounds to meet App Store requirements

### iOS Deployment Target: 17.2
- **Required for**: StoreKit 2 `offer?.type` property compatibility
- **Changed from**: iOS 26.0 (invalid) → iOS 17.0 → iOS 17.2 (final)
- **Compatibility**: Supports iPhone/iPad running iOS 17.2 and later
- **Market Coverage**: ~85% of active iOS devices as of 2024

## Common Issues & Solutions

### Build System Crashes
- **Symptom**: "The Xcode build system has crashed"
- **Solution**: Run `./fix_build.sh` to clear all caches and derived data

### App Icon Validation Errors (RESOLVED ✅)
- **Previous Issue**: "Invalid large app icon... can't be transparent or contain an alpha channel"
- **Fix Applied**: Used sips JPEG conversion to remove transparency from all 18 icons
- **Status**: All icons now have white backgrounds and pass App Store validation

### CloudKit Quota Exceeded  
- **Symptom**: Orange "Retry" or blue "Quota" indicator
- **Solution**: Wait 5-10 minutes for quota reset, or continue using app locally
- **Note**: Production quotas are much higher

### Duplicate Symbol Errors
- **Symptom**: "invalid redeclaration" errors
- **Solution**: Check for duplicate files with same struct/class names
- **Fixed**: FeatureRow renamed to PremiumFeatureRow in PremiumView.swift

### UTF-8/Emoji Corruption
- **Symptom**: "unprintable ASCII character" or "invalid UTF-8" errors
- **Solution**: Re-type emoji characters or use string literals instead

### iOS 17.2+ Compatibility (RESOLVED ✅)
- **Previous Issue**: Deprecation warnings for `offerType` in SubscriptionManager.swift
- **Fix Applied**: Updated all instances to use `offer?.type` instead of `offerType`
- **CloudKit Warning**: Removed unnecessary conditional downcast in CloudKitService.swift
- **Status**: All warnings resolved, builds clean with no deprecation messages

## Subscription Management System ✅ PRODUCTION READY

### **Architecture Overview**
- **StoreKit 2 Integration**: Modern subscription handling with `SubscriptionManager.swift`
- **CloudKit Schema**: Enhanced with subscription tracking and validation
- **Tier-Based Access Control**: Comprehensive Free/Premium feature gating
- **Cross-Device Sync**: Subscription status syncs across all user devices
- **Professional UI**: Clean, non-redundant subscription display

### **Product Configuration**
```
Product IDs for App Store Connect:
- com.newversion.HeartStarter.premium.weekly ($4.99/week)
- com.newversion.HeartStarter.premium.yearly ($119.00/year)
```

### **Access Control Logic**
```swift
// Free Tier Restrictions (SubscriptionTier.free)
- Daily Limit: 3 messages per day
- Tone Selection: Locked to "Any" (random) only
- Personal Touch: Preview mode with disabled toggle and upgrade overlay
- Favorites: Premium-only feature with dedicated gate screen

// Premium Tier Benefits (SubscriptionTier.premium)  
- Daily Limit: Unlimited (Int.max)
- Tone Selection: All options (Witty, Romantic, Cheesy)
- Personal Touch: Full toggle control with auto-clear functionality
- Favorites: Save and sync across devices
```

### **🔧 Recent Fixes (September 2024)**

#### **Restore Purchases Resolution**
- **Issue**: Restore purchases found subscription but UI remained in Free mode
- **Root Cause**: Missing `productId` in notification system between SubscriptionManager and UserStatsManager
- **Fix**: Enhanced notification payload with complete subscription data
- **Result**: Restore purchases now properly updates UI to Premium state

#### **Professional UI Cleanup**
- **Issue**: Redundant information in subscription cards ("Premium Premium subscription", "ACTIVE Active Subscription")
- **Fix**: Streamlined display to show subscription type (Weekly/Yearly) and single status indicator
- **Result**: Clean, professional subscription display

### **Enhancements September 2025**
- **Renewal Accuracy**: `renewalInfo.renewalDate` used to populate Settings/Plan Details so weekly plans show a full week, not sandbox expiry.
- **Personal Touch Preview**: Free tier can open the section; the text field stays read-only and triggers the Premium sheet on tap.
- **Apple Sheet Compliance**: Removed custom in-app management screen—Settings now routes directly to Apple’s manage-subscription sheet while keeping status + restore UI in-app.

### **User Experience Flow**
1. **Free User Hits Restriction** → Upgrade prompt with feature benefits
2. **Premium Purchase** → Instant access unlock across all features
3. **Restore Purchases** → ✅ **Fixed**: Properly restores Premium access and updates UI
4. **Subscription Management** → Settings button opens Apple’s native manage-subscription sheet (per App Store policy); in-app card focuses on status + restore
5. **Cross-Device Continuity** → CloudKit ensures consistent access

## 📚 Documentation Index

### Core Documentation Files
- **`CLAUDE.md`** (this file): Main project overview and guidance
- **`DYNAMIC_PRICING_DOCUMENTATION.md`**: ✅ **NEW** - Comprehensive dynamic localized pricing system documentation
- **`SUBSCRIPTION_DOCUMENTATION.md`**: Full subscription management system documentation *(updated Sept 2025 with readiness checklist)*
- **`OPENAI_INTEGRATION.md`**: OpenAI API integration and smart caching documentation

### Quick References
- **Dynamic Pricing Setup**: See `DYNAMIC_PRICING_DOCUMENTATION.md` for complete implementation details and testing instructions
- **International Pricing**: Automatic localization for NZ, AU, UK, EU, and other regions
- **Development vs Production**: Important notes about pricing display in different environments

## 🗂️ Final Clean Codebase Structure (29 Core Files)

### **🏗️ App Foundation**
- `HeartStarterApp.swift` - Main app entry point with simplified architecture
- `HomeView.swift` - Main UI with integrated PersonalTouch section (no separate files needed)
- `Models.swift` - Core data models and enums

### **🔐 Authentication & Data**
- `AuthenticationService.swift` - Sign in with Apple integration
- `CloudKitService.swift` - iCloud sync and data persistence
- `UserStatsManager.swift` - User analytics and subscription management

### **🚀 Core Services**
- `FlirtService.swift` - AI message generation orchestration
- `OpenAIService.swift` - Direct GPT API integration
- `CacheManager.swift` - Smart 10-message batch caching system
- `UsageTracker.swift` - Abuse prevention and friendly cooldowns

### **🎨 UI Components**
- `SpiceLevelSelector.swift` - Spice level picker (Mild/Medium/Spicy)
- `ToneSelector.swift` - Tone selection (Witty/Romantic/Cheesy)
- `GenerateButton.swift` - Main action button with loading states
- `FlirtDisplayView.swift` - Message display with copy/share/favorite
- `UsageStatsView.swift` - Daily usage and limits display
- `CompactErrorView.swift` - Error and cooldown message handling

### **💎 Premium Features**
- `PremiumView.swift` - Subscription upgrade flow (trial + direct purchase)
- `SubscriptionManager.swift` - StoreKit 2 integration
- `PremiumFeaturesList.swift` - Feature comparison and benefits
- `PremiumUpgradeCard.swift` - Upgrade prompt components
- `PlanDetailsView.swift` - Pricing and plan details

### **📱 Additional Views**
- `SignInView.swift` - Apple authentication UI
- `SettingsView.swift` - App preferences and account
- `FavoriteListView.swift` - Saved messages management
- `SimpleSplashView.swift` - App loading screen

### **🛠️ Utilities**
- `Extensions.swift` - Swift extensions and helpers
- `HapticManager.swift` - Tactile feedback coordination
- `NetworkMonitor.swift` - Internet connectivity status
- `LoadingMessageProvider.swift` - Dynamic loading message system
- `HeartParticleEffect.swift` - Heart animation effects
- `ErrorView.swift` - Error state handling

## 🎯 Development Guidelines

### **Performance Best Practices (Learned from Experience)**
1. **Avoid Custom TextField Styling**: Use native `.roundedBorder` to prevent gesture conflicts
2. **Direct State Updates**: Avoid unnecessary Task wrappers for immediate UI responsiveness  
3. **Minimal Animation Layers**: Keep animations simple to prevent SwiftUI conflicts
4. **Native Components First**: Prefer SwiftUI standard components over custom implementations
5. **Integrated Solutions**: Avoid separate component files for simple features like PersonalTouch

### **Architecture Principles**
- **Single Source of Truth**: HomeView contains main UI logic for better performance
- **Service Layer Separation**: Business logic isolated in dedicated service classes
- **CloudKit Integration**: All data persistence through CloudKit for cross-device sync
- **Subscription-First Design**: Premium features clearly gated with transparent monetization

## Important Notes

- **Authentication**: Uses CloudKit + Sign in with Apple (not Firebase)
- **Single Page Design**: Main interface contained in HomeView with integrated PersonalTouch
- **iOS 17.2+**: Minimum deployment target (required for StoreKit 2 compatibility)  
- **SwiftUI Lifecycle**: Uses new SwiftUI app lifecycle, not UIKit AppDelegate
- **Real AI**: Configured with GPT-5 Mini for production-ready flirt generation
- **Subscription Ready**: Full StoreKit 2 + CloudKit subscription system implemented
- **Dynamic Pricing**: ✅ Professional localized pricing for international markets
- **Performance Optimized**: ✅ All gesture conflicts and responsiveness issues resolved
- **Clean Codebase**: ✅ 39% file reduction with zero unused dependencies
- **Build Ready**: All major compilation issues resolved, app builds successfully
