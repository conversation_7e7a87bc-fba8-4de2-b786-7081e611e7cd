//
//  CloudKitVerification.swift
//  HeartStarter
//
//  Test utility to verify CloudKit schema is set up correctly
//  Run this after setting up your CloudKit schema
//

import Foundation
import CloudKit

struct CloudKitVerification {
    static let shared = CloudKitVerification()
    private let container = CKContainer.default()
    private lazy var database = container.privateCloudDatabase
    
    // MARK: - Schema Verification
    func verifySchema() async {
        print("🔍 Starting CloudKit Schema Verification...")
        
        // Check account status
        await checkAccountStatus()
        
        // Test each record type
        await testRecordType("User", expectedFields: [
            "userId": "String",
            "name": "String", 
            "email": "String",
            "createdDate": "Date",
            "lastLoginDate": "Date",
            "isPremium": "Int64"
        ])
        
        await testRecordType("UserStats", expectedFields: [
            "userId": "String",
            "totalFlirtsGenerated": "Int64",
            "dailyFlirtCount": "Int64", 
            "lastResetDate": "Date",
            "isPremium": "Int64",
            "seenMessageIds": "StringList",
            "lastUpdated": "Date"
        ])
        
        await testRecordType("FlirtMessage", expectedFields: [
            "userId": "String",
            "messageId": "String",
            "text": "String",
            "spiceLevel": "String",
            "tone": "String", 
            "createdAt": "Date",
            "isFavorite": "Int64"
        ])
        
        await testRecordType("Subscription", expectedFields: [
            "userId": "String",
            "productId": "String",
            "transactionId": "String",
            "purchaseDate": "Date",
            "expirationDate": "Date",
            "isActive": "Int64"
        ])
        
        print("✅ CloudKit Schema Verification Complete!")
    }
    
    private func checkAccountStatus() async {
        do {
            let status = try await container.accountStatus()
            switch status {
            case .available:
                print("✅ iCloud Account: Available")
            case .noAccount:
                print("❌ iCloud Account: Not signed in")
            case .restricted:
                print("⚠️  iCloud Account: Restricted")
            case .couldNotDetermine:
                print("❓ iCloud Account: Could not determine")
            case .temporarilyUnavailable:
                print("⏳ iCloud Account: Temporarily unavailable")
            @unknown default:
                print("❓ iCloud Account: Unknown status")
            }
        } catch {
            print("❌ Failed to check account status: \(error)")
        }
    }
    
    private func testRecordType(_ recordType: String, expectedFields: [String: String]) async {
        print("\n📋 Testing \(recordType) record type...")
        
        // Try to create a test record
        let testRecord = CKRecord(recordType: recordType)
        
        // Add test values for each expected field
        for (fieldName, fieldType) in expectedFields {
            switch fieldType {
            case "String":
                testRecord[fieldName] = "test_value" as CKRecordValue
            case "Int64":
                testRecord[fieldName] = 0 as CKRecordValue
            case "Date":
                testRecord[fieldName] = Date() as CKRecordValue
            case "StringList":
                testRecord[fieldName] = ["test"] as CKRecordValue
            default:
                print("⚠️  Unknown field type: \(fieldType)")
            }
        }
        
        do {
            let savedRecord = try await database.save(testRecord)
            print("✅ \(recordType): Schema valid - test record created")
            
            // Clean up - delete the test record
            try await database.deleteRecord(withID: savedRecord.recordID)
            print("🧹 \(recordType): Test record cleaned up")
            
        } catch let error as CKError {
            switch error.code {
            case .unknownItem:
                print("❌ \(recordType): Record type doesn't exist in CloudKit schema")
            case .invalidArguments:
                print("❌ \(recordType): Field mismatch - check field names and types")
            case .permissionFailure:
                print("❌ \(recordType): Permission denied - check security settings")
            default:
                print("❌ \(recordType): CloudKit error - \(error.localizedDescription)")
            }
        } catch {
            print("❌ \(recordType): Unexpected error - \(error)")
        }
    }
}

// MARK: - Usage Example
/*
// Add this to your app's launch to verify CloudKit setup
Task {
    await CloudKitVerification.shared.verifySchema()
}
*/

// MARK: - Manual Test Function
extension CloudKitVerification {
    /// Call this function manually to test CloudKit integration
    func quickTest() async {
        print("🧪 Running Quick CloudKit Test...")
        
        // Test creating a real user record
        let testUserId = "test_user_\(UUID().uuidString)"
        await testUserCreation(userId: testUserId)
        
        // Test creating user stats
        await testUserStatsCreation(userId: testUserId)
        
        // Clean up
        await cleanupTestData(userId: testUserId)
        
        print("🏁 Quick test complete!")
    }
    
    private func testUserCreation(userId: String) async {
        let userRecord = CKRecord(recordType: "User", recordID: CKRecord.ID(recordName: userId))
        userRecord["userId"] = userId as CKRecordValue
        userRecord["name"] = "Test User" as CKRecordValue
        userRecord["createdDate"] = Date() as CKRecordValue
        userRecord["isPremium"] = false as CKRecordValue
        
        do {
            _ = try await database.save(userRecord)
            print("✅ User record creation: Success")
        } catch {
            print("❌ User record creation: Failed - \(error)")
        }
    }
    
    private func testUserStatsCreation(userId: String) async {
        let statsRecord = CKRecord(recordType: "UserStats", recordID: CKRecord.ID(recordName: "stats_\(userId)"))
        statsRecord["userId"] = userId as CKRecordValue
        statsRecord["totalFlirtsGenerated"] = 0 as CKRecordValue
        statsRecord["dailyFlirtCount"] = 0 as CKRecordValue
        statsRecord["lastResetDate"] = Date() as CKRecordValue
        statsRecord["isPremium"] = false as CKRecordValue
        statsRecord["seenMessageIds"] = [] as CKRecordValue
        
        do {
            _ = try await database.save(statsRecord)
            print("✅ UserStats record creation: Success")
        } catch {
            print("❌ UserStats record creation: Failed - \(error)")
        }
    }
    
    private func cleanupTestData(userId: String) async {
        do {
            try await database.deleteRecord(withID: CKRecord.ID(recordName: userId))
            try await database.deleteRecord(withID: CKRecord.ID(recordName: "stats_\(userId)"))
            print("🧹 Test data cleanup: Success")
        } catch {
            print("⚠️  Test data cleanup: \(error)")
        }
    }
}