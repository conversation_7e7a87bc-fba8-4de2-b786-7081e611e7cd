//
//  SettingsView.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI

struct SettingsView: View {
    let userStats: UserStats
    let onClose: () -> Void
    let onManageSubscription: () -> Void
    
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var statsManager: UserStatsManager
    @State private var showingSignOutAlert = false
    @State private var showingDeleteAlert = false
    @State private var isSigningOut = false
    
    var body: some View {
        NavigationView {
            List {
                // User Section
                Section {
                    HStack {
                        Image(systemName: "person.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            if let userName = authService.userName, !userName.isEmpty {
                                Text(userName)
                                    .font(.headline)
                            } else {
                                Text("HeartStarter User")
                                    .font(.headline)
                            }
                            
                            if let userEmail = authService.userEmail, !userEmail.isEmpty {
                                Text(userEmail)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("Signed in with <PERSON>")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        // Sync status indicator
                        Group {
                            switch statsManager.syncStatus {
                            case .syncing:
                                ProgressView()
                                    .scaleEffect(0.8)
                            case .synced:
                                Image(systemName: "checkmark.icloud.fill")
                                    .foregroundColor(.green)
                            case .failed:
                                Image(systemName: "exclamationmark.icloud.fill")
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                // Stats Section
                Section("Usage Statistics") {
                    StatRow(
                        icon: "heart.fill",
                        title: "Total Flirts Generated",
                        value: "\(userStats.totalFlirtsGenerated)"
                    )
                    
                    StatRow(
                        icon: "calendar",
                        title: "Today's Usage",
                        value: "\(userStats.dailyFlirtCount)/\(userStats.dailyLimit)"
                    )
                    
                    StatRow(
                        icon: "star.fill",
                        title: "Favorite Messages",
                        value: "\(userStats.favoriteMessages.count)"
                    )
                    
                    if userStats.isPremium {
                        StatRow(
                            icon: "crown.fill",
                            title: "Premium Status",
                            value: "Active"
                        )
                    }
                }
                
                // Subscription Section
                if !userStats.isPremium {
                    Section {
                        Button {
                            onManageSubscription()
                        } label: {
                            HStack {
                                Image(systemName: "crown.fill")
                                    .foregroundColor(.yellow)
                                Text("Upgrade to Premium")
                                    .foregroundColor(.primary)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                } else {
                    Section {
                        Button {
                            onManageSubscription()
                        } label: {
                            HStack {
                                Image(systemName: "crown.fill")
                                    .foregroundColor(.yellow)
                                Text("Manage Subscription")
                                    .foregroundColor(.primary)
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                // Data & Sync Section
                Section("Data & Sync") {
                    Button {
                        Task {
                            await statsManager.forceSyncWithCloudKit()
                        }
                    } label: {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                                .foregroundColor(.blue)
                            Text("Sync Now")
                                .foregroundColor(.primary)
                            
                            if case .syncing = statsManager.syncStatus {
                                Spacer()
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled({
                        if case .syncing = statsManager.syncStatus {
                            return true
                        }
                        return false
                    }())
                }
                
                // Support Section
                Section("Support") {
                    Link(destination: URL(string: "mailto:<EMAIL>")!) {
                        HStack {
                            Image(systemName: "envelope.fill")
                                .foregroundColor(.green)
                            Text("Contact Support")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Link(destination: URL(string: "https://heartstarter.app/privacy")!) {
                        HStack {
                            Image(systemName: "hand.raised.fill")
                                .foregroundColor(.blue)
                            Text("Privacy Policy")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Link(destination: URL(string: "https://heartstarter.app/terms")!) {
                        HStack {
                            Image(systemName: "doc.text.fill")
                                .foregroundColor(.purple)
                            Text("Terms of Service")
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "arrow.up.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                // Account Actions Section
                Section {
                    Button {
                        showingSignOutAlert = true
                    } label: {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .foregroundColor(.orange)
                            
                            if isSigningOut {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Signing Out...")
                                        .foregroundColor(.orange)
                                }
                            } else {
                                Text("Sign Out")
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                    .disabled(isSigningOut)
                }
                
                // App Info Section
                Section {
                    HStack {
                        Text("Version")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Build")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onClose()
                    }
                }
            }
        }
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                signOut()
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will be safely stored in iCloud and restored when you sign in again.")
        }
    }
    
    private func signOut() {
        isSigningOut = true
        Task {
            await authService.signOut()
            await MainActor.run {
                isSigningOut = false
                onClose()
            }
        }
    }
}

struct StatRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.pink)
                .frame(width: 20)
            
            Text(title)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .foregroundColor(.secondary)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    SettingsView(
        userStats: UserStats(),
        onClose: { },
        onManageSubscription: { }
    )
    .environmentObject(AuthenticationService())
    .environmentObject(UserStatsManager())
}