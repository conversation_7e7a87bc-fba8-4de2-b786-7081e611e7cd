# CloudKit Schema Setup Guide for HeartStarter

This guide will walk you through setting up your CloudKit schema step-by-step. While this cannot be automated, following these instructions will ensure your schema is set up correctly.

## 🚀 Quick Start Checklist

- [ ] Apple Developer Account (paid)
- [ ] Xcode project with CloudKit capability enabled
- [ ] Internet connection
- [ ] 15-20 minutes of your time

## Step 1: Access CloudKit Dashboard

1. Go to [CloudKit Dashboard](https://icloud.developer.apple.com/)
2. Sign in with your Apple Developer account
3. Select your **HeartStarter** container (it should appear automatically if you've enabled CloudKit in Xcode)
   - If you don't see it, make sure you've added CloudKit capability in Xcode and built your project at least once

## Step 2: Select Development Environment

1. Make sure you're in the **Development** environment (you'll see this at the top)
2. Click on **Schema** in the sidebar

## Step 3: Create Record Types

You'll need to create 4 record types. For each record type:

### Record Type 1: User
1. Click **"+"** next to "Record Types"
2. Name it: `User` (case-sensitive)
3. Click **Create**
4. Add these fields:

| Field Name | Field Type | Options |
|------------|------------|---------|
| `userId` | String | ✅ Queryable, ✅ Searchable |
| `name` | String | (no special options) |
| `email` | String | (no special options) |
| `createdDate` | Date/Time | (no special options) |
| `lastLoginDate` | Date/Time | (no special options) |
| `isPremium` | Int(64) | (no special options) |

**How to add fields:**
- Click **"Add Field"** for each field
- Enter the field name exactly as shown
- Select the field type from dropdown
- For `userId`, check both "Queryable" and "Searchable" boxes

### Record Type 2: UserStats
1. Click **"+"** next to "Record Types"
2. Name it: `UserStats` (case-sensitive)
3. Add these fields:

| Field Name | Field Type | Options |
|------------|------------|---------|
| `userId` | String | ✅ Queryable, ✅ Searchable |
| `totalFlirtsGenerated` | Int(64) | (no special options) |
| `dailyFlirtCount` | Int(64) | (no special options) |
| `lastResetDate` | Date/Time | (no special options) |
| `isPremium` | Int(64) | (no special options) |
| `seenMessageIds` | String List | (no special options) |
| `lastUpdated` | Date/Time | (no special options) |

### Record Type 3: FlirtMessage
1. Click **"+"** next to "Record Types"
2. Name it: `FlirtMessage` (case-sensitive)
3. Add these fields:

| Field Name | Field Type | Options |
|------------|------------|---------|
| `userId` | String | ✅ Queryable, ✅ Searchable |
| `messageId` | String | ✅ Queryable |
| `text` | String | (no special options) |
| `spiceLevel` | String | (no special options) |
| `tone` | String | (no special options) |
| `createdAt` | Date/Time | (no special options) |
| `isFavorite` | Int(64) | (no special options) |

### Record Type 4: Subscription
1. Click **"+"** next to "Record Types"
2. Name it: `Subscription` (case-sensitive)
3. Add these fields:

| Field Name | Field Type | Options |
|------------|------------|---------|
| `userId` | String | ✅ Queryable, ✅ Searchable |
| `productId` | String | (no special options) |
| `transactionId` | String | (no special options) |
| `purchaseDate` | Date/Time | (no special options) |
| `expirationDate` | Date/Time | (no special options) |
| `isActive` | Int(64) | (no special options) |

## Step 4: Configure Security

1. Click on **Security Roles** in the sidebar
2. Select **Authenticated Users**
3. Make sure these permissions are set:

### For each Record Type (User, UserStats, FlirtMessage, Subscription):
- **Read**: ✅ Enabled
- **Create**: ✅ Enabled  
- **Write**: ✅ Enabled

**Important**: Make sure "World" permissions are NOT enabled for privacy.

## Step 5: Deploy to Production (Later)

**Don't do this yet!** Only after you've tested your app:

1. Click **Deploy Schema Changes** 
2. Select **Production**
3. Review changes carefully
4. Deploy

## Step 6: Verify Your Setup

After creating the schema, verify it looks like this:

```
📁 Record Types
├── 📄 User (6 fields)
├── 📄 UserStats (7 fields)  
├── 📄 FlirtMessage (7 fields)
└── 📄 Subscription (6 fields)
```

## 🎯 Visual Guide Screenshots

Since I can't provide actual screenshots, here's what you should see:

**Main Dashboard:**
```
Development | HeartStarter Container
├── Schema ← (You'll be here)
├── Data
├── Analytics  
└── Logs
```

**Record Type Creation:**
```
Record Types +
├── User
│   ├── userId (String) 🔍 Queryable, Searchable
│   ├── name (String)
│   ├── email (String)  
│   ├── createdDate (Date/Time)
│   ├── lastLoginDate (Date/Time)
│   └── isPremium (Int64)
```

## ⚠️ Common Mistakes to Avoid

1. **Wrong field names**: Must match exactly (case-sensitive)
2. **Wrong field types**: String vs String List, Int64 vs Int32
3. **Missing indexes**: Don't forget Queryable for userId fields
4. **Wrong environment**: Make sure you're in Development first
5. **Permissions**: Don't enable World permissions

## 🧪 Testing Your Schema

After setup, test in Xcode:

1. Build and run your app
2. Sign in with Apple ID
3. Generate some flirt messages
4. Add favorites
5. Check CloudKit Dashboard → Data tab to see records

## 🚨 Troubleshooting

### "Container not found"
- Ensure CloudKit capability is enabled in Xcode
- Build your project at least once
- Check your team and bundle identifier

### "Schema mismatch" errors
- Verify field names match exactly
- Check field types are correct
- Ensure queryable fields are marked

### "Permission denied"
- Check Security Roles settings
- Ensure authenticated users have proper permissions

## 🎉 You're Done!

Once you see your 4 record types with all fields created, you're ready to test your app! The CloudKit service in your code will automatically use this schema.

## 💡 Pro Tips

1. **Test thoroughly** in Development before deploying to Production
2. **Document changes** - CloudKit schema changes can't be undone
3. **Use CloudKit Console** to monitor your data during development
4. **Start small** - Test with one record type first if you're unsure

## 🆘 Need Help?

If you encounter issues:
1. Double-check field names and types against this guide
2. Verify your Apple Developer account has CloudKit enabled
3. Check Xcode console for specific error messages
4. Test with a simple record creation first

---

**Time Investment**: ~15-20 minutes
**Difficulty**: Beginner-friendly with this guide
**One-time Setup**: Yes, you'll never need to do this again for this app!