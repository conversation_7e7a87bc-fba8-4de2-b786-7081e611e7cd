//
//  HeartStarterApp.swift
//  HeartStarter
//
//  Created by <PERSON> on 19/09/2025.
//

import SwiftUI
import CloudKit

@main
struct HeartStarterApp: App {
    @StateObject private var authService = AuthenticationService()
    @StateObject private var statsManager = UserStatsManager()
    @StateObject private var flirtService = FlirtService()
    
    var body: some Scene {
        WindowGroup {
            RootView()
                .environmentObject(authService)
                .environmentObject(statsManager)
                .environmentObject(flirtService)
                .task {
                    // Setup the connection between auth service and stats manager
                    await MainActor.run {
                        statsManager.setAuthService(authService)
                    }
                    
                    // Check CloudKit availability
                    await checkCloudKitStatus()
                }
        }
    }
    
    private func checkCloudKitStatus() async {
        do {
            let status = try await CloudKitService.shared.checkAccountStatus()
            print("CloudKit account status: \(status)")
            
            switch status {
            case .available:
                print("CloudKit is available")
            case .noAccount:
                print("No iCloud account")
            case .restricted:
                print("iCloud account is restricted")
            case .couldNotDetermine:
                print("Could not determine iCloud status")
            case .temporarilyUnavailable:
                print("iCloud temporarily unavailable")
            @unknown default:
                print("Unknown iCloud status")
            }
        } catch {
            print("Failed to check CloudKit status: \(error)")
        }
    }
}

struct RootView: View {
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var statsManager: UserStatsManager
    
    var body: some View {
        Group {
            if authService.isSignedIn {
                ContentView()
                    .transition(.opacity)
            } else {
                SignInView(authService: authService)
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: authService.isSignedIn)
    }
}