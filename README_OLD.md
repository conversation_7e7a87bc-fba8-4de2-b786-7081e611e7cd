# Joking 😄 - **v1.0 AI-Powered Humor** 🎯

> AI-powered jokes with professional codebase, instant responsiveness & dynamic pricing

HeartStarter is a performance-optimized SwiftUI iOS app that blends OpenAI's GPT-5 and GPT-5 Mini models to generate creative, personalized flirt messages. Premium users get GPT-5 for standard flirts until they’ve generated 50 messages in a day (cached or fresh), plus a 10-message Personal Touch boost, after which everything gracefully runs on GPT-5 Mini for cost efficiency; free users run entirely on GPT-5 Mini. After extensive development and professional cleanup, v4.0 delivers instant responsiveness, a clean 29-file codebase (39% reduction), and enterprise-grade performance. Features advanced smart caching for 70-80% cost reduction, sophisticated abuse prevention with friendly UX, and professional dynamic localized pricing for international markets.

## 🚀 **Major v4.0 Achievements**
- **⚡ Performance Optimized**: Instant responsiveness - no more delayed clicks or keyboard issues
- **🗂️ Professional Cleanup**: From 47 to 29 core files (39% codebase reduction)
- **🎯 Personal Touch v4.0**: Integrated solution with zero gesture conflicts
- **🏗️ Clean Architecture**: Single-page design with optimized SwiftUI performance
- **🌍 Dynamic Localized Pricing**: Professional international pricing system
- **💰 70-80% Cost Reduction**: Intelligent batch caching with LRU management
- **🛡️ Smart Abuse Prevention**: Generous 100-message threshold with friendly cooldowns
- **✅ Production Ready**: All performance issues resolved, builds successfully
- **📅 Subscription Ready** *(September 2025)*: StoreKit 2 upgrade/downgrade/restore flows validated for TestFlight + App Store release.

## ✨ Features

### Core Features - **v4.0 Performance Optimized**
- **🤖 AI-Powered Generation**: Hybrid GPT-5/GPT-5 Mini pipeline (premium GPT-5 until 50 daily messages + 10 Personal Touch boosts, GPT-5 Mini thereafter and for free users) with 70-80% cost optimization
- **🌍 Dynamic Localized Pricing**: Professional international pricing (NZD, AUD, GBP, EUR, etc.)
- **🌶️ Three Spice Levels**: Mild (sweet), Medium (charming), Spicy (bold)
- **🎭 Tone Customization**: Witty, Romantic, or Cheesy styles
- **✨ Personal Touch v4.1**: Preview-first UX for Free users; Premium unlocks full editing
- **🧠 Advanced Smart Caching**: LRU cache with 200-entry capacity + 10-message batching
- **🛡️ Intelligent Abuse Prevention**: 100-message threshold with friendly 1-hour cooldowns
- **🔁 Restore-Friendly**: One-tap restore with real-time status alerts
- **📊 Real-time Analytics**: Usage tracking, cost monitoring, and performance metrics
- **📱 Copy & Share**: Instant clipboard copy with haptic feedback
- **☁️ CloudKit Sync**: Cross-device synchronization with automatic retry
- **🔐 Privacy First**: Sign in with Apple, secure keychain storage

### User Experience - **v4.0 Performance Optimized**
- **⚡ Instant Responsiveness**: Toggle responds on first click, keyboard appears immediately
- **⌨️ Native Keyboard Handling**: SwiftUI toolbar with "Done" button, no gesture conflicts
- **🎯 Responsive Touch**: All UI elements respond instantly with proper haptic feedback
- **🪄 Guided Upgrades**: Manage Subscription opens Apple’s billing sheet; gated features preview before upsell
- **📊 Advanced Usage Tracking**: Daily limits with smart analytics and friendly warnings
- **😊 Friendly Abuse Prevention**: Celebratory messages: *"🎉 You're absolutely crushing it!"*
- **⏰ Smart Cooldowns**: 1-hour breaks with real-time countdown timers
- **💖 Favorites**: Save and organize your best flirt messages
- **💞 Unified Hearts Animation**: Single toggle in Settings controls launch and new-message heart effects (enabled by default)
- **🌙 Dark Mode**: Full dark/light mode support
- **🗂️ Clean Codebase**: Professional architecture with zero unused files
- **📄 Documented Subscription Playbook**: See `SUBSCRIPTION_DOCUMENTATION.md` for upgrade/downgrade/restore procedures

## 📱 Screenshots

*Add your app screenshots here*

## 🚀 Quick Start

### Prerequisites
- Xcode 15.0+
- iOS 17.2+ / macOS 14.0+ (required for StoreKit 2 compatibility)
- OpenAI API key
- Apple Developer Account (for CloudKit & App Store deployment)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd HeartStarter
   ```

2. **Configure OpenAI API**
   - Add your OpenAI API key to `HeartStarter/OpenAI-Config.plist`
   ```xml
   <key>API_KEY</key>
   <string>your-openai-api-key-here</string>
   ```

3. **Open in Xcode**
   ```bash
   open HeartStarter.xcodeproj
   ```

4. **Build and Run**
   - Select iPad Air 11-inch (M3) simulator or your device
   - Press `⌘R` to build and run

### Troubleshooting Build Issues

If you encounter build system crashes or errors:
```bash
./fix_build.sh    # Clear caches and fix common issues
./test_build.sh   # Verify build success
```

## 🏗️ App Architecture

### Clean Architecture - **v4.0 Optimized (29 Core Files)**
```
HeartStarter/
├── 🏗️ App Foundation
│   ├── HeartStarterApp.swift      # Main app entry point with simplified architecture
│   ├── HomeView.swift             # Main UI with integrated PersonalTouch (no separate files)
│   └── Models.swift               # Core data models and enums
├── 🔐 Authentication & Data
│   ├── AuthenticationService.swift # Sign in with Apple integration
│   ├── CloudKitService.swift      # iCloud sync and data persistence
│   └── UserStatsManager.swift     # User analytics and subscription management
├── 🚀 Core Services
│   ├── FlirtService.swift         # AI message generation orchestration
│   ├── OpenAIService.swift        # Direct GPT API integration
│   ├── CacheManager.swift         # Smart 10-message batch caching system
│   └── UsageTracker.swift         # Abuse prevention and friendly cooldowns
├── 🎨 UI Components
│   ├── SpiceLevelSelector.swift   # Spice level picker (Mild/Medium/Spicy)
│   ├── ToneSelector.swift         # Tone selection (Witty/Romantic/Cheesy)
│   ├── GenerateButton.swift       # Main action button with loading states
│   ├── FlirtDisplayView.swift     # Message display with copy/share/favorite
│   ├── UsageStatsView.swift       # Daily usage and limits display
│   └── CompactErrorView.swift     # Error and cooldown message handling
├── 💎 Premium Features
│   ├── PremiumView.swift          # Subscription upgrade + trial flow
│   ├── SubscriptionManager.swift  # StoreKit 2 integration
│   ├── PremiumFeaturesList.swift  # Feature comparison and benefits
│   ├── PremiumUpgradeCard.swift   # Upgrade prompt components
│   └── PlanDetailsView.swift      # Pricing and plan details
├── 📱 Additional Views
│   ├── SignInView.swift           # Apple authentication UI
│   ├── SettingsView.swift         # App preferences and account
│   ├── FavoriteListView.swift     # Saved messages management
│   └── SimpleSplashView.swift     # App loading screen
├── 🛠️ Utilities
│   ├── Extensions.swift           # Swift extensions and helpers
│   ├── HapticManager.swift        # Tactile feedback coordination
│   ├── NetworkMonitor.swift       # Internet connectivity status
│   ├── LoadingMessageProvider.swift # Dynamic loading message system
│   ├── HeartParticleEffect.swift  # Heart animation effects
│   └── ErrorView.swift            # Error state handling
└── 🎯 Configuration
    ├── OpenAI-Config.plist        # API key configuration
    └── HeartStarter.entitlements  # iOS capabilities
```

### Key Architecture Improvements
- **39% File Reduction**: From 47 experimental files to 29 production-ready core files
- **Integrated Personal Touch**: No separate component files needed - built into HomeView
- **Zero Dependencies**: Self-contained solutions with minimal external imports
- **Performance Optimized**: Native SwiftUI components prevent gesture conflicts

### MVVM Pattern
- **Views**: SwiftUI views for UI rendering
- **ViewModels**: `@ObservableObject` classes managing state
- **Models**: Data structures and business logic
- **Services**: API calls, data persistence, authentication

## 🔧 Enhanced Key Services - **v2.0**

### Advanced OpenAI Integration
```swift
// Hybrid GPT-5 / GPT-5 Mini selection with intelligent batch generation
OpenAIService(apiKey: "your-key")
  .generateFlirtMessages(
    spiceLevel: .medium,
    tone: .witty,
    count: 10,              // 🆕 Optimized batch size
    userTier: .premium,     // 🆕 Tier-aware optimization
    useOptimalModel: true   // 🆕 GPT-5 boost for standard flirts until 50 daily messages
  )
```

### Sophisticated Smart Caching - **NEW v2.0**
```swift
// LRU cache with intelligent batch management
CacheManager
  ├── 200-entry LRU cache with 7-day expiration
  ├── 10-message batch generation (vs previous 5)
  ├── Real-time performance metrics & cost tracking
  ├── Intelligent cache warming for common use cases
  └── 80%+ cache hit rate for cost optimization
```

### Multi-Tier Abuse Prevention - **NEW v2.0**
```swift
// Friendly usage monitoring with generous limits
UsageTracker
  ├── 100-message threshold (increased from 50)
  ├── Celebratory warnings: "🎉 You're crushing it!"
  ├── 1-hour friendly cooldowns with countdown timers
  ├── Burst detection (20 messages in 5 minutes)
  └── Cross-device CloudKit sync for consistency
```

### Enhanced CloudKit Sync
```swift
// Advanced cross-device synchronization
UserStatsManager
  ├── Enhanced usage tracking (daily/hourly/personalized)
  ├── Abuse prevention metrics (warnings, cooldowns)
  ├── Cache performance analytics
  ├── Favorites management
  ├── Premium status & trial management
  └── Smart anti-repetition system
```

### Cost Optimization Results
- **70-80% API Cost Reduction**: Through intelligent batch caching
- **Cache Hit Rate**: 80%+ for typical usage patterns  
- **Business Impact**: Sustainable economics even with heavy premium users
- **User Experience**: Faster responses via cache + friendly abuse prevention

## 🎯 Personal Touch Solution v4.0 ✅ Performance Optimized

### Professional Performance Solution
After extensive development and testing, v4.0 delivers a fully optimized Personal Touch implementation:

**✅ Instant Responsiveness Achieved:**
- Toggle responds immediately on first click (no delays)
- Text field activates instantly (no gesture conflicts)
- Keyboard appears promptly with native iOS behavior  
- Done button works reliably in SwiftUI keyboard toolbar

### Technical Implementation - **Final Solution**
```swift
// Optimized for instant responsiveness
PersonalTouchSection (Integrated in HomeView)
  ├── Native .roundedBorder TextField (no custom styling conflicts)
  ├── Direct state updates (no Task wrappers causing delays)
  ├── SwiftUI keyboard toolbar (native "Done" button)
  ├── Embedded clear button (cross symbol in text field)
  └── Minimal animation layers (no complex transitions)
```

### Key Performance Insights Learned
1. **Custom TextField styling causes gesture conflicts** → Use native `.roundedBorder`
2. **Task wrappers add unnecessary delays** → Direct state assignments work better
3. **Complex animations create conflicts** → Simplified to essential animations only
4. **ScrollView gesture interference** → `.scrollDismissesKeyboard(.never)` prevents conflicts

### Enhanced User Experience
**Free Users - Clear Value Proposition:**
- **Crown icon indicator**: Clear premium feature identification
- **Disabled toggle state**: Visual indication of premium requirement
- **Immediate upgrade prompts**: No delays in monetization flow

**Premium Users - Seamless Experience:**
- **Instant toggle response**: Section expands immediately
- **Native text input**: Standard iOS behavior with no conflicts
- **Embedded clear button**: Cross symbol for quick text clearing
- **Auto-clear after generation**: Clean state for next use

### Architecture Benefits
- **Integrated Solution**: No separate PersonalTouch component files needed
- **Zero Dependencies**: Self-contained within HomeView for optimal performance
- **Native Components**: Uses standard SwiftUI elements to prevent conflicts
- **Professional Grade**: Enterprise-level responsiveness and reliability

## 💰 Subscription Tiers

HeartStarter offers a comprehensive subscription system with Free and Premium tiers:

### Free Tier
- **Daily Messages**: 3 flirt messages per day
- **Spice Levels**: Full access to Mild, Medium, and Spicy
- **Tone Options**: Random tone only (system selected)
- **Personal Touch**: ❌ Preview mode only (tap to see upgrade overlay)
- **Favorites**: ❌ Not available
- **CloudKit Sync**: ✅ Basic stats and usage tracking

### Premium Tier - **Enhanced v2.0**
- **Daily Messages**: Generous 100-message threshold before friendly cooldowns
- **Spice Levels**: Full access to all levels
- **Tone Options**: ✅ Witty, Romantic, Cheesy, or Random
- **Personal Touch**: ✅ Full toggle control with auto-clear + custom context (always fresh API calls)
- **Favorites**: ✅ Save unlimited favorite messages with cloud sync
- **Priority Generation**: ✅ Fresh, unique messages with advanced anti-repetition
- **Smart Caching**: ✅ 80% cost reduction for non-personalized messages
- **Friendly Limits**: ✅ Celebratory warnings + 1-hour cooldowns (vs harsh restrictions)
- **Cross-device Sync**: ✅ Full synchronization across all devices

### 🌍 Dynamic Localized Pricing - **NEW v3.0**

HeartStarter implements professional dynamic localized pricing using Apple's StoreKit 2 for a world-class international experience:

**Regional Pricing Examples:**
- **🇺🇸 United States**: $4.99/week, $119.00/year (Save 54%)
- **🇳🇿 New Zealand**: NZ$7.99/week, NZ$189.00/year (Save 55%)
- **🇦🇺 Australia**: A$7.49/week, A$179.00/year (Save 54%)
- **🇬🇧 United Kingdom**: £3.99/week, £95.00/year (Save 54%)
- **🇪🇺 European Union**: €4.49/week, €107.00/year (Save 54%)
- **🇨🇦 Canada**: CA$6.99/week, CA$159.00/year (Save 56%)
- **🇯🇵 Japan**: ¥750/week, ¥17,000/year (Save 56%)

**Key Features:**
- ✅ **Automatic Currency Conversion**: Apple handles real-time exchange rates
- ✅ **Regional Tax Compliance**: Local taxes included where required
- ✅ **Professional Formatting**: Native currency symbols and formatting
- ✅ **Dynamic Savings**: Real-time calculation based on regional pricing
- ✅ **Zero Maintenance**: No manual currency updates needed

**Base Pricing (USD Reference):**
- **Weekly**: $4.99/week
- **Yearly**: $119.00/year (Save ~50%)

### 🎁 7-Day Free Trial ✅ FULLY IMPLEMENTED

HeartStarter offers a comprehensive free trial that gives new users full access to all premium features for 7 days:

#### Trial Benefits
- **Complete Premium Access**: All features unlocked during trial period
  - Unlimited daily flirt messages (vs. 3/day free)
  - All tone options: Witty, Romantic, Cheesy (vs. random only)
  - Personal Touch: Custom context for personalized messages
  - Unlimited Favorites with cross-device sync
  - Priority generation with anti-repetition technology

#### Trial Management
- **Eligibility**: Available to new subscribers only (one per Apple ID)
- **Duration**: 7 full days of premium access
- **Auto-Conversion**: Seamlessly converts to paid subscription unless cancelled
- **Easy Cancellation**: Cancel anytime through iOS Settings → Subscriptions
- **Grace Period**: 3-day grace period after trial expiration for data retention

#### Smart Features
- **Real-Time Countdown**: View remaining trial time in Settings
- **Cross-Device Sync**: Trial status synced across all your devices
- **Intelligent UI**: Special trial indicators and status badges
- **Seamless Experience**: No interruption in service during trial period

#### 🚀 Ready for Production
- **StoreKit 2 Integration**: Modern subscription handling with full iOS 17.2+ compatibility
- **Zero Build Warnings**: Clean compilation with all deprecation warnings resolved
- **App Store Ready**: Production-ready implementation with comprehensive testing
- **CloudKit Sync**: Cross-device trial status synchronization

### Subscription Management ✅ PRODUCTION READY
- **StoreKit 2 Integration**: Seamless transaction handling with modern APIs
- **Restore Purchases Fix**: ✅ **September 2024** - Fixed subscription restoration flow
- **Professional UI**: Clean subscription display without redundant information
- **Intelligent Management**: Easy upgrade/downgrade through Settings
- **Cross-Device Sync**: Automatic subscription restoration across devices
- **Transparent Pricing**: No hidden fees with dynamic localized pricing
- **Smart Trial System**: Intelligent eligibility checking for 7-day free trials

## 🛠️ Development

### Build Commands
```bash
# Standard build
xcodebuild -project HeartStarter.xcodeproj -scheme HeartStarter build

# Build for specific simulator
xcodebuild -project HeartStarter.xcodeproj -scheme HeartStarter \
  -destination 'platform=iOS Simulator,name=iPad Air 11-inch (M3)' build

# Clean build
xcodebuild clean -project HeartStarter.xcodeproj -scheme HeartStarter
```

### CloudKit Sync Status
The app includes intelligent sync status indicators:
- 🟢 **Synced**: CloudKit working perfectly
- 🔵 **Quota**: Development quota exceeded (auto-resolves)
- 🔄 **Syncing**: Sync in progress
- 📴 **Offline**: No internet connection
- 🟠 **Retry**: CloudKit errors requiring retry

> **Note**: Development CloudKit quotas are intentionally low. Quota exceeded errors are normal during testing.

### Common Issues

**Build System Crashes**
```bash
./fix_build.sh  # Clears all caches and derived data
```

**CloudKit Quota Exceeded**
- Wait 5-10 minutes for quota reset
- App works perfectly without CloudKit sync
- Production quotas are much higher

**API Key Issues**
- Verify `OpenAI-Config.plist` contains valid API key
- App falls back to sample messages if key missing

## 🎨 UI/UX Features

### Performance Optimizations
- **Instant Response**: 0.15s animation timing for immediate feedback
- **Optimized Animations**: Simplified easeInOut animations (60fps)
- **Clean UI Design**: Eliminated all shadow effects from Generate button for crisp appearance
- **Pure White Text**: Consistent white text throughout loading and normal states
- **Consistent Loading Theme**: Generate button maintains pink/red gradient during loading
- **No Fade Effects**: Loading elements stay fully visible without opacity transitions
- **Reduced View Complexity**: Streamlined button styles and overlays
- **Efficient Haptics**: Prepared generators with lighter feedback styles

### Enhanced Interaction
- **Intelligent Keyboard**: Toolbar + tap-to-dismiss functionality
- **Smooth Scrolling**: Optimized ScrollView with keyboard awareness
- **Visual Feedback**: Toast notifications with smooth transitions
- **Professional Polish**: Native iOS design patterns with modern touches

## 🔐 Access Control & Security

### Subscription-Based Access Control
HeartStarter implements a comprehensive access control system that gates features based on subscription tiers:

**Free Tier Restrictions:**
- Daily message limit: 3 messages per day (tracked locally and synced via CloudKit)
- Tone selection: Random only (system-selected, user cannot choose)
- Personal Touch: Preview mode with smart toggle + upgrade overlay (tap to explore)
- Favorites: Completely disabled with premium upgrade prompt

**Premium Tier Access:**
- Unlimited daily messages with real-time quota tracking
- Full tone selection: Witty, Romantic, Cheesy, or Random
- Personal Touch: Custom context input for personalized messages
- Unlimited favorites with cross-device CloudKit sync

### Security Implementation

**Authentication & Identity:**
- **Sign in with Apple**: Secure, privacy-focused authentication
- **CloudKit User Records**: Automatic user identity management
- **Keychain Storage**: Encrypted credential storage for API keys

**Data Protection:**
- **Local-First Architecture**: App works offline, syncs when available
- **No Server Storage**: Messages aren't stored on external servers
- **CloudKit Privacy**: Apple's privacy-first cloud platform for user data
- **Secure API Integration**: OpenAI API key stored in encrypted keychain

**Access Validation:**
```swift
// Real-time subscription validation
UserStatsManager.stats.allowsToneSelection // Premium gate for tone picker
UserStatsManager.stats.allowsPersonalTouch // Premium gate for custom context
UserStatsManager.stats.allowsFavorites // Premium gate for favorites feature
UserStatsManager.stats.canGenerateToday // Daily limit enforcement
```

**Subscription Enforcement:**
- StoreKit 2 integration with transaction validation
- Real-time subscription status checking
- Graceful premium gate UI for locked features
- Automatic feature unlock upon successful purchase

## 🚢 App Store Deployment

### Manual Xcode Deployment (Recommended)
HeartStarter is configured for manual deployment through Xcode for optimal control and easier troubleshooting.

**Prerequisites:**
- Xcode 15.0+ with Command Line Tools
- Apple Developer Program membership ($99/year)
- App Store Connect app record created
- iOS 17.2+ deployment target configured

**Deployment Process:**

1. **Archive the App**
   ```
   • Open HeartStarter.xcodeproj in Xcode
   • Select "Any iOS Device" as destination
   • Product → Archive (⌘⇧B)
   • Wait for archive completion
   ```

2. **Upload to App Store Connect**
   ```
   • In Organizer, select your archive
   • Click "Distribute App"
   • Choose "App Store Connect"
   • Select "Upload" (not Export)
   • Use automatic signing
   • Click "Upload"
   ```

3. **TestFlight Processing**
   ```
   • App appears in App Store Connect → TestFlight
   • Processing takes 10-60 minutes
   • Apple emails when ready for testing
   ```

### App Store Requirements ✅ RESOLVED
- **App Icons**: All 18 required sizes generated and validated
- **Transparency**: Alpha channel removed from all icons (white backgrounds)
- **iOS Target**: 17.2+ for StoreKit 2 compatibility
- **Bundle Configuration**: Properly configured with CFBundleIconName

### Required Setup
1. Configure CloudKit container in Apple Developer Portal
2. Enable Sign in with Apple capability  
3. Add OpenAI API key to configuration
4. Test on physical device for full CloudKit functionality
5. Verify app icons meet App Store requirements (✅ completed)
6. (Optional) Set the `HEARTSTARTER_SHARE_URL` environment variable to your published App Store listing before building release binaries so that the Settings share shortcut is exposed only when the real link is available.

## 📄 License

This project is part of a development demonstration. Please respect OpenAI's usage policies and Apple's developer guidelines.

## 🤝 Contributing

This is a demonstration project showcasing AI integration with iOS development best practices.

---

**Made with ❤️ using SwiftUI, GPT-5 & GPT-5 Mini**

*HeartStarter - Because everyone deserves to be charming* ✨
